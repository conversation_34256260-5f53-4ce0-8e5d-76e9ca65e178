import React, { useState } from "react";
import { Upload, message, Spin } from "antd";
import { InboxOutlined, FileTextOutlined } from "@ant-design/icons";
import Select from "react-select";
import axios from "axios";
import Cookies from "js-cookie";

const { Dragger } = Upload;

interface Option {
  value: string;
  label: string;
}

interface PLCExplorationConfig {
  system: string;
  batch_identifier: string;
  phase_identifier: string;
  x_feature: string;
  y_feature: string;
  all_features: string[];
}

interface PLCProps {
  systemName: string;
  plc: PLCExplorationConfig | null;
  onUpdate: (systemName: string, plc: PLCExplorationConfig) => void;
}

const PLCExplorationConfig: React.FC<PLCProps> = ({ systemName, plc, onUpdate }) => {
  const [csvColumns, setCsvColumns] = useState<string[]>([]);
  const [isFileUploaded, setIsFileUploaded] = useState(false);
  const [isExtractingColumns, setIsExtractingColumns] = useState(false);
  const [uploadedFileName, setUploadedFileName] = useState<string>("");
  const [uploadMode, setUploadMode] = useState<'csv' | 'json'>('csv');
  const [isJsonUploading, setIsJsonUploading] = useState(false);
  const [mappingData, setMappingData] = useState({
    batch_identifier: "",
    phase_identifier: "",
    x_feature: "DateTime",
    y_feature: "",
  });

  const handleFileUpload = (file: File) => {
    setIsExtractingColumns(true);
    setUploadedFileName(file.name);
    
    const reader = new FileReader();
    reader.onload = (e) => {
      const text = e.target?.result as string;
      const lines = text.split('\n');
      if (lines.length > 0) {
        const headers = lines[0].split(',').map(header => header.trim().replace(/"/g, ''));
        setCsvColumns(headers);
        setIsFileUploaded(true);
        message.success('CSV file uploaded successfully!');
      }
      setIsExtractingColumns(false);
    };
    reader.onerror = () => {
      message.error('Failed to read CSV file');
      setIsExtractingColumns(false);
    };
    reader.readAsText(file);
    return false; // Prevent default upload behavior
  };

  const handleMappingChange = (field: string, value: string) => {
    setMappingData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleJsonFileUpload = async (file: File) => {
    setIsJsonUploading(true);

    try {
      // Read and validate JSON file
      const fileContent = await file.text();
      let jsonData: any;

      try {
        jsonData = JSON.parse(fileContent);
      } catch (parseError) {
        message.error('Invalid JSON file format');
        setIsJsonUploading(false);
        return false;
      }

      // Validate required keys
      const requiredKeys = ['system', 'batch_identifier', 'phase_identifier', 'x_feature', 'y_feature', 'all_features', 'validation'];
      const missingKeys = requiredKeys.filter(key => !(key in jsonData));

      if (missingKeys.length > 0) {
        message.error(`Missing required keys: ${missingKeys.join(', ')}`);
        setIsJsonUploading(false);
        return false;
      }

      // Validate data types
      if (typeof jsonData.system !== 'string' ||
          typeof jsonData.batch_identifier !== 'string' ||
          typeof jsonData.phase_identifier !== 'string' ||
          typeof jsonData.x_feature !== 'string' ||
          typeof jsonData.y_feature !== 'string' ||
          !Array.isArray(jsonData.all_features)) {
        message.error('Invalid data types in JSON file');
        setIsJsonUploading(false);
        return false;
      }

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('file', file);

      const token = Cookies.get("token");

      // Upload to backend
      const response = await axios.post(`${process.env.REACT_APP_API_BASE_URL}/configurations/upload-json-configuration`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data.status === 200) {
        message.success('JSON configuration uploaded successfully!');

        // Update the component with the uploaded configuration
        const plcConfig: PLCExplorationConfig = {
          system: jsonData.system,
          batch_identifier: jsonData.batch_identifier,
          phase_identifier: jsonData.phase_identifier,
          x_feature: jsonData.x_feature,
          y_feature: jsonData.y_feature,
          all_features: jsonData.all_features
        };

        onUpdate(systemName, response.data.data?.uploadedConfig);
        setUploadedFileName(file.name);
        setIsFileUploaded(true);
      } else {
        message.error('Failed to upload JSON configuration');
      }

    } catch (error: any) {
      console.error('JSON upload error:', error);
      message.error(error.response?.data?.message || 'Failed to upload JSON configuration');
    } finally {
      setIsJsonUploading(false);
    }

    return false; // Prevent default upload behavior
  };

  const handleSaveMapping = () => {
    if (!mappingData.batch_identifier || !mappingData.phase_identifier || !mappingData.y_feature) {
      message.error('Please map all required fields before saving');
      return;
    }

    const plcConfig: PLCExplorationConfig = {
      system: systemName,
      batch_identifier: mappingData.batch_identifier,
      phase_identifier: mappingData.phase_identifier,
      x_feature: mappingData.x_feature,
      y_feature: mappingData.y_feature,
      all_features: csvColumns
    };

    onUpdate(systemName, plcConfig);
    message.success('PLC Exploration Configuration saved!');
  };

  const uploadProps = {
    name: 'file',
    multiple: false,
    accept: uploadMode === 'csv' ? '.csv' : '.json',
    beforeUpload: uploadMode === 'csv' ? handleFileUpload : handleJsonFileUpload,
    showUploadList: false,
  };

  const columnOptions: Option[] = csvColumns.map(col => ({
    value: col,
    label: col
  }));

  return (
    <div className="space-y-8">
      <div className="flex items-center space-x-2 mb-6">
        <span className="bg-blue-600 text-white px-2 py-1 rounded-full text-sm">
          Step 8/8
        </span>
        <h2 className="text-xl font-bold">PLC Exploration Configuration</h2>
      </div>

      {/* Step 1: File Upload */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Step 1: Upload Configuration File</h3>
          <div className="flex items-center space-x-4">
            <label className="text-sm font-medium text-gray-700">Upload Mode:</label>
            <div className="flex space-x-2">
              <button
                onClick={() => setUploadMode('csv')}
                className={`px-3 py-1 rounded text-sm ${
                  uploadMode === 'csv'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                CSV Mapping
              </button>
              <button
                onClick={() => setUploadMode('json')}
                className={`px-3 py-1 rounded text-sm ${
                  uploadMode === 'json'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                JSON Upload
              </button>
            </div>
          </div>
        </div>
        <style>
          {`
            .plc-upload-area .ant-upload.ant-upload-drag {
              border: 2px dashed #bfbfbf !important;
              border-radius: 8px !important;
              background: #fafbfc !important;
              min-height: 120px;
              box-sizing: border-box;
            }
            .plc-upload-area .ant-upload.ant-upload-drag:hover,
            .plc-upload-area .ant-upload.ant-upload-drag-active {
              border-color: #1890ff !important;
              background: #f0f5ff !important;
            }
          `}
        </style>
        {/* Show uploaded file name if file is uploaded */}
        {isFileUploaded && uploadedFileName && (
          <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <span className="text-green-600">✓</span>
              <span className="text-sm font-medium text-green-800">
                File uploaded: {uploadedFileName}
              </span>
            </div>
          </div>
        )}
        <div className="plc-upload-area">
          <Spin
            spinning={uploadMode === 'csv' ? isExtractingColumns : isJsonUploading}
            tip={uploadMode === 'csv' ? "Extracting columns from CSV..." : "Uploading JSON configuration..."}
          >
            <Dragger {...uploadProps}>
              <p className="ant-upload-drag-icon">
                {uploadMode === 'csv' ? <InboxOutlined /> : <FileTextOutlined />}
              </p>
              <p className="ant-upload-text">
                Click or drag {uploadMode === 'csv' ? 'CSV' : 'JSON'} file to this area to upload
              </p>
              <p className="ant-upload-hint">
                {uploadMode === 'csv'
                  ? 'Support for a single CSV file. The file will be used to extract column headers.'
                  : 'Support for a single JSON file with PLC configuration. Must contain: system, batch_identifier, phase_identifier, x_feature, y_feature, all_features, validation keys.'
                }
              </p>
            </Dragger>
          </Spin>
        </div>
      </div>

      {/* Step 2: Column Mapping - Only for CSV mode */}
      {isFileUploaded && uploadMode === 'csv' && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-4">Step 2: Map Columns</h3>
          <div className="bg-gray-50 p-6 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Batch Identifier *
                </label>
                <Select
                  options={columnOptions}
                  value={columnOptions.find(opt => opt.value === mappingData.batch_identifier)}
                  onChange={(selected) => handleMappingChange('batch_identifier', selected?.value || '')}
                  placeholder="Select batch identifier column"
                  className="basic-single-select"
                  classNamePrefix="select"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phase Identifier *
                </label>
                <Select
                  options={columnOptions}
                  value={columnOptions.find(opt => opt.value === mappingData.phase_identifier)}
                  onChange={(selected) => handleMappingChange('phase_identifier', selected?.value || '')}
                  placeholder="Select phase identifier column"
                  className="basic-single-select"
                  classNamePrefix="select"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  X Feature (DateTime)
                </label>
                <Select
                  options={columnOptions}
                  value={columnOptions.find(opt => opt.value === mappingData.x_feature)}
                  onChange={(selected) => handleMappingChange('x_feature', selected?.value || '')}
                  placeholder="Select DateTime column"
                  className="basic-single-select"
                  classNamePrefix="select"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Y Feature *
                </label>
                <Select
                  options={columnOptions}
                  value={columnOptions.find(opt => opt.value === mappingData.y_feature)}
                  onChange={(selected) => handleMappingChange('y_feature', selected?.value || '')}
                  placeholder="Select Y feature column"
                  className="basic-single-select"
                  classNamePrefix="select"
                />
              </div>
            </div>

            <div className="mt-6">
              <h4 className="text-md font-medium mb-3">Available Columns ({csvColumns.length})</h4>
              <div className="bg-white p-4 rounded border max-h-40 overflow-y-auto">
                <div className="flex flex-wrap gap-2">
                  {csvColumns.map((column, index) => (
                    <span
                      key={index}
                      className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm"
                    >
                      {column}
                    </span>
                  ))}
                </div>
              </div>
              <p className="text-sm text-gray-500 mt-2">
                All columns will be included in the configuration
              </p>
            </div>

            <div className="mt-6">
              <button
                onClick={handleSaveMapping}
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
              >
                Save Mapping
              </button>
            </div>
          </div>
        </div>
      )}

      {/* JSON Upload Success Message */}
      {isFileUploaded && uploadMode === 'json' && (
        <div className="mb-8">
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <div className="flex items-center space-x-2 mb-4">
              <span className="text-green-600 text-xl">✓</span>
              <h3 className="text-lg font-semibold text-green-800">JSON Configuration Uploaded Successfully</h3>
            </div>
            <p className="text-sm text-green-700 mb-2">
              File: <span className="font-medium">{uploadedFileName}</span>
            </p>
            <p className="text-sm text-green-600">
              The JSON configuration has been validated and uploaded to S3. The PLC exploration configuration is now ready to use.
            </p>
          </div>
        </div>
      )}

      {/* Current Configuration Display */}
      {plc && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-4">Current Configuration</h3>
          <div className="bg-gray-50 p-4 rounded-lg">
            <pre className="text-sm overflow-x-auto">
              {JSON.stringify(plc, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
};

export default PLCExplorationConfig; 
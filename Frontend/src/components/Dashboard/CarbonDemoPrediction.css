.carbon-demo-prediction {
  background: #f5f5f5;
  min-height: 100vh;
}

.carbon-black-table .ant-table {
  font-size: 13px;
}

.carbon-black-table .ant-table-thead > tr > th {
  background-color: #f0f2f5;
  font-weight: 600;
  text-align: center;
  padding: 12px 16px;
  border-bottom: 2px solid #d9d9d9;
}

.carbon-black-table .ant-table-tbody > tr > td {
  padding: 12px 16px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.carbon-black-table .ant-table-tbody > tr:hover > td {
  background-color: #f9f9f9;
}

.carbon-black-table .ant-table-tbody > tr:nth-child(even) {
  background-color: #fafafa;
}

.carbon-black-table .ant-table-tbody > tr:nth-child(even):hover > td {
  background-color: #f5f5f5;
}

/* Animation for new rows */
.carbon-black-table .ant-table-tbody > tr {
  animation: slideInFromTop 0.6s ease-out;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Status indicators */
.carbon-demo-prediction .ant-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.carbon-demo-prediction .ant-card-head {
  border-bottom: 1px solid #e8e8e8;
}

/* Button styling */
.carbon-demo-prediction .ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

.carbon-demo-prediction .ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
}

.carbon-demo-prediction .ant-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* Responsive design */
@media (max-width: 768px) {
  .carbon-demo-prediction {
    padding: 12px;
  }
  
  .carbon-black-table .ant-table {
    font-size: 12px;
  }
  
  .carbon-black-table .ant-table-thead > tr > th,
  .carbon-black-table .ant-table-tbody > tr > td {
    padding: 8px 12px;
  }
  
  .carbon-demo-prediction .flex {
    flex-direction: column;
    gap: 12px;
  }
  
  .carbon-demo-prediction .flex.items-center {
    align-items: flex-start;
  }
}

/* Loading state */
.carbon-black-table .ant-spin-container {
  min-height: 300px;
}

/* Table pagination */
.carbon-black-table .ant-pagination {
  margin-top: 16px;
  text-align: center;
}

/* Error state */
.carbon-demo-prediction .bg-red-50 {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
}

.carbon-demo-prediction .text-red-600 {
  color: #dc2626;
}

/* Success indicators */
.carbon-demo-prediction .text-green-600 {
  color: #16a34a;
}

.carbon-demo-prediction .text-blue-600 {
  color: #2563eb;
}

/* Typography */
.carbon-demo-prediction .font-mono {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.carbon-demo-prediction .font-semibold {
  font-weight: 600;
}

.carbon-demo-prediction .font-medium {
  font-weight: 500;
}

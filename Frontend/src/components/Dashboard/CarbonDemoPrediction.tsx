import React, { useState, useEffect } from 'react';
import { Table, message, Spin, Card, Button } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { ReloadOutlined, PauseOutlined, PlayCircleOutlined } from '@ant-design/icons';
import { getRequest } from '../../utils/apiHandler';
import './CarbonDemoPrediction.css';

interface CarbonBlackData {
  datetime: string;
  IAN: number;
  batch_id?: string;
  key?: string;
}

const CarbonDemoPrediction: React.FC = () => {
  const [data, setData] = useState<CarbonBlackData[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isAutoRefreshEnabled, setIsAutoRefreshEnabled] = useState<boolean>(true);
  const [intervalId, setIntervalId] = useState<NodeJS.Timeout | null>(null);

  // Define table columns
  const columns: ColumnsType<CarbonBlackData> = [
    {
      title: 'DateTime',
      dataIndex: 'datetime',
      key: 'datetime',
      width: 200,
      render: (text: string) => {
        const date = new Date(text);
        return (
          <span className="text-sm">
            {date.toLocaleString('en-US', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
              hour12: false
            })}
          </span>
        );
      },
    },
    {
      title: 'IAN',
      dataIndex: 'IAN',
      key: 'IAN',
      width: 120,
      render: (value: number) => (
        <span className="text-sm font-semibold text-blue-600">
          {value?.toFixed(4) || '0.00'}
        </span>
      ),
    },
  ];

  // Function to fetch random carbon black data
  const fetchCarbonBlackData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await getRequest('/blends/carbon-random');

      console.log('response?.data?.data', response?.data?.data)
      if (response?.data?.data) {
        const newRecord: CarbonBlackData = {
          ...response.data.data,
          key: `${Date.now()}_${Math.random()}`, // Unique key for each row
        };

        setData(prevData => {
          // Add new record to the end and keep only the last 50 records
          const updatedData = [...prevData, newRecord];
          return updatedData.slice(-50); // Keep the last 50 records
        });
      } else {
        setError('Failed to fetch carbon black data');
        message.error('Failed to fetch carbon black data');
      }
    } catch (error) {
      console.error('Error fetching carbon black data:', error);
      setError('Error fetching carbon black data');
      message.error('Error fetching carbon black data');
    } finally {
      setLoading(false);
    }
  };

  // Function to start auto-refresh
  const startAutoRefresh = () => {
    if (intervalId) {
      clearInterval(intervalId);
    }

    const newIntervalId = setInterval(() => {
      fetchCarbonBlackData();
    }, 60000); // 1 minute = 60000 milliseconds

    setIntervalId(newIntervalId);
  };

  // Function to stop auto-refresh
  const stopAutoRefresh = () => {
    if (intervalId) {
      clearInterval(intervalId);
      setIntervalId(null);
    }
  };

  // Handle auto-refresh toggle
  const handleAutoRefreshToggle = () => {
    const newState = !isAutoRefreshEnabled;
    setIsAutoRefreshEnabled(newState);

    if (newState) {
      startAutoRefresh();
    } else {
      stopAutoRefresh();
    }
  };

  // Manual refresh
  const handleManualRefresh = () => {
    fetchCarbonBlackData();
  };

  // Initial data fetch and setup auto-refresh
  useEffect(() => {
    fetchCarbonBlackData(); // Initial fetch

    if (isAutoRefreshEnabled) {
      startAutoRefresh();
    }

    // Cleanup on unmount
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, []);

  // Handle auto-refresh state changes
  useEffect(() => {
    if (isAutoRefreshEnabled) {
      startAutoRefresh();
    } else {
      stopAutoRefresh();
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [isAutoRefreshEnabled]);

  return (
    <div className="carbon-demo-prediction p-6">
      <Card>
        <div className="mb-4 flex justify-between items-center">
          <div>
            <h3 className="text-lg font-bold mb-2">Prediction Data</h3>
            {/* <p className="text-sm text-gray-600">
              Real-time IAN values from CB_batches_formatted.csv • Updates every 1 minute
            </p> */}
          </div>

          {/* <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <Button
                type={isAutoRefreshEnabled ? "default" : "primary"}
                icon={isAutoRefreshEnabled ? <PauseOutlined /> : <PlayCircleOutlined />}
                onClick={handleAutoRefreshToggle}
                size="small"
              >
                {isAutoRefreshEnabled ? 'Pause' : 'Start'} Auto-refresh
              </Button>

              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={handleManualRefresh}
                size="small"
                loading={loading}
              >
                Manual Refresh
              </Button>
            </div>

            {loading && <Spin size="small" />}

            <div className="text-sm text-gray-500">
              Status: {isAutoRefreshEnabled ?
                <span className="text-green-600 font-medium">Auto-refresh ON</span> :
                <span className="text-red-600 font-medium">Auto-refresh OFF</span>
              }
            </div>
          </div> */}
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        <div className="overflow-x-auto">
          <Table
            columns={columns}
            dataSource={data}
            pagination={false}
            scroll={{ y: 400 }}
            size="small"
            bordered
            className="carbon-black-table"
            loading={loading && data.length === 0}
            locale={{
              emptyText: loading ? 'Loading...' : 'No data available'
            }}
          />
        </div>

        {/* <div className="mt-4 flex justify-between items-center text-xs text-gray-500">
          <span>Total records: {data.length}</span>
          <span>Last updated: {data.length > 0 ? new Date(data[data.length - 1].datetime).toLocaleTimeString() : 'Never'}</span>
        </div> */}
      </Card>
    </div>
  );
};

export default CarbonDemoPrediction;

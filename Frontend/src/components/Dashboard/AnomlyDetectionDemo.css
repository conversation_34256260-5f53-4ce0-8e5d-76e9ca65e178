.anomaly-detection-demo {
  background: #f5f5f5;
  min-height: 100vh;
}

.anomaly-table-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 16px 0;
}

.anomaly-table .ant-table {
  font-size: 13px;
}

.anomaly-table .ant-table-thead > tr > th {
  background-color: #f8f9fa;
  font-weight: 600;
  text-align: center;
  padding: 12px 16px;
  border-bottom: 2px solid #e8e8e8;
  color: #333;
}

.anomaly-table .ant-table-tbody > tr > td {
  padding: 12px 16px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: middle;
}

/* Normal row styling */
.anomaly-table .normal-row {
  background-color: white;
  transition: background-color 0.3s ease;
}

.anomaly-table .normal-row:hover > td {
  background-color: #f9f9f9;
}

/* Anomalous row styling - red highlighting */
.anomaly-table .anomaly-row {
  background-color: #fef2f2 !important;
  border-left: 4px solid #ef4444;
  transition: background-color 0.3s ease;
}

.anomaly-table .anomaly-row:hover > td {
  background-color: #fee2e2 !important;
}

.anomaly-table .anomaly-row > td {
  background-color: #fef2f2 !important;
  border-bottom: 1px solid #fecaca;
}

/* Animation for new rows */
.anomaly-table .ant-table-tbody > tr {
  animation: slideInFromTop 0.5s ease-out;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Score badge styling */
.anomaly-table .ant-table-tbody .bg-red-100 {
  background-color: #fee2e2 !important;
  color: #dc2626 !important;
  border: 1px solid #fca5a5 !important;
  font-weight: 600;
  animation: pulse 2s infinite;
}

.anomaly-table .ant-table-tbody .bg-green-100 {
  background-color: #dcfce7 !important;
  color: #16a34a !important;
  border: 1px solid #86efac !important;
  font-weight: 500;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Fixed columns styling */
.anomaly-table .ant-table-fixed-left {
  box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
}

.anomaly-table .ant-table-thead > tr > th.ant-table-cell-fix-left {
  background-color: #f8f9fa;
}

.anomaly-table .ant-table-tbody > tr > td.ant-table-cell-fix-left {
  background-color: white;
}

.anomaly-table .anomaly-row > td.ant-table-cell-fix-left {
  background-color: #fef2f2 !important;
}

/* Loading state */
.anomaly-table .ant-spin-container {
  min-height: 300px;
}

/* Responsive design */
@media (max-width: 768px) {
  .anomaly-table-container {
    padding: 16px;
    margin: 12px 0;
  }
  
  .anomaly-table .ant-table {
    font-size: 12px;
  }
  
  .anomaly-table .ant-table-thead > tr > th,
  .anomaly-table .ant-table-tbody > tr > td {
    padding: 8px 12px;
  }
}

/* Status indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.status-indicator .indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-indicator .normal-dot {
  background-color: #10b981;
}

.status-indicator .anomaly-dot {
  background-color: #ef4444;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

/* Header styling */
.anomaly-detection-demo h2 {
  color: #1f2937;
  margin-bottom: 8px;
}

.anomaly-detection-demo p {
  color: #6b7280;
  line-height: 1.5;
}

/* Table scroll styling */
.anomaly-table .ant-table-body {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.anomaly-table .ant-table-body::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.anomaly-table .ant-table-body::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 4px;
}

.anomaly-table .ant-table-body::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 4px;
}

.anomaly-table .ant-table-body::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Empty state styling */
.anomaly-table .ant-empty {
  padding: 40px 20px;
}

.anomaly-table .ant-empty-description {
  color: #6b7280;
}

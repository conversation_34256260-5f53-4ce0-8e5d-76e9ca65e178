import React, { useState, useRef, useEffect } from 'react';
import { Button, Progress, message, Card, Space, Typography, Row, Col } from 'antd';
import {
  FileOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  DeleteOutlined,
  UploadOutlined
} from '@ant-design/icons';
import { getRequest, postRequest } from '../utils/apiHandler';
import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';
import { useSelector } from 'react-redux';

const { Text } = Typography;

interface FileUploadItem {
  id: string;
  file: File;
  path: string;
  dropdownValue: string;
  progress: number;
  speed: number;
  status: 'idle' | 'uploading' | 'completed' | 'error';
  error: string | null;
  uploadId: string | null;
  key: string | null;
}

interface MultiFileUploadState {
  files: FileUploadItem[];
  isUploading: boolean;
}

const FileUploader: React.FC = () => {
  const [uploadState, setUploadState] = useState<MultiFileUploadState>({
    files: [],
    isUploading: false,
  });

  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const abortControllersRef = useRef<Map<string, AbortController>>(new Map());
  const [systemDetails, setSystemDetails] = useState<any>(null);

  const selectSystems = useSelector((state: any) => state.systems.systems);
  console.log('selectSystems', selectSystems)

  useEffect(() => {
    if (selectSystems && selectSystems.length > 0) {
      let systemId = selectSystems[0].systems[0]?.systemId;
      console.log('systemId', systemId)
      getSystemDetails(systemId)
    }
  },[selectSystems])

  const getSystemDetails = async (systemId: string) => {
    try {
      const response = await getRequest(`/auth/system-details/${systemId}`);
      console.log('response.data.data', response.data.data)
      if(response.data.data){
        setSystemDetails(response.data.data)
      }else{
        setSystemDetails(null)
      }

    } catch (error) {
      console.error('Error fetching system details:', error);
      return null;
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatSpeed = (bytesPerSecond: number): string => {
    return formatFileSize(bytesPerSecond) + '/s';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFilesSelect(files);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFilesSelect(Array.from(files));
    }
  };

  const handleFilesSelect = (files: File[]) => {
    // Limit to maximum 2 files total
    const remainingSlots = 2 - uploadState.files.length;
    if (remainingSlots <= 0) {
      message.error('Maximum 2 files allowed. Please remove existing files first.');
      return;
    }

    const filesToAdd = files.slice(0, remainingSlots);
    if (files.length > remainingSlots) {
      message.warning(`Only ${remainingSlots} file(s) can be added. Maximum 2 files allowed.`);
    }

    const newFiles: FileUploadItem[] = filesToAdd.map((file, index) => {
      if (file.size > 2 * 1024 * 1024 * 1024) { // 2GB limit
        message.error(`File ${file.name} exceeds 2GB limit`);
        return null;
      }

      // Determine default dropdown value based on existing files
      let defaultDropdownValue = 'plc';
      let defaultPath = 'data/input/plc';

      if (uploadState.files.length > 0) {
        // If first file exists, set opposite value for second file
        const firstFileDropdown = uploadState.files[0].dropdownValue;
        defaultDropdownValue = firstFileDropdown === 'plc' ? 'quality' : 'plc';
        defaultPath = defaultDropdownValue === 'plc' ? 'data/input/plc' : 'data/input/quality';
      } else if (index > 0) {
        // If adding multiple files at once, alternate between plc and quality
        defaultDropdownValue = index % 2 === 0 ? 'plc' : 'quality';
        defaultPath = defaultDropdownValue === 'plc' ? 'data/input/plc' : 'data/input/quality';
      }

      return {
        id: uuidv4(),
        file,
        path: defaultPath,
        dropdownValue: defaultDropdownValue,
        progress: 0,
        speed: 0,
        status: 'idle' as const,
        error: null,
        uploadId: null,
        key: null,
      };
    }).filter(Boolean) as FileUploadItem[];

    setUploadState(prev => ({
      ...prev,
      files: [...prev.files, ...newFiles],
    }));
  };

  const updateFileState = (fileId: string, updates: Partial<FileUploadItem>) => {
    setUploadState(prev => ({
      ...prev,
      files: prev.files.map(file =>
        file.id === fileId ? { ...file, ...updates } : file
      ),
    }));
  };

  const removeFile = (fileId: string) => {
    // Cancel upload if in progress
    const abortController = abortControllersRef.current.get(fileId);
    if (abortController) {
      abortController.abort();
      abortControllersRef.current.delete(fileId);
    }

    setUploadState(prev => ({
      ...prev,
      files: prev.files.filter(file => file.id !== fileId),
    }));
  };

  const uploadSingleFile = async (fileItem: FileUploadItem) => {
    if (!fileItem.path) {
      updateFileState(fileItem.id, {
        status: 'error',
        error: 'Path is required'
      });
      return;
    }

    const abortController = new AbortController();
    abortControllersRef.current.set(fileItem.id, abortController);

    try {
      updateFileState(fileItem.id, { status: 'uploading', progress: 0, speed: 0, error: null });
      console.log('fileItem', fileItem)

      // Step 1: Initiate multipart upload
      const initiateResponse = await postRequest('/upload/initiate', {
        fileName: fileItem.file.name,
        fileType: fileItem.file.type,
        fileSize: fileItem.file.size,
        path: fileItem.path,
      });

      if (initiateResponse.status !== 200) throw new Error('Failed to initiate upload');

      const { uploadId, key, totalParts, chunkSize } = initiateResponse.data.data;

      updateFileState(fileItem.id, { uploadId, key });

      // Step 2: Upload parts in parallel
      const uploadedParts: Array<{ ETag: string; PartNumber: number }> = [];
      const startTime = Date.now();
      let uploadedBytes = 0;

      // Get presigned URLs for all parts
      const partNumbers = Array.from({ length: totalParts }, (_, i) => i + 1);
      const urlsResponse = await postRequest('/upload/parts', {
        uploadId,
        key,
        partNumbers
      });

      if (urlsResponse.status !== 200) throw new Error('Failed to get presigned URLs');

      const { presignedUrls } = urlsResponse.data.data;

      // Upload parts with controlled concurrency (4 parallel uploads)
      const maxConcurrency = 4;

      for (let i = 0; i < presignedUrls.length; i += maxConcurrency) {
        const batch = presignedUrls.slice(i, i + maxConcurrency);

        const batchPromises = batch.map(async ({ partNumber, presignedUrl }: any) => {
          const start = (partNumber - 1) * chunkSize;
          const end = Math.min(start + chunkSize, fileItem.file.size);
          const chunk = fileItem.file.slice(start, end);

          const uploadResponse = await fetch(presignedUrl, {
            method: 'PUT',
            body: chunk,
            signal: abortController.signal,
          });

          if (!uploadResponse.ok) throw new Error(`Failed to upload part ${partNumber}`);

          const etag = uploadResponse.headers.get('ETag');
          if (!etag) throw new Error(`No ETag received for part ${partNumber}`);

          uploadedParts.push({ ETag: etag, PartNumber: partNumber });
          uploadedBytes += chunk.size;

          // Update progress and speed
          const elapsedTime = (Date.now() - startTime) / 1000;
          const speed = uploadedBytes / elapsedTime;
          const progress = (uploadedBytes / fileItem.file.size) * 100;

          updateFileState(fileItem.id, { progress, speed });
        });

        await Promise.all(batchPromises);
      }

      // Step 3: Complete multipart upload
      const completeResponse = await postRequest('/upload/complete', {
        uploadId,
        key,
        parts: uploadedParts,
      });

      if (completeResponse.status !== 200) throw new Error('Failed to complete upload');

      updateFileState(fileItem.id, { status: 'completed', progress: 100 });

    } catch (error: any) {
      if (error.name === 'AbortError') {
        // Handle abort
        if (fileItem.uploadId && fileItem.key) {
          try {
            await postRequest('/upload/abort', {
              uploadId: fileItem.uploadId,
              key: fileItem.key,
            });
          } catch (abortError) {
            console.error('Failed to abort upload:', abortError);
          }
        }

        updateFileState(fileItem.id, { status: 'idle', progress: 0, speed: 0 });
      } else {
        updateFileState(fileItem.id, {
          status: 'error',
          error: error.message || 'Upload failed',
        });
      }
    } finally {
      abortControllersRef.current.delete(fileItem.id);
    }
  };

  const uploadAllFiles = async () => {
    const filesToUpload = uploadState.files.filter(file => file.status === 'idle');

    if (filesToUpload.length === 0) {
      message.warning('No files to upload');
      return;
    }

    // Validate exactly 2 files
    if (uploadState.files.length !== 2) {
      message.error('Please select exactly 2 files to upload');
      return;
    }

    if (filesToUpload.length !== 2) {
      message.error('Both files must be ready to upload');
      return;
    }

    // Validate that all files have paths
    const filesWithoutPath = filesToUpload.filter(file => !file.path);
    if (filesWithoutPath.length > 0) {
      message.error('Please select paths for all files');
      return;
    }

    setUploadState(prev => ({ ...prev, isUploading: true }));

    // Upload files with limited concurrency (2 files at a time)
    const maxConcurrentUploads = 2;

    for (let i = 0; i < filesToUpload.length; i += maxConcurrentUploads) {
      const batch = filesToUpload.slice(i, i + maxConcurrentUploads);
      const batchPromises = batch.map(file => uploadSingleFile(file));

      // Wait for current batch to complete before starting next batch
      await Promise.allSettled(batchPromises);
    }

    setUploadState(prev => ({ ...prev, isUploading: false }));
    message.success('All uploads completed!');

    try {
      const webhookUrl = process.env.REACT_APP_WEBHOOK_URL;
      console.log('webhookUrl', webhookUrl)
      const token = process.env.REACT_APP_WEBHOOK_TOKEN;

      if (webhookUrl && token) {
        const response = await axios.post(
          webhookUrl,
          {},
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );

        console.log(" Webhook response", response);
        message.success("Webhook triggered successfully!");
      } else {
        console.warn("Webhook URL or Token not defined in .env");
      }
    } catch (error) {
      console.error("Webhook error:", error);
      message.error("Failed to trigger webhook!");
    }
  };

  const cancelUpload = (fileId: string) => {
    const abortController = abortControllersRef.current.get(fileId);
    if (abortController) {
      abortController.abort();
    }
  };

  const resetAll = () => {
    // Cancel all ongoing uploads
    abortControllersRef.current.forEach(controller => controller.abort());
    abortControllersRef.current.clear();

    setUploadState({
      files: [],
      isUploading: false,
    });

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const updateFileDropdown = (fileId: string, dropdownValue: string) => {
    let newPath = '';
    if (dropdownValue === 'plc') {
      newPath = 'data/input/plc';
    } else if (dropdownValue === 'quality') {
      newPath = 'data/input/quality';
    }

    // Update the current file
    updateFileState(fileId, { dropdownValue, path: newPath });

    // If there are 2 files, update the other file to the opposite value
    if (uploadState.files.length === 2) {
      const otherFile = uploadState.files.find(file => file.id !== fileId);
      if (otherFile) {
        const oppositeValue = dropdownValue === 'plc' ? 'quality' : 'plc';
        const oppositePath = oppositeValue === 'plc' ? 'data/input/plc' : 'data/input/quality';
        updateFileState(otherFile.id, { dropdownValue: oppositeValue, path: oppositePath });
      }
    }
  };



  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 px-4">
      <div className="max-w-4xl w-full">
        {/* File Drop Zone */}
        <div
          className={`
            relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 shadow-lg bg-white mb-6
            ${isDragOver
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-300 hover:border-gray-400'
            }
            ${uploadState.isUploading ? 'pointer-events-none opacity-75' : ''}
          `}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <input
            ref={fileInputRef}
            type="file"
            multiple
            onChange={handleFileInputChange}
            className="hidden"
            accept="*/*"
          />

          <div className="space-y-4">
            <UploadOutlined className="text-4xl text-gray-400" />
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-2">
                Drop your files here or click to browse
              </h3>
              <p className="text-gray-500 mb-4">
                Select exactly 2 files (PLC and Quality data)
              </p>
              <button
                onClick={() => fileInputRef.current?.click()}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium shadow-sm"
              >
                Select Files
              </button>
            </div>
          </div>
        </div>



        {/* Files List */}
        {uploadState.files.length > 0 && (
          <div className="bg-white p-6 rounded-xl shadow-lg">
            <div className="flex justify-between items-center mb-4">
              <h4 className="text-lg font-semibold text-gray-700">Selected Files ({uploadState.files.length})</h4>
              <Space>
                <Button onClick={resetAll} disabled={uploadState.isUploading}>
                  Clear All
                </Button>
                <Button
                  type="primary"
                  icon={<UploadOutlined />}
                  onClick={uploadAllFiles}
                  disabled={uploadState.isUploading || uploadState.files.length !== 2}
                  loading={uploadState.isUploading}
                >
                  Upload Both Files
                </Button>
              </Space>
            </div>

            <div className="space-y-4">
              {uploadState.files.map((fileItem) => (
                <Card key={fileItem.id} size="small">
                  <div className="space-y-3">
                    {/* File Info Row */}
                    <Row gutter={16} align="middle">
                      <Col span={12}>
                        <Space>
                          <FileOutlined className="text-blue-600" />
                          <div>
                            <Text strong className="block">{fileItem.file.name}</Text>
                            <Text type="secondary" className="text-sm">
                              {formatFileSize(fileItem.file.size)}
                            </Text>
                          </div>
                        </Space>
                      </Col>

                      <Col span={8}>
                        {fileItem.status === 'uploading' && (
                          <div>
                            <Progress
                              percent={Math.round(fileItem.progress)}
                              size="small"
                              status="active"
                            />
                            <Text type="secondary" className="text-xs">
                              {formatSpeed(fileItem.speed)}
                            </Text>
                          </div>
                        )}

                        {fileItem.status === 'completed' && (
                          <Space>
                            <CheckCircleOutlined className="text-green-600" />
                            <Text type="success">Completed</Text>
                          </Space>
                        )}

                        {fileItem.status === 'error' && (
                          <Space>
                            <ExclamationCircleOutlined className="text-red-600" />
                            <Text type="danger" title={fileItem.error || ''}>Error</Text>
                          </Space>
                        )}

                        {fileItem.status === 'idle' && (
                          <Text type="secondary">Ready</Text>
                        )}
                      </Col>

                      <Col span={4} className="text-right">
                        <Space>
                          {fileItem.status === 'uploading' && (
                            <Button
                              size="small"
                              danger
                              onClick={() => cancelUpload(fileItem.id)}
                            >
                              Cancel
                            </Button>
                          )}
                          <Button
                            size="small"
                            icon={<DeleteOutlined />}
                            onClick={() => removeFile(fileItem.id)}
                            disabled={fileItem.status === 'uploading'}
                          />
                        </Space>
                      </Col>
                    </Row>

                    {/* Path Selection Row */}
                    <Row gutter={16} align="middle">
                      <Col span={6}>
                        <Text strong>Upload Path:</Text>
                      </Col>
                      <Col span={8}>
                        <select
                          value={fileItem.dropdownValue}
                          onChange={(e) => updateFileDropdown(fileItem.id, e.target.value)}
                          disabled={fileItem.status === 'uploading'}
                          className="border rounded px-3 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="plc">PLC</option>
                          <option value="quality">Quality</option>
                          {/* <option value="other">Other</option> */}
                        </select>
                      </Col>
                      {/* <Col span={10}>
                        <Text type="secondary" className="text-sm">
                          <strong>Path:</strong> {fileItem.path || 'Path will be set automatically'}
                        </Text>
                      </Col> */}
                    </Row>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FileUploader;
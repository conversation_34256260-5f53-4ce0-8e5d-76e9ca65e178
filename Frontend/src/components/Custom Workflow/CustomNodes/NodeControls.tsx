import React, { useState } from 'react';
import { Button, Tooltip, Dropdown, MenuProps } from 'antd';
import { DeleteOutlined, PlayCircleOutlined, MoreOutlined, SettingOutlined } from '@ant-design/icons';
import { NodeControlsProps } from './types';

const NodeControls: React.FC<NodeControlsProps> = ({
  nodeId,
  onDelete,
  onExecute,
  variant = 'overlay',
  onOpenConfig,
  hasConfigurableFields = false
}) => {
  const [dropdownVisible, setDropdownVisible] = useState(false);

  const menuItems: MenuProps['items'] = [
    {
      key: 'delete',
      label: 'Delete',
      icon: <DeleteOutlined />,
      danger: true,
      onClick: () => {
        onDelete(nodeId);
        setDropdownVisible(false);
      }
    }
  ];

  const wrapperClass =
    variant === 'overlay'
      ? 'absolute right-2 top-1/2 -translate-y-1/2 flex flex-row items-center gap-1'
      : 'flex flex-row items-center gap-1';

  const buttonClass = "w-7 h-7 flex items-center justify-center rounded-lg border-0 transition-all duration-200";

  return (
    <div className={wrapperClass}>
      {hasConfigurableFields && onOpenConfig && (
        <Tooltip title="Configure Fields">
          <Button
            size="small"
            type="text"
            icon={<SettingOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              onOpenConfig();
            }}
            className={`${buttonClass} bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800`}
          />
        </Tooltip>
      )}
      
      <Tooltip title="Execute">
        <Button
          size="small"
          type="text"
          icon={<PlayCircleOutlined />}
          onClick={(e) => {
            e.stopPropagation();
            onExecute(nodeId);
          }}
          className={`${buttonClass} bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800`}
        />
      </Tooltip>
      
      <Dropdown
        menu={{ items: menuItems }}
        trigger={['click']}
        open={dropdownVisible}
        onOpenChange={setDropdownVisible}
        placement="bottomRight"
      >
        <Tooltip title="More actions">
          <Button
            size="small"
            type="text"
            icon={<MoreOutlined />}
            onClick={(e) => {
              e.stopPropagation();
            }}
            className={`${buttonClass} bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800`}
          />
        </Tooltip>
      </Dropdown>
    </div>
  );
};

export default NodeControls;

import React, { useState } from 'react';
import { Modal } from 'antd';
import Editor from '@monaco-editor/react';
import { CustomNodeData } from './types';

const JsonConfigEditor: React.FC<{ nodeData: CustomNodeData; theme?: 'light' | 'vs-dark' }> = ({ nodeData, theme = 'light' }) => {
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  
  const settingsPayload = {
    settings: nodeData.settings || {}
  };

  const jsonString = JSON.stringify(settingsPayload, null, 2);

  const openEditor = () => {
    setIsEditorOpen(true);
  };

  const closeEditor = () => {
    setIsEditorOpen(false);
  };

  return (
    <div style={{ height: '100%' }}>
      <div className="border border-gray-200 rounded-lg overflow-hidden" style={{ height: '100%' }}>
        <Editor
          height="100%"
          language="json"
          value={jsonString}
          theme={theme}
          options={{
            readOnly: true,
            minimap: { enabled: false },
            scrollBeyondLastLine: false,
            fontSize: 12,
            lineNumbers: 'on',
            wordWrap: 'on',
            automaticLayout: true,
            folding: true,
            showFoldingControls: 'always',
            foldingStrategy: 'indentation'
          }}
        />
      </div>
      
      <Modal
        open={isEditorOpen}
        onCancel={closeEditor}
        footer={null}
        width={900}
        destroyOnClose
        title="JSON Configuration"
      >
        <div className="space-y-2">
          <div className="border border-gray-200 rounded overflow-hidden" style={{ height: 500 }}>
            <Editor
              height="100%"
              language="json"
              value={jsonString}
              theme={theme}
              options={{
                readOnly: true,
                minimap: { enabled: false },
                scrollBeyondLastLine: false,
                fontSize: 13,
                lineNumbers: 'on',
                wordWrap: 'on',
                automaticLayout: true,
                folding: true,
                showFoldingControls: 'always',
                foldingStrategy: 'indentation'
              }}
            />
          </div>
          <div className="flex justify-end gap-2">
            <button onClick={closeEditor} className="px-3 py-1 rounded border border-gray-300 text-gray-700">Close</button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default JsonConfigEditor; 
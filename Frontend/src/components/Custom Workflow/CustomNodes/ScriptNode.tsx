import React, { useState, useEffect, useMemo, useRef } from 'react';
import { NodeProps } from 'reactflow';
import { Typography } from 'antd';
import { CodeOutlined } from '@ant-design/icons';
import BaseNode from './BaseNode';
import <PERSON><PERSON>enderer from './FieldRenderer';
import JsonToggleWrapper from './JsonToggleWrapper';
import { ScriptNodeData, Operation, FieldConfig } from './types';
import { useFetchOperations } from '../../../hooks/useFetchOperations';

const { Text } = Typography;

interface ScriptNodeProps extends NodeProps {
  data: ScriptNodeData & {
    onSettingsChange?: (nodeId: string, settings: Record<string, any>) => void;
    onDelete?: (nodeId: string) => void;
    onToggleMinimize?: (nodeId: string) => void;
    onExecute?: (nodeId: string) => void;
    onToggleJsonConfig?: (nodeId: string) => void;
  };
}

const ScriptNode: React.FC<ScriptNodeProps> = ({ id, data, selected }) => {
  const [settings, setSettings] = useState(data.settings || {});
  const { operations, loading, getById } = useFetchOperations('custom');
  const operation = useMemo(() => (data.operationId ? getById(data.operationId) : null), [getById, data.operationId]);
  const editorsRef = useRef<Map<string, () => void>>(new Map());

  useEffect(() => {
    setSettings(data.settings || {});
  }, [data.settings]);

  const handleSettingChange = (key: string, value: any) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    if (data.onSettingsChange) {
      data.onSettingsChange(id, { [key]: value });
    }
  };

  const handleRegisterEditor = (fieldId: string, openEditor: () => void) => {
    editorsRef.current.set(fieldId, openEditor);
  };

  const handleOpenConfig = () => {
    const editors = Array.from(editorsRef.current.values());
    if (editors.length > 0) {
      editors[0]();
    }
  };

  const hasConfigurableFields = () => {
    return operation?.config?.fields?.some((field: FieldConfig) => 
      field.type === 'code' || field.type === 'json'
    ) || false;
  };

  const renderDynamicFields = () => {
    if (!operation?.config?.fields || !Array.isArray(operation.config.fields)) {
      return (
        <div className="space-y-2">
          <Text className="block text-sm text-gray-700 mb-2">Loading configuration...</Text>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {operation.config.fields.map((field: FieldConfig) => {
          if (!field?.id || !field?.type) {
            console.warn('Invalid field config:', field);
            return null;
          }
          return (
            <FieldRenderer
              key={field.id}
              field={field}
              value={settings[field.id as keyof typeof settings]}
              onChange={(value) => handleSettingChange(field.id, value)}
              language={settings.language as string}
              onLanguageChange={(language) => handleSettingChange('language', language)}
              onRegisterEditor={handleRegisterEditor}
              nodeData={data}
            />
          );
        })}
      </div>
    );
  };

  const renderHiddenFields = () => {
    if (!operation?.config?.fields || !Array.isArray(operation.config.fields)) {
      return null;
    }

    return (
      <div style={{ display: 'none' }}>
        {operation.config.fields.map((field: FieldConfig) => {
          if (!field?.id || !field?.type || (field.type !== 'code' && field.type !== 'json')) {
            return null;
          }
          return (
            <FieldRenderer
              key={`hidden-${field.id}`}
              field={field}
              value={settings[field.id as keyof typeof settings]}
              onChange={(value) => handleSettingChange(field.id, value)}
              language={settings.language as string}
              onLanguageChange={(language) => handleSettingChange('language', language)}
              onRegisterEditor={handleRegisterEditor}
              nodeData={data}
            />
          );
        })}
      </div>
    );
  };

  const getHeaderColor = () => {
    if (operation?.config?.ui?.headerColor) return operation.config.ui.headerColor;
    return '#fff7e6';
  };

  const getHeaderIcon = () => {
    if (operation?.icon) {
      return (
        <img
          src={operation.icon}
          alt={operation.name}
          style={{ width: '18px', height: '18px' }}
          onError={(e) => {
            console.warn(`Failed to load operation icon: ${operation.icon}`);
            (e.currentTarget as HTMLImageElement).style.display = 'none';
          }}
        />
      );
    }
    return <CodeOutlined style={{ color: '#fa8c16', fontSize: '18px' }} />;
  };

  const renderContent = () => {
    if (!data.onToggleJsonConfig) return renderDynamicFields();
    
    return (
      <>
        <JsonToggleWrapper 
          nodeData={data} 
          onToggleJsonConfig={data.onToggleJsonConfig}
          nodeId={id}
        >
          {renderDynamicFields()}
        </JsonToggleWrapper>
        {renderHiddenFields()}
      </>
    );
  };

  if (loading && !operation) {
    return (
      <BaseNode
        id={id}
        data={data}
        selected={selected}
        headerColor={getHeaderColor()}
        headerIcon={getHeaderIcon()}
        showInputHandle={true}
        showOutputHandle={true}
        onSettingsChange={(nid: string, ns: Record<string, any>) => data.onSettingsChange && data.onSettingsChange(nid, ns)}
        onDelete={(nid: string) => data.onDelete && data.onDelete(nid)}
        onToggleMinimize={(nid: string) => data.onToggleMinimize && data.onToggleMinimize(nid)}
        onExecute={(nid: string) => data.onExecute && data.onExecute(nid)}
        onOpenConfig={handleOpenConfig}
        hasConfigurableFields={hasConfigurableFields()}
      >
        <div className="flex items-center justify-center h-full">
          <Text type="secondary">Loading configuration...</Text>
        </div>
      </BaseNode>
    );
  }

  return (
    <BaseNode
      id={id}
      data={data}
      selected={selected}
      headerColor={getHeaderColor()}
      headerIcon={getHeaderIcon()}
      showInputHandle={operation?.config?.handles?.input !== false}
      showOutputHandle={operation?.config?.handles?.output !== false}
      onSettingsChange={(nid: string, ns: Record<string, any>) => data.onSettingsChange && data.onSettingsChange(nid, ns)}
      onDelete={(nid: string) => data.onDelete && data.onDelete(nid)}
      onToggleMinimize={(nid: string) => data.onToggleMinimize && data.onToggleMinimize(nid)}
      onExecute={(nid: string) => data.onExecute && data.onExecute(nid)}
      onOpenConfig={handleOpenConfig}
      hasConfigurableFields={hasConfigurableFields()}
    >
      {renderContent()}
    </BaseNode>
  );
};

export default ScriptNode; 
import React from 'react';
import { Card } from 'antd';
import { Hand<PERSON>, Position } from 'reactflow';
import NodeControls from './NodeControls';
import { BaseNodeProps } from './types';


interface BaseNodeInternalProps extends BaseNodeProps {
  children: React.ReactNode;
  headerColor: string;
  headerIcon: React.ReactNode;
  showInputHandle?: boolean;
  showOutputHandle?: boolean;
  width?: number;
}

const BaseNode: React.FC<BaseNodeInternalProps> = ({
  id,
  data,
  selected,
  children,
  headerColor,
  headerIcon,
  showInputHandle = false,
  showOutputHandle = false,
  width = 280,
  onSettingsChange,
  onDelete,
  onToggleMinimize,
  onExecute,
  onOpenConfig,
  hasConfigurableFields
}) => {
  const minimizedHeight = 48;
  const headerHeight = data.minimized ? 60 : 74;
  const handleTopPosition = headerHeight / 2 - 6;
  const [isEditing, setIsEditing] = React.useState(false);
  const [draftLabel, setDraftLabel] = React.useState(data.label);

  React.useEffect(() => {
    setDraftLabel(data.label);
  }, [data.label]);

  const commitLabel = () => {
    if (isEditing) {
      setIsEditing(false);
      if (draftLabel !== data.label && (data as any).onLabelChange) {
        (data as any).onLabelChange(id, draftLabel);
      }
    }
  };

  const handleDoubleClickToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isEditing) {
      onToggleMinimize(id);
    }
  };

  return (
    <div className="group relative">
      {showInputHandle && (
        <Handle
          id="in"
          type="target"
          position={Position.Left}
          style={{
            left: -8,
            top: handleTopPosition,
            width: 12,
            height: 12,
            background: '#6366f1',
            border: '2px solid white',
            borderRadius: '50%',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            position: 'absolute',
            zIndex: 20
          }}
        />
      )}
      
      {showOutputHandle && (
        <Handle
          id="out"
          type="source"
          position={Position.Right}
          style={{
            right: -8,
            top: handleTopPosition,
            width: 12,
            height: 12,
            background: '#10b981',
            border: '2px solid white',
            borderRadius: '50%',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            position: 'absolute',
            zIndex: 20
          }}
        />
      )}

      <Card
        size="small"
        style={{
          width,
          minHeight: data.minimized ? minimizedHeight : 120,
          border: selected ? '2px solid #3b82f6' : '1px solid #e5e7eb',
          borderRadius: 12,
          boxShadow: selected 
            ? '0 4px 12px rgba(59, 130, 246, 0.15), 0 2px 4px rgba(0, 0, 0, 0.05)' 
            : '0 2px 8px rgba(0, 0, 0, 0.06), 0 1px 3px rgba(0, 0, 0, 0.1)',
          overflow: 'hidden',
          background: '#ffffff'
        }}
        bodyStyle={{ padding: 0 }}
      >
        <div 
          style={{ 
            background: headerColor,
            padding: '12px 16px',
            borderBottom: data.minimized ? 'none' : '1px solid #f3f4f6',
            display: 'flex',
            alignItems: 'center',
            gap: 12,
            borderRadius: data.minimized ? '12px' : '12px 12px 0 0'
          }}
        >
          <div className="flex items-center gap-3 flex-1 min-w-0">
            {!isEditing && headerIcon}
            <div className="flex items-center gap-2 flex-1 min-w-0">
              {isEditing ? (
                <input
                  value={draftLabel}
                  onChange={(e) => setDraftLabel(e.target.value)}
                  onBlur={commitLabel}
                  onKeyDown={(e) => { 
                    if (e.key === 'Enter') commitLabel(); 
                    if (e.key === 'Escape') { 
                      setIsEditing(false); 
                      setDraftLabel(data.label); 
                    } 
                  }}
                  className="text-sm font-medium text-gray-800 border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-full"
                  spellCheck={false}
                  autoFocus
                />
              ) : (
                <>
                  <span
                    className="font-semibold text-sm text-gray-800 cursor-text"
                    style={{
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      width: '80px',
                      flexShrink: 0
                    }}
                    onClick={() => setIsEditing(true)}
                    title={`${data.label} - Click to rename`}
                  >
                    {data.label}
                  </span>
                  <div 
                    className="flex-1 min-h-[20px] cursor-pointer"
                    onDoubleClick={handleDoubleClickToggle}
                    title={`Double-click to ${data.minimized ? 'expand' : 'minimize'}`}
                    style={{ minWidth: '80px' }}
                  />
                </>
              )}
            </div>
          </div>
          {!data.minimized && (
            <NodeControls
              nodeId={id}
              onDelete={onDelete}
              onExecute={onExecute}
              variant="inline"
              onOpenConfig={onOpenConfig}
              hasConfigurableFields={hasConfigurableFields}
            />
          )}
        </div>
        
        {!data.minimized && (
          <div style={{ padding: '12px', display: 'flex', flexDirection: 'column' }}>
            {children}
          </div>
        )}
        {data.minimized && (
          <div style={{ display: 'none' }}>
            {children}
          </div>
        )}
      </Card>

      {data.minimized && !isEditing && (
        <NodeControls
          nodeId={id}
          onDelete={onDelete}
          onExecute={onExecute}
          variant="overlay"
          onOpenConfig={onOpenConfig}
          hasConfigurableFields={hasConfigurableFields}
        />
      )}
    </div>
  );
};

export default BaseNode; 
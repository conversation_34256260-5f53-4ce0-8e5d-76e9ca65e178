import React, { useState } from 'react';
import { Button } from 'antd';
import { SaveModal } from '../Modal/workflowModal';
import traingle from '../../img/traingle.svg';

interface CustomFlowActionsProps {
  onSave: (workflowName?: string) => void;
  onExecute: (workflowName?: string) => void;
  visible: boolean;
  workflowId: number;
}

const CustomFlowActions: React.FC<CustomFlowActionsProps> = ({ onSave, onExecute, visible, workflowId }) => {
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [actionType, setActionType] = useState<'save' | 'executeFlow'>('save');

  if (!visible) return null;

  const openModalIfNeeded = (type: 'save' | 'executeFlow') => {
    setActionType(type);
    if (workflowId === 0) {
      setIsModalOpen(true);
    } else {
      if (type === 'save') onSave();
      else onExecute();
    }
  };

  const handleModalClose = () => setIsModalOpen(false);

  const handleModalConfirm = (name?: string) => {
    if (actionType === 'save') onSave(name);
    else onExecute(name);
  };

  return (
    <div className="flow-actions flex">
      <Button onClick={() => openModalIfNeeded('executeFlow')} className="combined-button">
        <span className="execute-section">
          <img className='h-[15px]' src={traingle}/> Execute Flow
        </span>
      </Button>
      <Button onClick={() => openModalIfNeeded('save')} className="save-section btn-primary-new">
        <span className="save-section">Save</span>
      </Button>
      <SaveModal isOpen={isModalOpen} onClose={handleModalClose} onSave={handleModalConfirm} type={actionType} />
    </div>
  );
};

export default CustomFlowActions;

import React, { useState } from "react";
import Plot from "react-plotly.js";
import dayjs from "dayjs";
import { Spin, Switch } from "antd";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import weekOfYear from "dayjs/plugin/weekOfYear";
import isoWeek from "dayjs/plugin/isoWeek";
import updateLocale from "dayjs/plugin/updateLocale";

// Initialize dayjs plugins
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(weekOfYear);
dayjs.extend(isoWeek);
dayjs.extend(updateLocale);

// Set Monday as first day of week
dayjs.updateLocale("en", { weekStart: 1 });

interface KpiStatData {
  date: string;
  mean_value: string;
  median_value: string;
  std_dev_value: string;
  good_percentage?: number; // New field from backend
  bad_percentage?: number;  // New field from backend
  q1_value?: number;
  q3_value?: number;
  iqr_mean_value?: number;
  iqr_std_dev_value?: number;
  median_iqr_ratio?: number;
  cp_value?: number;
  cpk_value?: number;
  count: number;
}

interface KpiStatisticsGraphsProps {
  data: KpiStatData[];
  loading: boolean;
  viewType: "daily" | "monthly" | "weekly";
}

export const KpiStatisticsGraphs: React.FC<KpiStatisticsGraphsProps> = ({
  data,
  loading,
  viewType,
}) => {
  // State for percentage toggle
  const [showGoodPercentage, setShowGoodPercentage] = useState(true);

  // Handle empty or invalid data
  const isValidData = React.useMemo(() => {
    return (
      Array.isArray(data) &&
      data.length > 0 &&
      data.some(
        (item) => item?.mean_value && item?.median_value && item?.std_dev_value
      )
    );
  }, [data]);

  const graphs = [
    {
      id: "mean",
      title: "Mean Values",
      yAxisTitle: "Mean Value",
      color: "#1a237e",
      getValue: (item: KpiStatData) => {
        const value = parseFloat(item?.mean_value || "0");
        return isNaN(value) ? 0 : value;
      },
    },
    {
      id: "median",
      title: "Median Values",
      yAxisTitle: "Median Value",
      color: "#1a237e",
      getValue: (item: KpiStatData) => {
        const value = parseFloat(item?.median_value || "0");
        return isNaN(value) ? 0 : value;
      },
    },
    {
      id: "stddev",
      title: "Standard Deviation",
      yAxisTitle: "Standard Deviation",
      color: "#1a237e",
      getValue: (item: KpiStatData) => {
        const value = parseFloat(item?.std_dev_value || "0");
        return isNaN(value) ? 0 : value;
      },
      getYAxisConfig: (values: number[]) => {
        const maxValue = Math.max(...values, 0.1);
        return {
          range: [0, maxValue],
          tickformat: ".3f",
        };
      },
    },
    {
      id: "percentage",
      title: "Percentage Analysis",
      yAxisTitle: showGoodPercentage
        ? "Good Percentage (%)"
        : "Bad Percentage (%)",
      color: "#1a237e",
      getValue: (item: KpiStatData) => {
        const value = showGoodPercentage
          ? item?.good_percentage || 0
          : item?.bad_percentage || 0;
        return isNaN(value) ? 0 : value;
      },
      getYAxisConfig: () => ({
        range: [0, 100], // Percentage always 0-100
        tickformat: ".1f",
        ticksuffix: "%",
      }),
    },
    {
      id: "iqr_mean",
      title: "Q3 - Q1 Mean",
      yAxisTitle: "IQR Mean Value",
      color: "#1a237e",
      getValue: (item: KpiStatData) => {
        const value = item?.iqr_mean_value || 0;
        return isNaN(value) ? 0 : value;
      },
    },
    {
      id: "iqr_std_dev",
      title: "Q3 - Q1 Standard Deviation",
      yAxisTitle: "IQR Standard Deviation",
      color: "#1a237e",
      getValue: (item: KpiStatData) => {
        const value = item?.iqr_std_dev_value || 0;
        return isNaN(value) ? 0 : value;
      },
      getYAxisConfig: (values: number[]) => {
        const maxValue = Math.max(...values, 0.1);
        return {
          range: [0, maxValue],
          tickformat: ".3f",
        };
      },
    },
    {
      id: "median_iqr_ratio",
      title: "Median to IQR Ratio",
      yAxisTitle: "Median/IQR Ratio",
      color: "#1a237e",
      getValue: (item: KpiStatData) => {
        const value = item?.median_iqr_ratio || 0;
        return isNaN(value) ? 0 : value;
      },
      getYAxisConfig: (values: number[]) => {
        const minValue = Math.min(...values);
        const maxValue = Math.max(...values);
        const padding = (maxValue - minValue) * 0.1;
        return {
          range: [Math.max(0, minValue - padding), maxValue + padding],
          tickformat: ".3f",
        };
      },
    },
    {
      id: "cp",
      title: "Cp (Process Capability)",
      yAxisTitle: "Cp Value",
      color: "#1a237e",
      getValue: (item: KpiStatData) => {
        const value = item?.cp_value || 0;
        return isNaN(value) ? 0 : value;
      },
      getYAxisConfig: (values: number[]) => {
        const maxValue = Math.max(...values, 2.0);
        return {
          range: [0, maxValue],
          tickformat: ".3f",
        };
      },
    },
    {
      id: "cpk",
      title: "Cpk (Process Capability Index)",
      yAxisTitle: "Cpk Value",
      color: "#1a237e",
      getValue: (item: KpiStatData) => {
        const value = item?.cpk_value || 0;
        return isNaN(value) ? 0 : value;
      },
      getYAxisConfig: (values: number[]) => {
        const maxValue = Math.max(...values, 2.0);
        return {
          range: [0, maxValue],
          tickformat: ".3f",
        };
      },
    },
  ];

  // Helper function to check if a specific graph has valid data
  const hasValidDataForGraph = (graphId: string) => {
    if (graphId === "percentage") {
      // Check if any item has percentage data
      return data.some((item) =>
        (item?.good_percentage !== undefined && item?.good_percentage !== null) ||
        (item?.bad_percentage !== undefined && item?.bad_percentage !== null)
      );
    }
    if (graphId === "cp" || graphId === "cpk") {
      // Check if any item has Cp/Cpk data
      return data.some((item) =>
        (item?.cp_value !== undefined && item?.cp_value !== null) ||
        (item?.cpk_value !== undefined && item?.cpk_value !== null)
      );
    }
    if (graphId === "iqr_mean" || graphId === "iqr_std_dev" || graphId === "median_iqr_ratio") {
      // Check if any item has IQR-based data
      return data.some((item) =>
        (item?.iqr_mean_value !== undefined && item?.iqr_mean_value !== null) ||
        (item?.iqr_std_dev_value !== undefined && item?.iqr_std_dev_value !== null) ||
        (item?.median_iqr_ratio !== undefined && item?.median_iqr_ratio !== null)
      );
    }
    return isValidData;
  };

  // Helper function to render individual graph
  const renderGraph = (graph: typeof graphs[0]) => {
    const hasData = hasValidDataForGraph(graph.id);

    if (!hasData) {
      let errorMessage = "No data available";
      if (graph.id === "percentage") {
        errorMessage = "No percentage data available - Apply system filters to see percentage analysis";
      } else if (graph.id === "cp" || graph.id === "cpk") {
        errorMessage = "No process capability data available - Configure specification limits to see Cp/Cpk analysis";
      } else if (graph.id === "iqr_mean" || graph.id === "iqr_std_dev" || graph.id === "median_iqr_ratio") {
        errorMessage = "No IQR data available - Insufficient data points for quartile analysis";
      }

      return (
        <div className="flex justify-center items-center h-[300px] text-gray-500 text-center px-4">
          {errorMessage}
        </div>
      );
    }

    const values = data
      .map((item) => graph.getValue(item))
      .filter((val) => !isNaN(val));

    // Calculate y-axis range
    let yAxisConfig;
    if (graph.getYAxisConfig) {
      yAxisConfig = graph.getYAxisConfig(values);
    } else if (graph.id === "percentage") {
      yAxisConfig = {
        range: [0, 100],
        tickformat: ".1f",
        ticksuffix: "%",
      };
    } else {
      const minValue = Math.max(
        0,
        Math.min(...values) - (Math.max(...values) - Math.min(...values)) * 0.1
      );
      const maxValue =
        Math.max(...values) + (Math.max(...values) - Math.min(...values)) * 0.1;
      yAxisConfig = {
        range: [minValue, maxValue],
        tickformat: ".2f",
      };
    }

    // Format x-axis labels based on viewType
    const xValues = data.map((item) => {
      if (viewType === "weekly" && item.date.includes("-")) {
        // Convert ISO week format to a proper date for the first day of that week
        const weekDate = getDateFromISOWeek(item.date);
        if (weekDate) {
          const weekStart = dayjs(weekDate);
          const weekEnd = weekStart.add(6, "day");
          // Format as "Jan (20-26)" style
          return `${weekStart.format("MMM")} (${weekStart.format(
            "DD"
          )}-${weekEnd.format("DD")})`;
        }
        return item.date;
      } else if (viewType === "monthly") {
        // Format as "Jan 2025"
        return dayjs.utc(item.date).format("MMM YYYY");
      } else {
        // Daily view - show only "Jan 20"
        return dayjs.utc(item.date).format("MMM DD");
      }
    });

    const yValues = data.map((item) => graph.getValue(item));

    // Create Plotly data
    const plotData = [
      {
        type: "bar",
        x: xValues,
        y: yValues,
        marker: {
          color: graph.color,
        },
        name: graph.yAxisTitle,
        hovertemplate:
          viewType === "weekly"
            ? "<b>%{x}</b><br>" +
              `${graph.yAxisTitle}: %{y:.2f}${graph.id === "percentage" ? "%" : ""}<br>` +
              "Count: %{customdata}<extra></extra>"
            : `${graph.yAxisTitle}: %{y:.2f}${graph.id === "percentage" ? "%" : ""}<br>` +
              "Date: %{x}<br>" +
              "Count: %{customdata}<extra></extra>",
        customdata: data.map((item) => item.count),
      },
    ];

    // Create Plotly layout
    const layout = {
      title: {
        text: graph.title,
        font: {
          size: 14,
          weight: 600,
        },
      },
      height: 300,
      margin: {
        l: 50,
        r: 20,
        b: 50,
        t: 40,
        pad: 4,
      },
      xaxis: {
        title: "",
        tickangle: 0,
        tickfont: {
          size: 10,
        },
        type: "category",
        showticklabels: true,
        // Show fewer ticks when there are many values
        nticks:
          viewType === "daily" ? Math.min(10, xValues.length) : undefined,
        // Show every nth tick based on data length
        tickmode: viewType === "daily" ? "auto" : "linear",
        // Ensure first and last dates are always shown
        showfirstticklabel: true,
        showlastticklabel: true,
      },
      yaxis: {
        title: {
          text: graph.yAxisTitle,
          font: {
            size: 12,
            weight: 600,
          },
        },
        ...yAxisConfig,
        tickfont: {
          size: 10,
        },
      },
      bargap: 0.4,
      hovermode: "closest",
    };

    // Plotly config
    const config = {
      responsive: true,
      displayModeBar: false,
      toImageButtonOptions: {
        format: "png",
        filename: `${graph.id}_chart`,
        height: 400,
        width: 700,
        scale: 2,
      },
    };

    return (
      <div>
        <Plot
          data={plotData as any}
          layout={layout as any}
          config={config as any}
          style={{ width: "100%", height: "300px" }}
        />
      </div>
    );
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-4">
        <div className="flex justify-center items-center h-[600px]">
          <Spin size="large" />
        </div>
      </div>
    );
  }

  // Helper function to convert ISO week format to date using dayjs
  const getDateFromISOWeek = (isoWeekString: string): Date | null => {
    try {
      // Parse year and week from format "YYYY-WW"
      const [yearStr, weekStr] = isoWeekString.split("-");
      const year = parseInt(yearStr, 10);
      const week = parseInt(weekStr, 10);

      if (isNaN(year) || isNaN(week)) {
        return null;
      }

      // Use dayjs to get the date of the first day of the week
      // The week parameter in dayjs.week() is 1-indexed
      const date = dayjs().year(year).isoWeek(week).startOf("isoWeek").toDate();
      return date;
    } catch (error) {
      console.error("Error parsing ISO week:", isoWeekString, error);
      return null;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-4">
      {/* 3x3 Grid Layout for Statistical Graphs */}
      <div className="grid grid-cols-3 gap-4">
        {/* Row 1 */}
        {/* Mean Values */}
        <div className="bg-gray-50 rounded-lg p-3">
          <h4 className="text-base font-medium text-[#333] mb-3">Average Mean</h4>
          {renderGraph(graphs[0])}
        </div>

        {/* Median Values */}
        <div className="bg-gray-50 rounded-lg p-3">
          <h4 className="text-base font-medium text-[#333] mb-3">Average Median</h4>
          {renderGraph(graphs[1])}
        </div>

        {/* Standard Deviation */}
        <div className="bg-gray-50 rounded-lg p-3">
          <h4 className="text-base font-medium text-[#333] mb-3">Average Standard Deviation</h4>
          {renderGraph(graphs[2])}
        </div>

        {/* Row 2 */}
        {/* Percentage Analysis */}
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-base font-medium text-[#333]">Average in Spec</h4>
            <div className="flex items-center gap-2">
              <span className={`text-xs font-medium ${!showGoodPercentage ? 'text-gray-400' : 'text-green-600'}`}>
                Good %
              </span>
              <Switch
                size="small"
                checked={!showGoodPercentage}
                onChange={(checked) => setShowGoodPercentage(!checked)}
                style={{
                  backgroundColor: showGoodPercentage ? '#52c41a' : '#ff4d4f'
                }}
              />
              <span className={`text-xs font-medium ${showGoodPercentage ? 'text-gray-400' : 'text-red-600'}`}>
                Bad %
              </span>
            </div>
          </div>
          {renderGraph(graphs[3])}
        </div>

        {/* Q3 - Q1 Mean */}
        <div className="bg-gray-50 rounded-lg p-3">
          <h4 className="text-base font-medium text-[#333] mb-3">Q3 - Q1 Mean</h4>
          {renderGraph(graphs[4])}
        </div>

        {/* Q3 - Q1 Standard Deviation */}
        <div className="bg-gray-50 rounded-lg p-3">
          <h4 className="text-base font-medium text-[#333] mb-3">Q3 - Q1 Standard Deviation</h4>
          {renderGraph(graphs[5])}
        </div>

        {/* Row 3 */}
        {/* Median to IQR Ratio */}
        <div className="bg-gray-50 rounded-lg p-3">
          <h4 className="text-base font-medium text-[#333] mb-3">Median to IQR Ratio</h4>
          {renderGraph(graphs[6])}
        </div>

        {/* Cp */}
        <div className="bg-gray-50 rounded-lg p-3">
          <h4 className="text-base font-medium text-[#333] mb-3">Cp</h4>
          {renderGraph(graphs[7])}
        </div>

        {/* Cpk */}
        <div className="bg-gray-50 rounded-lg p-3">
          <h4 className="text-base font-medium text-[#333] mb-3">Cpk</h4>
          {renderGraph(graphs[8])}
        </div>
      </div>
    </div>
  );
};

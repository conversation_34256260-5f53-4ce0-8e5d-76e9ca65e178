import { useCallback, useRef, useState } from 'react';
import type { Node, Edge } from 'reactflow';
import type { CustomNodeData } from '../components/Custom Workflow/CustomNodes/types';
import { postRequest } from '../utils/apiHandler';

export type ExecuteWorkflowStatus = 'idle' | 'loading' | 'success' | 'error';

interface UseExecuteWorkflowReturn {
  status: ExecuteWorkflowStatus;
  result: unknown | null;
  error: string | null;
  execute: (workflowId: number, nodes: Node[], edges: Edge[], systems: any) => Promise<'success' | 'error'>;
  cancel: () => void;
}

export const useExecuteWorkflow = (): UseExecuteWorkflowReturn => {
  const [status, setStatus] = useState<ExecuteWorkflowStatus>('idle');
  const [result, setResult] = useState<unknown | null>(null);
  const [error, setError] = useState<string | null>(null);
  const controllerRef = useRef<AbortController | null>(null);

  const cancel = useCallback(() => {
    if (controllerRef.current) {
      controllerRef.current.abort();
      controllerRef.current = null;
    }
    setStatus('idle');
    setResult(null);
    setError(null);
  }, []);

  const execute = useCallback(async (workflowId: number, nodes: Node[], edges: Edge[], systems: any): Promise<'success' | 'error'> => {
    if (controllerRef.current) {
      controllerRef.current.abort();
      controllerRef.current = null;
    }

    const payload = {
      workflow: {
        user_id: 1,
        name: 'Custom Workflow',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        workflow_type: 'custom',
      },
      workflowStructure: {
        hierarchy: {
          nodes: nodes.map(node => ({
            ...node,
            data: {
              ...node.data,
              settings: (node.data as CustomNodeData).settings
            }
          })),
          edges,
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      workflowComponents: nodes.map((node) => ({
        component: node.id,
        type: (node.data as CustomNodeData).nodeType,
        settings: (node.data as CustomNodeData).settings,
        operationId: (node.data as CustomNodeData).operationId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })),
      systems: {
        names: systems?.[0]?.systems || [],
      },
    };

    const controller = new AbortController();
    controllerRef.current = controller;

    setStatus('loading');
    setResult(null);
    setError(null);

    try {
      const route = `/custom-workflow/${workflowId}/preview-workflow`;
      const response = await postRequest(route, payload);
      if (controller.signal.aborted) return 'error';
      setResult(response?.data ?? null);
      setStatus('success');
      return 'success';
    } catch (err: any) {
      if (err?.name === 'CanceledError' || err?.name === 'AbortError') return 'error';
      setError('Execution Failed');
      setStatus('error');
      return 'error';
    } finally {
      if (controllerRef.current === controller) {
        controllerRef.current = null;
      }
    }
  }, []);

  return { status, result, error, execute, cancel };
};

export default useExecuteWorkflow;

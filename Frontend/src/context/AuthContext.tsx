import React, { createContext, useContext, useState, useEffect } from 'react';
import Cookies from "js-cookie";
import { AuthResult, OnboardingStatus } from '../routes/types';
interface AuthContextType {
  authState: AuthResult;
  loading: boolean;

  login: (token: string, userData: any) => Promise<void>;
  logout: () => void;
  checkAuth: () => Promise<void>;
  updateFirstLoginStatus: (value: boolean) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [loading, setLoading] = useState(true);
  const [authState, setAuthState] = useState<AuthResult>({
    isAuthenticated: false,
    user: null
  });

  const checkAuth = async () => {
    try {
      console.log("Checking auth...");
      const token = Cookies.get('token');
      if (!token) {
        setAuthState({
          isAuthenticated: false,
          user: null
        });
        return;
      }

      const storedUserData = localStorage.getItem('userData');
      const userData = storedUserData ? JSON.parse(storedUserData) : null;

      console.log("User data from localStorage:", userData);

      setAuthState({
        isAuthenticated: true,
        user: userData || {
          id: '1',
          first_name: 'User',
          onboarding: 'not_started',
          is_first_login: false,
        }
      });
    } catch (error) {
      console.error('Auth check failed:', error);
      setAuthState({
        isAuthenticated: false,
        user: null
      });
    } finally {
      setLoading(false);
    }
  };

  // Add a function to update the is_first_login flag
  const updateFirstLoginStatus = (value: boolean) => {
    if (authState.user) {
      const updatedUser = {
        ...authState.user,
        is_first_login: value
      };

      // Update localStorage
      const userData = JSON.parse(localStorage.getItem('userData') || '{}');
      userData.is_first_login = value;
      localStorage.setItem('userData', JSON.stringify(userData));

      // Update state
      setAuthState({
        ...authState,
        user: updatedUser
      });
    }
  };

  const login = async (token: string, userData: any) => {
    console.log('userData received in login:', userData)
    console.log('userData keys:', Object.keys(userData))
    console.log('refreshToken in userData:', userData.refreshToken)

    try {
      Cookies.set('token', token);

      // Check if refresh token is already in cookies (set by backend)
      const existingRefreshToken = Cookies.get('refreshToken');
      console.log('Existing refresh token in cookies:', !!existingRefreshToken);

      // Also save refresh token if provided in response data
      if (userData.refreshToken) {
        Cookies.set('refreshToken', userData.refreshToken);
        console.log("✅ Refresh token saved from response data");
      } else {
        console.log("❌ No refreshToken found in userData");
        console.log("Available fields:", Object.keys(userData));
      }

      // Final check - what cookies do we have now?
      setTimeout(() => {
        const finalRefreshToken = Cookies.get('refreshToken');
        const finalToken = Cookies.get('token');
        console.log('Final cookie check:', {
          hasToken: !!finalToken,
          hasRefreshToken: !!finalRefreshToken,
          allCookies: document.cookie
        });
      }, 100);

      console.log("Login called, Token-", token);
      await new Promise((resolve) => setTimeout(resolve, 0));

      const onboardingStatus = userData.onboarding || 'not_started';
      localStorage.setItem(
        "userData",
        JSON.stringify({
          id: userData.id,
          first_name: userData.first_name,
          last_name: userData.last_name,
          onboarding: onboardingStatus,
          role: userData.role,
          tenant_id: userData.tenant_id,
          is_first_login: userData.is_first_login || false,
          tenant_type: userData?.tenant_type,
          email: userData?.email,
        })
      );

      setAuthState({
        isAuthenticated: true,
        user: {
          id: userData.id,
          first_name: userData.first_name || 'User',
          onboarding: onboardingStatus,
          role: userData.role,
          selected_systems: userData.selected_systems || [],
          tenant_id: userData.tenant_id,
          is_first_login: userData.is_first_login || false,
          tenant_type: userData?.tenant_type,
          email: userData?.email
        },
      });

      // Start automatic token refresh
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  };

  const logout = () => {
    // Industry standard logout - no timers to clear
    Cookies.remove('token');
    Cookies.remove('refreshToken');
    Cookies.remove('chat_messages');
    localStorage.removeItem('userData');
    setAuthState({
      isAuthenticated: false,
      user: null
    });
  };

  useEffect(() => {
    checkAuth();
  }, []);

  return (
    <AuthContext.Provider value={{ authState, loading, login, logout, checkAuth, updateFirstLoginStatus }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
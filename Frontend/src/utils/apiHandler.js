import axios from "axios";
import Cookies from 'js-cookie';

// Token refresh function
const refreshToken = async () => {
    try {
        const refreshTokenValue = Cookies.get('refreshToken');
        console.log('Refresh token check:', {
            hasRefreshToken: !!refreshTokenValue,
            refreshTokenLength: refreshTokenValue?.length,
            allCookies: document.cookie
        });

        if (!refreshTokenValue) {
            throw new Error('No refresh token available');
        }

        console.log('Attempting to refresh token...', {
            refreshTokenExists: !!refreshTokenValue,
            refreshTokenLength: refreshTokenValue?.length
        });

        // Create a new axios instance to avoid interceptor loops
        const refreshAxios = axios.create({
            baseURL: process.env.REACT_APP_API_BASE_URL,
            timeout: 10000, 
            withCredentials: true
        });

        const response = await postRequest('/auth/token/refresh', {
            refreshToken: refreshTokenValue
        });

        if (response?.data?.status === 200) {
            const newAccessToken = response?.data?.data?.accessToken;
            console.log('Token refreshed successfully');         
            // Update the token cookie
            Cookies.set('token', newAccessToken);

            return newAccessToken;
        } else {
            throw new Error('Token refresh failed: ' + response?.data?.message);
        }
    } catch (error) {
        console.error('Token refresh error:', error);

        // Create a serializable error for Redux
        const serializableError = {
            message: error?.message || 'Token refresh failed',
            status: error.response?.status,
            code: error.response?.data?.error?.code
        };

        // Don't redirect here, let the interceptor handle it
        throw serializableError;
    }
};

// Setup axios interceptor for automatic token refresh
let isRefreshing = false;
let failedQueue = [];

const processQueue = (error, token = null) => {
    failedQueue.forEach(prom => {
        if (error) {
            prom.reject(error);
        } else {
            prom.resolve(token);
        }
    });

    failedQueue = [];
};

axios.interceptors.response.use(
    (response) => response,
    async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
            const errorCode = error?.response?.data?.error?.code;
            const errorMessage = error?.response?.data?.message;

            console.log('🚨 401 Error detected - Token expired!', {
                errorCode,
                errorMessage,
                url: originalRequest.url,
                method: originalRequest.method,
                isRefreshing,
                hasRefreshToken: !!Cookies.get('refreshToken'),
                fullErrorData: error.response.data,
                responseStatus: error.response.status
            });

            // Check if it's a token expired error (either from new or legacy system)
            // Also handle any 401 error that might be token-related
            if (errorCode === 'TOKEN_EXPIRED' ||
                errorMessage === 'Invalid token' ||
                errorMessage === 'Token expired' ||
                errorMessage === 'jwt expired' ||
                // errorMessage?.toLowerCase().includes('token') ||
                // errorMessage?.toLowerCase().includes('expired') ||
                !errorCode) { // Handle 401s without specific error codes
                if (isRefreshing) {
                    console.log('Token refresh in progress, queuing request...');
                    // Queue this request while refresh is in progress
                    return new Promise((resolve, reject) => {
                        failedQueue.push({ resolve, reject });
                    }).then(() => {
                        console.log('Processing queued request after refresh...');
                        // Update the authorization header with new token
                        originalRequest.headers.Authorization = `Bearer ${Cookies.get('token')}`;
                        return axios(originalRequest);
                    }).catch(err => {
                        console.error('Queued request failed:', err);
                        return Promise.reject(err);
                    });
                }

                originalRequest._retry = true;
                isRefreshing = true;

                try {
                    console.log('🔄 Starting token refresh...');
                    const newToken = await refreshToken(); 
                    console.log('✅ Token refresh successful! New token length:', newToken?.length);
                    processQueue(null, newToken);

                    // Update the original request with new token
                    originalRequest.headers.Authorization = `Bearer ${newToken}`;
                    console.log('🔄 Retrying original request with new token...', {
                        url: originalRequest.url,
                        method: originalRequest.method
                    });

                    // Retry the original request
                    const retryResponse = await axios(originalRequest);
                    console.log('✅ Original request succeeded after token refresh');
                    return retryResponse;
                } catch (refreshError) {
                    console.error('❌ Token refresh failed:', {
                        error: refreshError?.message,
                        response: refreshError.response?.data,
                        status: refreshError.response?.status
                    });
                    processQueue(refreshError, null);

                    // Clear tokens and redirect to login
                    Cookies.remove('token');
                    Cookies.remove('refreshToken');
                    // alert('Your session has expired. Please login again.');
                    window.location.href = '/login';

                    // Create a serializable error for Redux
                    const serializableError = {
                        message: refreshError?.message || 'Token refresh failed',
                        status: refreshError.response?.status,
                        code: refreshError.response?.data?.error?.code
                    };
                    return Promise.reject(serializableError);
                } finally {
                    isRefreshing = false;
                    console.log('🔄 Token refresh process completed, isRefreshing set to false');
                }
            } else if (errorCode === 'SECURITY_VIOLATION') {
                // alert('Security validation failed. Please login again.');
                Cookies.remove('token');
                Cookies.remove('refreshToken');
                window.location.href = '/login';
            } else if (errorCode === 'REFRESH_TOKEN_EXPIRED' ||
                       errorMessage === 'Refresh token expired' ||
                       errorMessage === 'Refresh token has been revoked') {
                console.log('🚨 Refresh token expired - automatic logout');

                // Refresh token expired, need to login again
                Cookies.remove('token');
                Cookies.remove('refreshToken');
                Cookies.remove('chat_messages');
                localStorage.removeItem('userData');

                // alert('Your session has expired. Please login again.');
                window.location.href = '/login';
            }
        }

        // Create a serializable error for Redux
        const serializableError = {
            message: error?.response?.data?.message || error?.message  || 'Request failed',
            status: error.response?.status,
            code: error.response?.data?.error?.code,
            url: error.config?.url
        };
        return Promise.reject(serializableError);
    }
);

export const postRequest = async (route, data, isLocal = false) => {
    let url;
    if (isLocal) {
        url = "http://localhost:8080/api/v1" + route;
    } else {
        url = process.env.REACT_APP_API_BASE_URL + route;
    }
    //   let url = process.env.REACT_APP_API_BASE_URL + route;
    const token = Cookies.get("token");

    try {
        // Check if token is expired before making request
        if (token) {
            try {
                const tokenPayload = JSON.parse(atob(token.split('.')[1]));
                const currentTime = Math.floor(Date.now() / 1000);
                const isExpired = tokenPayload.exp <= currentTime;

                console.log(`Making POST request to: ${route}`, {
                    hasToken: !!token,
                    tokenLength: token?.length,
                    tokenExpiry: new Date(tokenPayload.exp * 1000).toISOString(),
                    isTokenExpired: isExpired,
                    timeUntilExpiry: (tokenPayload.exp - currentTime) + ' seconds'
                });

                if (isExpired) {
                    console.log('🚨 Token is expired before making request!');
                }
            } catch (decodeError) {
                console.log('Could not decode token:', decodeError?.message);
            }
        }

        return await axios.post(url, data, {
            headers: { Authorization: `Bearer ${token}` },
        });
    } catch (error) {
        console.error(`❌ POST request failed for ${route}:`, {
            status: error.response?.status,
            message: error.response?.data?.message,
            errorCode: error.response?.data?.error?.code,
            fullErrorData: error.response?.data,
            url: url
        });
        if (error?.response && error?.response?.data?.message === "Invalid token") {
            console.error(`🚨 [INVALID TOKEN] Route: ${route}, Token: ${token}`);
        }

        // Create a serializable error for Redux
        const serializableError = {
            message: error?.message || error?.response?.data?.message || 'Request failed',
            status: error.response?.status,
            code: error.response?.data?.error?.code,
            url: url
        };
        throw serializableError;
    }
};


export const putRequest = async (route, data, isLocal = false) => {
    let url;
    if (isLocal) {
        url = "http://localhost:8080/api/v1" + route;
    } else {
        url = process.env.REACT_APP_API_BASE_URL + route;
    }
    // let url = process.env.REACT_APP_API_BASE_URL + route
    const token = Cookies.get("token");
    return await axios.put(url, data
        , {
            headers: {
                Authorization: `Bearer ${token}`
            }
        }
    );
}

export const getRequest = async (route, params, isLocal = false) => {
    let url;
    if (isLocal) {
        url = "http://localhost:8080/api/v1" + route;
    } else {
        url = process.env.REACT_APP_API_BASE_URL + route;
    }
    // let url = process.env.REACT_APP_API_BASE_URL + route;
    const token = Cookies.get("token");
    console.log('token', token)

    try {
        return await axios.get(url, {
            params,
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
    } catch (error) {
        if (error?.response && error?.response?.data?.message === "Invalid token") {
            console.error(`🚨 [INVALID TOKEN] Route: ${route}, Token: ${token}`);
        }

        // Create a serializable error for Redux
        const serializableError = {
            message: error?.message || error?.response?.data?.message || 'Request failed',
            status: error.response?.status,
            code: error.response?.data?.error?.code
        };
        throw serializableError;
    }

}

export const patchRequest = async (route, data) => {
    let url = process.env.REACT_APP_API_BASE_URL + route;
    const token = Cookies.get("token");
    try {
        return await axios.patch(url, data, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
    } catch (error) {
        throw error;
    }
}

export const uploadFileRequest = async (route, formData) => {
    let url = process.env.REACT_APP_API_BASE_URL + route;
    const token = Cookies.get("token");

    return await axios.post(url, formData, {
        headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'multipart/form-data'
        }
    });
}

export const getOperationsRequest = async () => {
    let url = process.env.REACT_APP_API_BASE_URL + '/workflow/operations';
    const token = Cookies.get("token");

    return await axios.get(url, {
        headers: {
            Authorization: `Bearer ${token}`
        }
    });
}

export const getMlJobDataRequest = async (workflowId, mlJobId) => {
    // let url = 'http://*************:8080/api/update_results_workflow/jobdata/8';

    let url = process.env.REACT_APP_API_BASE_URL + `/update_results_workflow/jobdata/${workflowId}${mlJobId ? '/' + mlJobId : ''}`;
    const token = Cookies.get("token");

    return await axios.get(url, {
        headers: {
            Authorization: `Bearer ${token}`
        }
    });
}

export const deleteRequest = async (route) => {
    let url = process.env.REACT_APP_API_BASE_URL + route;
    const token = Cookies.get("token");

    return await axios.delete(url, {
        headers: {
            Authorization: `Bearer ${token}`
        }
    });
}
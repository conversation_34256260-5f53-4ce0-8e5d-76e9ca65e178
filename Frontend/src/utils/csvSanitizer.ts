/**
 * CSV Sanitization Utility
 * 
 * Prevents CSV Macro Injection attacks by sanitizing values that could be interpreted
 * as formulas by spreadsheet applications (Excel, Google Sheets, etc.)
 * 
 * OWASP Compliance: Follows OWASP WSTG v4.2 recommendations for CSV injection prevention
 * 
 * @see https://owasp.org/www-project-web-security-testing-guide/v42/4-Web_Application_Security_Testing/07-Input_Validation_Testing/07.7-Testing_for_CSV_Injection.html
 */

/**
 * Dangerous characters that can trigger formula execution in spreadsheet applications
 * These characters at the start of a cell value can be interpreted as formulas
 */
const DANGEROUS_START_CHARS = ['=', '+', '-', '@', '\t', '\r'];

/**
 * Sanitizes a value for safe CSV export by preventing formula injection attacks
 * 
 * OWASP Recommended Approach: Prepend single quote (') to values starting with dangerous characters
 * This forces spreadsheet applications to treat the cell as text instead of executable formula
 * 
 * @param value - The value to sanitize (can be any type)
 * @returns Sanitized value safe for CSV export
 * 
 * @example
 * sanitizeCsvValue("=SUM(A1:A10)") // Returns "'=SUM(A1:A10)"
 * sanitizeCsvValue("+2+3") // Returns "'+2+3"
 * sanitizeCsvValue("Normal text") // Returns "Normal text" (unchanged)
 * sanitizeCsvValue(123) // Returns 123 (unchanged)
 */
export function sanitizeCsvValue(value: any): any {
  // Handle null, undefined, or non-string values
  if (value === null || value === undefined || typeof value !== 'string') {
    return value;
  }

  // Check if value starts with any dangerous character
  const startsWithDangerousChar = DANGEROUS_START_CHARS.some(char => 
    value.startsWith(char)
  );

  // If value starts with dangerous character, prepend single quote to prevent formula execution
  if (startsWithDangerousChar) {
    return `'${value}`;
  }

  // Value is safe, return unchanged
  return value;
}

/**
 * Sanitizes an array of values for safe CSV export
 * 
 * @param values - Array of values to sanitize
 * @returns Array of sanitized values
 */
export function sanitizeCsvValues(values: any[]): any[] {
  return values.map(value => sanitizeCsvValue(value));
}

/**
 * Sanitizes a data object for safe CSV export
 * 
 * @param data - Object with key-value pairs to sanitize
 * @returns Object with sanitized values
 */
export function sanitizeCsvData(data: Record<string, any>): Record<string, any> {
  const sanitized: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(data)) {
    sanitized[key] = sanitizeCsvValue(value);
  }
  
  return sanitized;
}

/**
 * Sanitizes an array of data objects for safe CSV export
 * 
 * @param dataArray - Array of objects to sanitize
 * @returns Array of sanitized objects
 */
export function sanitizeCsvDataArray(dataArray: Record<string, any>[]): Record<string, any>[] {
  return dataArray.map(data => sanitizeCsvData(data));
}

{"system": "reactors", "x_feature": "DateTime", "y_feature": "Weight_Sensor", "all_features": ["minutes_from_start", "Equipment_No", "Phase", "batch_id", "step_id", "phase_step_id", "Yield", "DateTime", "FI-26", "PI-01", "RR-03", "RR-03_Loading", "TIC-04_PV", "TIC-04_SP", "TIC-04_OUT", "R-03_Loading", "R-03", "TIC-05_PV", "TIC-05_SP", "TIC-05_OUT", "R-04_LOADING", "R-04_PV_D", "R-05_Loading", "R-05_PV_D", "FIC-08_PV", "FIC-08_SP", "FIC-08_OUT", "R-07_Loading", "R-07_PV_D", "FIQ-07", "FIQ-03", "PIC-04_PV", "PIC-04_SP", "PIC-04_OUT", "DPI-04", "FIQ-08", "LIC-04_PV", "LIC-04_SP", "LIC-04_OUT", "LI-10_KG", "FIC-02_PV", "FIC-02_SP", "FIC-02_OUT", "FIQ-15", "FI-28", "FIQ-18", "PIC-03_PV", "PIC-03_SP", "PIC-03_OUT", "TI-50", "PI-14", "TIC-09_PV", "TIC-09_SP", "TIC-09_OUT", "FIC-10_PV", "FIC-10_SP", "FIC-10_OUT", "LI-16", "LI-16_KG", "AI-01", "LI-06_KG", "LI-32-KG", "R-10_Loading", "R-10_PV_D", "LS-02", "LI-07_KG", "FI-22", "LI-36_KG", "FIQ-33", "PIC-08_PV", "PIC-08_SP", "PIC-08_OUT", "LI-12_KG", "DPI-09", "DPI-01", "DI-03", "FITI-01", "FIC-07_PV", "FIC-07_SP", "FIC-07_OUT", "FITI-02", "DPI-10", "DI-04", "FITI-03", "DI-05", "FITI-04", "PIC-05_PV", "PIC-05_SP", "PIC-05_OUT", "DPI-03", "FIC-13_PV", "FIC-13_SP", "FIC-13_OUT", "FIC-01_PV", "FIC-01_SP", "FIC-01_OUT", "FIQ-35", "LIC-03_PV", "LIC-03_SP", "LIC-03_OUT", "LI-23_KG", "LI-19_KG", "TIC-07_PV", "TIC-07_SP", "TIC-07_OUT", "FIQ-09", "FIC-15_PV", "FIC-15_SP", "FIC-15_OUT", "TI-41", "FIQ-36", "FIQ-23", "TI-42", "TIC-08_PV", "TIC-08_SP", "TIC-08_OUT", "PV-01", "FIQ-16", "FITI-05", "LI-22_KG", "LI-24_KG", "LI-25_KG", "LI-27_KG", "LI-28_KG", "LIC-05_PV", "LIC-05_SP", "LIC-05_OUT", "LI-35_KG", "FIC-16_PV", "LI-08_KG", "PI-31", "LI-30_KG", "FIC-17_PV", "FIC-17_SP", "FIC-17_OUT", "FIQ-24", "LI-37", "LI-37_KG", "PI-34", "PI-35", "LI-33", "LI-33_KG", "TI-55", "TIC-10_PV", "TIC-10_SP", "TIC-10_OUT", "PI-36", "TI-56", "LI-34_KG", "FI-01", "FLV-01", "PSH-01", "R-01_PV_D", "RR-03_Loading.1", "TI-01", "TI-02", "WI-01", "XV-01", "FI-02", "PI-02", "R-03_PV_D", "RR-05", "TI-03", "TI-04", "WI-02", "XV-02", "DI-02", "FI-03", "FIC-05_PV", "FIC-05_SP", "FIC-05_OUT", "FLV-02", "LI-01", "PI-12", "PSH-02", "R-04_PV_D.1", "RR-07", "TI-05", "TIC-06_PV", "TIC-06_SP", "TIC-06_OUT", "WI-05", "XV-03", "XV-04", "XV-05", "FI-05", "FI-19", "PI-03", "PSH-03", "R-05_PV_D.1", "RR-09", "TI-06", "TI-07", "WI-06", "XV-06", "XV-07", "FI-06", "FI-16", "FI-07", "FIC-18_PV", "FIC-18_SP", "FIC-06_OUT", "FLV-04", "PI-13", "PI-04", "PH-03", "R-06_PV_D", "RR-11", "TI-08", "TI-09", "TI-10", "WI-03", "XV-08", "FI-08", "FI-17", "FIC-11_PV", "FIC-11_SP", "FIC-11_OUT", "FLV-05", "PI-05", "PSH-04", "R-07_PV_D.1", "RR-13", "TI-11", "TI-12", "WI-04", "XV-09", "XV-10", "FI-23", "FI-25", "FI-20", "FI-10", "FI-11", "LI-03", "LIC-01_PV", "LIC-06_SP", "LIC-06_OUT", "PI-07", "PI-08", "TI-16", "TI-17", "TI-18", "TIC-01_PV", "TIC-12_SP", "TIC-12_OUT", "FI-18", "FI-12", "FI-21", "FI-13", "LI-04", "LIC-02_PV", "LIC-07_SP", "LIC-07_OUT", "PI-09", "TI-19", "TI-20", "TI-21", "TI-22", "TIC-02_PV", "TIC-13_SP", "TIC-13_OUT", "TIC-03_PV", "TIC-14_SP", "TIC-14_OUT", "FIC-04_PV", "FIC-19_PV", "FIC-19_SP", "FIC-12_PV", "FIC-12_SP", "FIC-12_OUT", "LI-09", "LS-01", "P-02_PV_D", "P-02_LOADING", "P-03_PV_D", "P-03_LOADING", "PH-01", "PI-16", "PSH-05", "TI-29", "XV-11", "DI-06", "DI-01", "FI-14", "FI-27", "FI-15", "FLV-03", "LI-10", "PI-10", "PI-17", "PIC-02_PV", "PIC-02_SP", "PIC-02_OUT", "TI-23", "TI-24", "TI-30", "TI-25", "TI-26", "WFE-01_PV_D", "WFE-01_LOADING", "XV-12", "Date", "QC_Result-1", "QC_Result-2", "QC_Result-3", "QC_Result-4", "QC_Result-5", "DateTime_1", "XV-01_1", "XV-02_1", "XV-22", "XV-23", "PIC-01_PV", "PIC-01_SP", "PIC-01_OUT", "PIC-06_PV", "PIC-06_SP", "PIC-06_OUT", "FLV-06", "FLV-07", "XV-24", "FIQ-33_1", "FLV-08", "XV-25", "XV-26", "XV-27", "PSH-04_1", "PSH-05_1", "XV-05_1", "R-04_PV_D_1", "RR-07_1", "PSH-06", "PI-55", "FLV-03_1", "XV-07_1", "XV-12_1", "PSH-07", "PSH-08", "FI-26_standardized", "FI-02_standardized", "Weight_Sensor", "Temperature_Sensor", "Phase_Actuals", "phase_actuals_change", "Step_ID_phase_actuals", "Step_ID_", "batch_status", "FI-01_standardized"], "batch_identifier": "batch_id", "phase_identifier": "Phase", "validation": [{"column_name": "Weight_Sensor", "data_type": "numeric"}, {"column_name": "Weight_Sensor", "data_type": "numeric"}]}
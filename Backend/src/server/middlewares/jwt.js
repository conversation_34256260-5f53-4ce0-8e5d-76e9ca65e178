
import jwt from 'jsonwebtoken';
import { generateResponse } from "../utils/commonResponse.js";
import { generateSecureAccessToken, verifySecureToken } from '../services/enhancedJWT.js';
import { logSecurityEvent } from '../utils/securityUtils.js';
import { loadKeyPairFromEnv } from '../utils/keyGenerator.js';
import redisClient from '../cache/redisClient.js';
import 'dotenv/config'

// Load ES256 key pair for legacy function
let legacyKeyPair;
try {
  legacyKeyPair = loadKeyPairFromEnv();
} catch (error) {
  console.error('❌ Failed to load ES256 key pair for legacy function:', error.message);
}

// Legacy function for backward compatibility
const generateToken = (userData) =>{
    if (!legacyKeyPair) {
        throw new Error('ES256 key pair not loaded. Please generate keys first.');
    }
    return jwt.sign(userData, legacyKeyPair.privateKey, {
        algorithm: 'ES256' // Use ES256 for stronger security
    })
}
// Enhanced token generation with security context
const generateSecureToken = (userData, req) => {
    return generateSecureAccessToken(userData, req);
}

// Function to generate a token for FastAPI internal communication
const generateInternalToken = () => {
  const payload = {
    service: "node-service",   
    iat: Math.floor(Date.now() / 1000)
  };

  if (!legacyKeyPair) {
    throw new Error('ES256 key pair not loaded. Please generate keys first.');
  }

  return jwt.sign(payload, legacyKeyPair.privateKey, {
    algorithm: "ES256",
    expiresIn: "30m"
  });
};

// Enhanced authentication middleware with security validation
const authenticateToken = async (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const cookieToken = req.cookies?.token;

    const token = authHeader && authHeader.split(' ')[1] || cookieToken;

    if (!token) {
        return generateResponse(res, 401, 'Access token required')
    }

    try {
        // Use enhanced verification with security context validation
        const user = await verifySecureToken(token, req, {
            expectedType: 'access',
            strictIP: false, // Allow IP flexibility for mobile users
            strictFingerprint: true, // Require exact browser match
            redisClient: redisClient // Pass Redis client for blacklist checking
        });

        req.user = user;
        next();
    } catch (error) {
      console.log('errorssssssssss', error)
        logSecurityEvent('AUTHENTICATION_FAILED', {
            error: error.message,
            ip: req.ip,
            userAgent: req.headers['user-agent']
        });

        if (error.message === 'Token expired') {
            return generateResponse(res, 401, 'Token expired', null, null, {
                code: 'TOKEN_EXPIRED',
                message: 'Please refresh your token'
            });
        } else if (error.message === 'Token security validation failed') {
            return generateResponse(res, 403, 'Security validation failed', null, null, {
                code: 'SECURITY_VIOLATION',
                message: 'Token security validation failed'
            });
        } else {
            return generateResponse(res, 403, 'Invalid token');
        }
    }
};

// Middleware to verify OTP token during 2FA login
const OtpToken = async (req, res, next,type = 'verification_token') => {
    const authHeader = req.headers['verification_token'];
    const cookieToken = req.cookies?.verification_token;

    const token = authHeader && authHeader.split(' ')[1] || cookieToken;

    if (!token) {
        return generateResponse(res, 401, 'Access token required')
    }

    try {
        // Use enhanced verification with security context validation
        const user = await verifySecureToken(token, req, {
            expectedType: 'access',
            strictIP: false, // Allow IP flexibility for mobile users
            strictFingerprint: true, // Require exact browser match
            redisClient: redisClient // Pass Redis client for blacklist checking
        });

        req.user = user;
        next();
    } catch (error) {
      console.log('errorssssssssss', error)
        logSecurityEvent('AUTHENTICATION_FAILED', {
            error: error.message,
            ip: req.ip,
            userAgent: req.headers['user-agent']
        });

        if (error.message === 'Token expired') {
            return generateResponse(res, 401, 'Token expired', null, null, {
                code: 'TOKEN_EXPIRED',
                message: 'Please refresh your token'
            });
        } else if (error.message === 'Token security validation failed') {
            return generateResponse(res, 403, 'Security validation failed', null, null, {
                code: 'SECURITY_VIOLATION',
                message: 'Token security validation failed'
            });
        } else {
            return generateResponse(res, 403, 'Invalid token');
        }
    }
};

const skipSuperAdminActivity = (req, res, next) => {
  if (req.user?.isSuperAdmin) {
    return generateResponse(res, 200, 'Super admin activity not tracked');
  }
  next();
};

export {
    generateToken,
    generateSecureToken,
    authenticateToken,
    skipSuperAdminActivity,
    generateInternalToken,
    OtpToken
  }
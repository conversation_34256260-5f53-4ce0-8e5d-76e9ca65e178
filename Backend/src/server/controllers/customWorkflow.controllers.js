/**
 * Controller for custom workflow operations.
 * 
 * @module customWorkflowController
 */
import Workflow from '../models/workflow.model.js';
import WorkflowStructure from '../models/workflowStructure.model.js';
import WorkflowComponents from '../models/workflowComponents.model.js';
import Operations from '../models/operation.model.js';
import { sequelize } from "../database/dbConnection.js";
import { generateResponse } from "../utils/commonResponse.js";
import { Op } from 'sequelize';
import { prepareFunctionCode, prepareScriptCode, prepareScriptConfig, getNodesFromStructure } from '../utils/scriptGenerationTools.js';
import { executePreviewNode, executeRunScript } from '../utils/windmillApi.js';
import axios from 'axios';
import { get } from 'mongoose';

/**
 * Save or update custom workflow data.
 * Updates workflow, components, and structure in a transaction.
 *
 * @async
 * @function saveCustomWorkflow
 * @param {import('express').Request} req - Express request object.
 * @param {import('express').Response} res - Express response object.
 * @returns {Promise<void>}
 */
export const saveCustomWorkflow = async (req, res) => {
    const { id } = req.params;
    const { workflows, workflowComponents, workflowStructure } = req.body;
    const { id: userId } = req.user;

    const transaction = await sequelize.transaction();

    try {
        // Check if workflow exists and is custom type
        const existingWorkflow = await Workflow.findByPk(id);

        if (!existingWorkflow) {
            await transaction.rollback();
            return generateResponse(res, 404, 'Workflow not found');
        }

        if (existingWorkflow.workflow_type !== 'custom') {
            await transaction.rollback();
            return generateResponse(res, 400, 'Workflow is not a custom workflow');
        }

        // Update workflow data
        if (workflows && workflows.length > 0) {
            for (const workflowData of workflows) {
                await Workflow.update(
                    { ...workflowData, updated_at: new Date() },
                    { where: { id }, transaction }
                );
            }
        }

        // Update workflow components
        if (workflowComponents && workflowComponents.length > 0) {
            // Delete existing components
            await WorkflowComponents.destroy({
                where: { workflow_id: id },
                transaction
            });

            // Insert new components
            const componentsToInsert = workflowComponents.map(component => ({
                ...component,
                workflow_id: id,
                created_at: new Date(),
                updated_at: new Date()
            }));

            await WorkflowComponents.bulkCreate(componentsToInsert, { transaction });
        }

        // Update workflow structures
        if (workflowStructure && workflowStructure.length > 0) {
            // Delete existing structures
            await WorkflowStructure.destroy({
                where: { workflow_id: id },
                transaction
            });

            // Insert new structures
            const structuresToInsert = workflowStructure.map(structure => ({
                ...structure,
                workflow_id: id,
                created_at: new Date(),
                updated_at: new Date()
            }));

            console.log('structuresToInsert', structuresToInsert)
            await WorkflowStructure.bulkCreate(structuresToInsert, { transaction });
        }

        await transaction.commit();

        // Extract nodes from workflow structure
        const nodes = await getNodesFromStructure(workflowStructure);
        console.log('Extracted nodes from structure:', nodes);

        // Prepare function code from workflow components
        // const functionCode = await prepareScriptCode(nodes);

        // TODO: Format payload for AIML API call
        // const aimlPayload = formatForAimlApi({ workflows, workflowComponents, workflowStructures });
        // await callAimlApi(aimlPayload);

        return generateResponse(res, 200, 'Custom workflow updated successfully', {
            workflowId: id,
            updated: true
        });

    } catch (error) {
        await transaction.rollback();
        console.error('Error updating custom workflow:', error);
        return generateResponse(res, 500, 'Failed to update custom workflow');
    }
};

/**
 * Get detailed workflow data including components and structure.
 *
 * @async
 * @function getDetailedWorkflowData
 * @param {import('express').Request} req - Express request object.
 * @param {import('express').Response} res - Express response object.
 * @returns {Promise<void>}
 */
export const getDetailedWorkflowData = async (req, res) => {
    const { id } = req.params;

    try {
        // Check if workflow exists and is custom type
        const workflow = await Workflow.findOne({
            where: {
                id,
                workflow_type: 'custom'
            }
        });

        if (!workflow) {
            return generateResponse(res, 404, 'Custom workflow not found');
        }

        // Fetch related data from all tables
        const [workflowComponents, workflowStructures] = await Promise.all([
            WorkflowComponents.findAll({
                where: { workflow_id: id }
            }),
            WorkflowStructure.findAll({
                where: { workflow_id: id }
            })
        ]);

        // Format the response data
        const detailedWorkflowData = {
            workflow: {
                id: workflow.id,
                name: workflow.name,
                description: workflow.description,
                workflow_type: workflow.workflow_type,
                user_id: workflow.user_id,
                folder_id: workflow.folder_id,
                created_at: workflow.created_at,
                updated_at: workflow.updated_at
            },
            components: workflowComponents.map(component => ({
                id: component.id,
                component: component.component,
                settings: component.settings,
                type: component.type,
                created_at: component.created_at,
                updated_at: component.updated_at
            })),
            structures: workflowStructures.map(structure => ({
                id: structure.id,
                hierarchy: structure.hierarchy,
                created_at: structure.created_at,
                updated_at: structure.updated_at
            }))
        };

        return generateResponse(res, 200, 'Detailed workflow data fetched successfully', detailedWorkflowData);

    } catch (error) {
        console.error('Error fetching detailed workflow data:', error);
        return generateResponse(res, 500, 'Failed to fetch detailed workflow data');
    }
};

/**
 * Preview execution of a workflow node.
 *
 * @async
 * @function previewWorkflowNode
 * @param {import('express').Request} req - Express request object.
 * @param {import('express').Response} res - Express response object.
 * @returns {Promise<void>}
 */
export const previewWorkflowNode = async (req, res) => {
    const { workflowId } = req.params;
    const { type, settings } = req.body;

    try {
        // Validate workflow exists and is custom type
        const workflow = await Workflow.findOne({
            where: {
                id: workflowId,
                workflow_type: 'custom'
            }
        });

        if (!workflow) {
            return generateResponse(res, 404, 'Custom workflow not found');
        }

        // Prepare script code from node data
        const scriptCode = await prepareScriptCode([{ data: { nodeType: type, settings } }]);

        // Prepare config.
        const scriptConfig = await prepareScriptConfig(
            [{ type, settings }]
        );

        // Execute the script via Windmill API
        const payload = {
            "args": {
                config: scriptConfig
            },
            "content": scriptCode,
            "language": "python3",
            "kind": "code",
            "dedicated_worker": false
        }

        // console.log('Payload for Windmill API:', payload);
        const previewResponse = await executePreviewNode(payload);

        return generateResponse(res, 200, 'Workflow executed successfully', {
            workflowId,
            // executionResult: windmillResponse.data,
            previewResponse
        });

    } catch (error) {
        console.error('Error executing workflow:', error);
        return generateResponse(res, 500, 'Failed to execute workflow');
    }
};

/**
 * Get user's custom workflows with optional search and limit.
 *
 * @async
 * @function getUserCustomWorkflows
 * @param {import('express').Request} req - Express request object.
 * @param {import('express').Response} res - Express response object.
 * @returns {Promise<void>}
 */
export const getUserCustomWorkflows = async (req, res) => {
    const { id: userId } = req.user;
    const { searchedWorkflow, limit = 6 } = req.query;

    try {
        let whereCondition = {
            user_id: userId,
            workflow_type: 'custom'
        };

        // Add search condition if searchedWorkflow is provided
        if (searchedWorkflow) {
            whereCondition.name = {
                [Op.iLike]: `%${searchedWorkflow}%`
            };
        }

        const workflows = await Workflow.findAll({
            where: whereCondition,
            // attributes: [
            //     'id', 'name', 'description', 'workflow_type', 
            //     'created_at', 'updated_at', 'folder_id'
            // ],
            order: [['updated_at', 'DESC']],
            limit: parseInt(limit)
        });

        const formattedWorkflows = workflows.map(workflow => ({
            id: workflow.id,
            name: workflow.name,
            description: workflow.description,
            workflow_type: workflow.workflow_type,
            folder_id: workflow.folder_id,
            created_at: workflow.created_at,
            updated_at: workflow.updated_at
        }));

        return generateResponse(res, 200, 'User custom workflows fetched successfully', {
            workflows: formattedWorkflows,
            total: formattedWorkflows.length,
            searchTerm: searchedWorkflow || null
        });

    } catch (error) {
        console.error('Error fetching user custom workflows:', error);
        return generateResponse(res, 500, 'Failed to fetch user custom workflows');
    }
};

/**
 * Get node-specific data for a workflow.
 *
 * @async
 * @function getWorkflowNodeData
 * @param {import('express').Request} req - Express request object.
 * @param {import('express').Response} res - Express response object.
 * @returns {Promise<void>}
 */
export const getWorkflowNodeData = async (req, res) => {
    const { workflowId } = req.params;

    try {
        // Validate workflow exists and is custom type
        const workflow = await Workflow.findOne({
            where: {
                id: workflowId,
                workflow_type: 'custom'
            }
        });

        if (!workflow) {
            return generateResponse(res, 404, 'Custom workflow not found');
        }

        // Fetch all workflow components (nodes) for this workflow
        const workflowComponents = await WorkflowComponents.findAll({
            where: { workflow_id: workflowId },
            order: [['created_at', 'ASC']]
        });

        // Format the node data into an array of objects
        const nodeData = workflowComponents.map(component => ({
            nodeId: component.id,
            component: component.component,
            type: component.type,
            settings: component.settings,
            created_at: component.created_at,
            updated_at: component.updated_at
        }));

        return generateResponse(res, 200, 'Workflow node data fetched successfully', {
            workflowId,
            nodes: nodeData,
            totalNodes: nodeData.length
        });

    } catch (error) {
        console.error('Error fetching workflow node data:', error);
        return generateResponse(res, 500, 'Failed to fetch workflow node data');
    }
};

/**
 * Run a script for a workflow using Windmill API.
 *
 * @async
 * @function runScript
 * @param {import('express').Request} req - Express request object.
 * @param {import('express').Response} res - Express response object.
 * @returns {Promise<void>}
 */
export const runScript = async (req, res) => {
    const { workflowId } = req.params;
    const { scriptHashId, payload } = req.body;

    try {
        // // Validate workflow exists and is custom type
        // const workflow = await Workflow.findOne({
        //     where: {
        //         id: workflowId,
        //         workflow_type: 'custom'
        //     }
        // });

        // if (!workflow) {
        //     return generateResponse(res, 404, 'Custom workflow not found');
        // }


        // console.log('Payload for Windmill API:', payload);
        const jobId = await executeRunScript(scriptHashId, payload);

        return generateResponse(res, 200, 'Workflow executed successfully', {
            workflowId,
            jobId
        });
    } catch (error) {
        console.error('Error running script:', error);
        return generateResponse(res, 500, 'Failed to run script');
    }
}

/**
 * Run a flow for a workflow using Windmill API.
 *
 * @async
 * @function runFlow
 * @param {import('express').Request} req - Express request object.
 * @param {import('express').Response} res - Express response object.
 * @returns {Promise<void>}
 */
export const runFlow = async (req, res) => {
    const { workflowId } = req.params;
    const { flowPath, payload } = req.body;

    try {
        // // Validate workflow exists and is custom type
        // const workflow = await Workflow.findOne({
        //     where: {
        //         id: workflowId,
        //         workflow_type: 'custom'
        //     }
        // });

        // if (!workflow) {
        //     return generateResponse(res, 404, 'Custom workflow not found');
        // }


        // console.log('Payload for Windmill API:', payload);
        const jobId = await executeRunScript(flowPath, payload);

        return generateResponse(res, 200, 'Workflow executed successfully', {
            workflowId,
            jobId
        });
    } catch (error) {
        console.error('Error running flow:', error);
        return generateResponse(res, 500, 'Failed to run flow');
    }
}

/**
 * Preview workflow code execution for a given structure.
 *
 * @async
 * @function previewWorkflowCode
 * @param {import('express').Request} req - Express request object.
 * @param {import('express').Response} res - Express response object.
 * @returns {Promise<void>}
 */
export const previewWorkflowCode = async (req, res) => {
    const { workflowId } = req.params;
    const { workflowStructure } = req.body;

    try {
        // Validate workflow exists and is custom type
        const workflow = await Workflow.findOne({
            where: {
                id: workflowId,
                workflow_type: 'custom'
            }
        });

        if (!workflow) {
            return generateResponse(res, 404, 'Custom workflow not found');
        }

        // Extract nodes from workflow structure
        const nodes = getNodesFromStructure(workflowStructure.hierarchy);
        
        // Prepare script code and config.
        const scriptCode = await prepareScriptCode(nodes);
        const scriptConfig = await prepareScriptConfig(nodes, workflowStructure.hierarchy);

        // Prepare payload for windmill API
        const payload = {
            "args": {
                config: scriptConfig
            },
            "content": scriptCode,
            "language": "python3",
            "kind": "code",
            "dedicated_worker": false
        }

        // console.log('Payload for Windmill API:', payload);
        const jobId = await executePreviewNode(payload);

        return generateResponse(res, 200, 'Workflow executed successfully', {
            workflowId,
            jobId
        });
    } catch (error) {
        console.error('Error running script:', error);
        return generateResponse(res, 500, 'Failed to run script');
    }
}
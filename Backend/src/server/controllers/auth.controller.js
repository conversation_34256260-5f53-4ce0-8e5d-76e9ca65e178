import crypto from 'crypto';
import { sendOtpEmail } from '../utils/mailer.js';
import otpStore from '../utils/otpStore.js';
import User from '../models/user.model.js';
import ActivityLog from '../models/activityLogs.model.js';
import { generateToken } from "../middlewares/jwt.js";
import { generateTokenPair } from "../services/enhancedJWT.js";
import redisClient from '../cache/redisClient.js';
import Roles from '../models/roles.model.js';
import { generateResponse } from "../utils/commonResponse.js";

export const sendOtp = async (req, res) => {
  const { email ,userData} = req.body;
  console.log('userData :', userData);
  if (!email) return res.status(400).json({ message: 'Email is required' });

  const otp = crypto.randomInt(100000, 999999).toString();
  otpStore.storeOtp(email, otp);

  try {
    await sendOtpEmail(email, otp ,userData);
    res.json({ message: 'OTP sent to email' });
  } catch (err) {
    res.status(500).json({ message: 'Failed to send OTP', error: err.message });
  }
};

export const verifyEmailOtp = async (req, res) => {
  const {userId, email, otp } = req.body;
  console.log('userId :', userId);
  if (!email || !otp) return res.status(400).json({ message: 'Email and OTP are required' });

  if (otpStore.verifyOtp(email, otp)) {
    if (!userId) {
      return generateResponse(res, 400, 'User ID and verification token are required');
    }
    
    // Get the user with their 2FA secret
    const user = await User.findOne({
      attributes: ['id', 'first_name', 'last_name', 'email', 'onboarding', 'tenant_id', 'selected_systems', 'two_factor_secret', 'two_factor_enabled'],
      where: { id: userId },
      include: [
        {
          model: Roles,
          as: 'roles',
          attributes: ['name', 'alias', 'rank'] 
        },
      ]
    });
    
    if (!user) {
      return generateResponse(res, 404, 'User not found');
    }
  
    // If verified, create user data and token
    const userData = {
      first_name: user.get('first_name'),
      last_name: user.get('last_name'),
      email: user.get('email'),
      id: user.get('id'),
      onboarding: user.get('onboarding'),
      tenant_id: user.get('tenant_id'),
      selected_systems: user.get('selected_systems'),
      role: user.get('roles')
    };
    
    try {
      // Generate secure token pair with enhanced security
      const tokenPair = generateTokenPair(userData, req);

      // Store refresh token in Redis with user context
      const refreshTokenKey = `refresh:${userData.id}:${tokenPair.refreshTokenId}`;
      const refreshTokenData = {
        userId: userData.id,
        createdAt: new Date().toISOString(),
        ip: req.ip,
        userAgent: req.headers['user-agent'] || 'unknown'
      };

      // Store refresh token for 7 days
      await redisClient.setEx(refreshTokenKey, 7 * 24 * 60 * 60, JSON.stringify(refreshTokenData));

      // Add tokens to user data
      userData.accessToken = tokenPair.accessToken;
      userData.refreshToken = tokenPair.refreshToken;
      userData.expiresIn = tokenPair.expiresIn;
      userData.tokenType = tokenPair.tokenType;

      // For backward compatibility, also set token field
      userData.token = tokenPair.accessToken;

      // Also include refresh token in response for frontend access
      userData.refreshToken = tokenPair.refreshToken;
      userData.accessToken = tokenPair.accessToken;
      userData.expiresIn = tokenPair.expiresIn;

      // Log the activity
      const newEntry = { user_id: userData.id, last_login: new Date() };
      const activityLog = new ActivityLog(newEntry);
      await activityLog.save();

      // Set cookies (non-httpOnly for backward compatibility)
      res.cookie('token', tokenPair.accessToken, {
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 1 * 60 * 1000 // 1 minute
      });

      res.cookie('refreshToken', tokenPair.refreshToken, {
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
      });

      return generateResponse(res, 200, 'OTP verified successfully', userData);

    } catch (tokenError) {
      console.error('Token generation error:', tokenError);
      return generateResponse(res, 500, 'Failed to generate secure tokens');
    }
  } else {
    res.status(400).json({ message: 'Invalid or expired OTP', verified: false });
  }
};

import { generateResponse } from "../utils/commonResponse.js";
import {
    generateTokenPair,
    refreshAccessToken,
    revokeRefreshToken,
    revokeAccessToken,
    verifySecureToken
} from '../services/enhancedJWT.js';
import { logSecurityEvent } from '../utils/securityUtils.js';
import redisClient from '../cache/redisClient.js';
import User from '../models/user.model.js';
import Roles from '../models/roles.model.js';

/**
 * Refresh access token using refresh token
 */
export const refreshToken = async (req, res) => {
    try {
        const { refreshToken } = req.body;
        console.log('refreshToken', refreshToken)

        if (!refreshToken) {
            logSecurityEvent('REFRESH_TOKEN_MISSING', {
                ip: req.ip,
                userAgent: req.headers['user-agent']
            });
            return generateResponse(res, 400, 'Refresh token is required');
        }

        console.log('Processing token refresh request...');

        // Refresh the access token
        const tokenData = await refreshAccessToken(refreshToken, req, redisClient);
        console.log('tokenData', tokenData)

        // Also update the cookie
        res.cookie('token', tokenData.accessToken, {
            httpOnly: false,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'lax',
            maxAge: 5 * 60 * 1000 // 1 minute (matching your updated expiry)
        });

        console.log('Token refresh successful');
        return generateResponse(res, 200, 'Token refreshed successfully', tokenData);

    } catch (error) {
        console.log('Token refresh error:', error.message);
        logSecurityEvent('TOKEN_REFRESH_REQUEST_FAILED', {
            error: error.message,
            ip: req.ip,
            userAgent: req.headers['user-agent']
        });

        if (error.message === 'Refresh token expired' || error.message.includes('expired')) {
            return generateResponse(res, 401, 'Refresh token expired', null, null, {
                code: 'REFRESH_TOKEN_EXPIRED',
                message: 'Please login again'
            });
        } else if (error.message === 'Invalid refresh token' || error.message.includes('invalid')) {
            return generateResponse(res, 401, 'Invalid refresh token', null, null, {
                code: 'INVALID_REFRESH_TOKEN',
                message: 'Please login again'
            });
        } else if (error.message === 'Token security validation failed') {
            return generateResponse(res, 403, 'Security validation failed', null, null, {
                code: 'SECURITY_VIOLATION',
                message: 'Token security validation failed'
            });
        } else if (error.message === 'Refresh token has been revoked') {
            return generateResponse(res, 403, 'Refresh token revoked', null, null, {
                code: 'TOKEN_REVOKED',
                message: 'Please login again'
            });
        } else {
            return generateResponse(res, 500, 'Token refresh failed', null, null, {
                code: 'REFRESH_FAILED',
                message: 'Internal server error during token refresh'
            });
        }
    }
};

/**
 * Logout user and revoke both access and refresh tokens
 */
export const logout = async (req, res) => {
    try {
        const { refreshToken, accessToken } = req.body;

        // Revoke access token if provided
        if (accessToken) {
            try {
                await revokeAccessToken(accessToken, redisClient);
            } catch (accessError) {
                console.log('Access token revocation failed:', accessError.message);
                // Continue with logout even if access token revocation fails
            }
        }

        // Revoke refresh token if provided
        if (refreshToken) {
            try {
                await revokeRefreshToken(refreshToken, redisClient);
            } catch (refreshError) {
                console.log('Refresh token revocation failed:', refreshError.message);
                // Continue with logout even if refresh token revocation fails
            }
        }

        logSecurityEvent('USER_LOGOUT', {
            userId: req.user?.id,
            ip: req.ip,
            hasAccessToken: !!accessToken,
            hasRefreshToken: !!refreshToken
        });

        // Clear cookies
        res.clearCookie('token');
        res.clearCookie('refreshToken');

        return generateResponse(res, 200, 'Logged out successfully');

    } catch (error) {
        logSecurityEvent('LOGOUT_FAILED', {
            userId: req.user?.id,
            error: error.message
        });

        // Even if token revocation fails, clear cookies and return success
        res.clearCookie('token');
        res.clearCookie('refreshToken');

        return generateResponse(res, 200, 'Logged out successfully');
    }
};

/**
 * Validate current token and return user info
 */
export const validateToken = async (req, res) => {
    try {
        // Token is already validated by middleware, just return user info
        const user = req.user;
        
        // Remove sensitive security info before sending
        const userInfo = {
            id: user.id,
            first_name: user.first_name,
            last_name: user.last_name,
            email: user.email,
            onboarding: user.onboarding,
            tenant_id: user.tenant_id,
            tenant_type: user.tenant_type,
            selected_systems: user.selected_systems,
            role: user.role,
            is_first_login: user.is_first_login,
            requiresEmailVerification: user.requiresEmailVerification,
            isSuperAdmin: user.isSuperAdmin
        };
        
        return generateResponse(res, 200, 'Token is valid', userInfo);
        
    } catch (error) {
        return generateResponse(res, 403, 'Invalid token');
    }
};

/**
 * Get security info for current session
 */
export const getSecurityInfo = async (req, res) => {
    try {
        const user = req.user;
        
        if (!user.security) {
            return generateResponse(res, 200, 'Security info retrieved', {
                message: 'Legacy token - no security context available'
            });
        }
        
        const securityInfo = {
            loginTime: new Date(user.security.timestamp).toISOString(),
            ipAddress: user.security.ip,
            browserInfo: user.security.userAgent,
            tokenType: user.type,
            expiresAt: new Date(user.exp * 1000).toISOString()
        };
        
        return generateResponse(res, 200, 'Security info retrieved', securityInfo);
        
    } catch (error) {
        return generateResponse(res, 500, 'Failed to retrieve security info');
    }
};

/**
 * Revoke all refresh tokens for a user (force logout from all devices)
 */
export const revokeAllTokens = async (req, res) => {
    try {
        const userId = req.user.id;
        
        // Get all refresh token keys for this user from Redis
        const keys = await redisClient.keys(`refresh:${userId}:*`);
        
        if (keys.length > 0) {
            // Delete all refresh tokens for this user
            await redisClient.del(keys);
        }
        
        logSecurityEvent('ALL_TOKENS_REVOKED', {
            userId,
            tokenCount: keys.length,
            ip: req.ip
        });
        
        return generateResponse(res, 200, 'All tokens revoked successfully', {
            revokedTokens: keys.length
        });
        
    } catch (error) {
        logSecurityEvent('REVOKE_ALL_TOKENS_FAILED', {
            userId: req.user?.id,
            error: error.message
        });
        
        return generateResponse(res, 500, 'Failed to revoke tokens');
    }
};

/**
 * Get active sessions for user
 */
export const getActiveSessions = async (req, res) => {
    try {
        const userId = req.user.id;
        
        // Get all refresh token keys for this user
        const keys = await redisClient.keys(`refresh:${userId}:*`);
        
        const sessions = [];
        for (const key of keys) {
            const tokenData = await redisClient.get(key);
            if (tokenData) {
                try {
                    const parsed = JSON.parse(tokenData);
                    sessions.push({
                        id: key.split(':')[2], // Extract session ID
                        createdAt: parsed.createdAt,
                        ip: parsed.ip,
                        userAgent: parsed.userAgent,
                        lastUsed: parsed.lastUsed || parsed.createdAt
                    });
                } catch (e) {
                    // Skip invalid session data
                }
            }
        }
        
        return generateResponse(res, 200, 'Active sessions retrieved', {
            sessions,
            totalSessions: sessions.length
        });
        
    } catch (error) {
        return generateResponse(res, 500, 'Failed to retrieve sessions');
    }
};

import { Router } from "express";
import {authenticateToken} from "../middlewares/jwt.js"
import multer from "multer";
const router =Router()

// Multer middleware for file uploads
const uploadMiddleware = multer({ dest: "uploads/" });

// ********Import the controllers ******

import {
    saveConfiguration,
    materials,
    qualityParameter,
    process,systems,parametersTypes,
    parameter,parameterCategories,
    getConfiguration,
    uploadJsonConfiguration} from "../controllers/configuration.controllers.js";



// ********Define path of controllers ******

// router.route('/products').get(authenticateToken,products)
router.route('/materials').get(authenticateToken,materials)
router.route('/quality-parameters').get(authenticateToken,qualityParameter)
router.route('/processes').get(authenticateToken,process)
router.route('/parameters').get(authenticateToken,parameter)
router.route('/parameters-types').get(authenticateToken,parametersTypes)
// router.route('/other-parameters').get(authenticateToken,otherParameters)
router.route('/systems').get(authenticateToken,systems)
router.route('/parameter-categories').get(authenticateToken,parameterCategories)
router.get('/get-configuration', authenticateToken, getConfiguration);




router.route('/save-configurations').post(authenticateToken,saveConfiguration)
router.route('/upload-json-configuration').post(uploadMiddleware.single("file"), authenticateToken, uploadJsonConfiguration)
export default router
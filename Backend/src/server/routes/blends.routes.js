import express from 'express';
import {
    getRandomBlendData,
    getAllBlendData,
    refreshBlendData,
    getRandomCarbonBlackData
} from '../controllers/blends.controller.js';

const router = express.Router();

// Route to get random blend data
router.get('/random', getRandomBlendData);

// Route to get all blend data with pagination
router.get('/all', getAllBlendData);

// Route to refresh blend data cache
router.post('/refresh', refreshBlendData);

// Route to get random carbon black IAN data
router.get('/carbon-random', getRandomCarbonBlackData);

export default router;

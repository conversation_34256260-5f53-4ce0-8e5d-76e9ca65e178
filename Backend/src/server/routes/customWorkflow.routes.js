import express from 'express';
import { 
    saveCustomWorkflow, 
    getDetailedWorkflowData, 
    previewWorkflowNode,
    previewWorkflowCode,
    getUserCustomWorkflows, 
    getWorkflowNodeData,
    runScript,
    runFlow
} from '../controllers/customWorkflow.controllers.js';
import { authenticateToken } from '../middlewares/jwt.js';

const router = express.Router();

router.put('/:id', authenticateToken, saveCustomWorkflow);

router.get('/:id/details', authenticateToken, getDetailedWorkflowData);

router.post('/:workflowId/preview-node', authenticateToken, previewWorkflowNode);

router.post('/:workflowId/preview-workflow', authenticateToken, previewWorkflowCode);

router.post('/:workflowId/run-script', authenticateToken, runScript);

router.post('/:workflowId/run-flow', authenticateToken, runFlow);

router.get('/listings', authenticateToken, getUserCustomWorkflows);

router.get('/:workflowId/workflow-nodes', authenticateToken, getWorkflowNodeData);

export default router;

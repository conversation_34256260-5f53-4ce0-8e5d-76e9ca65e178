import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { generateSecurityContext, validateSecurityContext, logSecurityEvent } from '../utils/securityUtils.js';
import { loadKeyPairFromEnv } from '../utils/keyGenerator.js';
import 'dotenv/config';

// Load ES256 key pair from environment
let keyPair;
try {
  keyPair = loadKeyPairFromEnv();
  console.log('✅ ES256 key pair loaded successfully');
} catch (error) {
  console.error('❌ Failed to load ES256 key pair:', error.message);
  console.error('Please run: node src/server/utils/keyGenerator.js to generate keys');
  process.exit(1);
}

// Token configuration
const TOKEN_CONFIG = {
  ACCESS_TOKEN_EXPIRY: '15m', // 15 minutes
  REFRESH_TOKEN_EXPIRY: '1d', // 1 day
  ISSUER: 'tvarit-app',
  AUDIENCE: 'tvarit-users',
  ALGORITHM: 'ES256' // ECDSA with P-256 curve - stronger than HS256
};

const createFilteredUserData = (userData) => {
  return {
    first_name: userData.first_name,
    last_name: userData.last_name,
    email: userData.email,
    id: userData.id,
    onboarding: userData.onboarding,
    tenant_id: userData.tenant_id,
    tenant_type: userData.tenant_type,
    selected_systems: userData.selected_systems,
    role: userData.role?.dataValues
      ? userData.role.dataValues // extract only role.dataValues
      : null,
    is_first_login: userData.is_first_login,
    requiresEmailVerification: userData.requiresEmailVerification,
    isSuperAdmin: userData.isSuperAdmin,
  };
};


/**
 * Generate secure access token with security context
 */
export const generateSecureAccessToken = (userData, req) => {
  try {
    const securityContext = generateSecurityContext(req);
    let filterUserData = createFilteredUserData(userData)

    const payload = {
      ...filterUserData,
      security: securityContext,
      type: 'access',
      tokenId: crypto.randomUUID(), // Unique token ID for blacklisting
      iss: TOKEN_CONFIG.ISSUER,
      aud: TOKEN_CONFIG.AUDIENCE
    };

    const token = jwt.sign(payload, keyPair.privateKey, {
      expiresIn: TOKEN_CONFIG.ACCESS_TOKEN_EXPIRY,
      algorithm: TOKEN_CONFIG.ALGORITHM
    });

    logSecurityEvent('ACCESS_TOKEN_GENERATED', {
      userId: userData.id,
      tokenId: payload.tokenId,
      ip: securityContext.ip,
      fingerprint: securityContext.fingerprint.substring(0, 8) + '...'
    });

    return { token, tokenId: payload.tokenId };
  } catch (error) {
    logSecurityEvent('ACCESS_TOKEN_GENERATION_FAILED', { error: error.message });
    throw new Error('Failed to generate access token');
  }
};

/**
 * Generate refresh token
 */
export const generateRefreshToken = (userData, req) => {
  try {
    const securityContext = generateSecurityContext(req);
    let filterUserData = createFilteredUserData(userData)
    console.log('filterUserData', filterUserData)

    const payload = {
      ...filterUserData,      security: securityContext,
      type: 'refresh',
      tokenId: crypto.randomUUID(), // Unique token ID for tracking
      iss: TOKEN_CONFIG.ISSUER,
      aud: TOKEN_CONFIG.AUDIENCE
    };
    
    const token = jwt.sign(payload, keyPair.privateKey, {
      expiresIn: TOKEN_CONFIG.REFRESH_TOKEN_EXPIRY,
      algorithm: TOKEN_CONFIG.ALGORITHM
    });
    
    logSecurityEvent('REFRESH_TOKEN_GENERATED', {
      userId: userData.id,
      tokenId: payload.tokenId,
      ip: securityContext.ip
    });
    
    return { token, tokenId: payload.tokenId };
  } catch (error) {
    logSecurityEvent('REFRESH_TOKEN_GENERATION_FAILED', { error: error.message });
    throw new Error('Failed to generate refresh token');
  }
};

/**
 * Verify and validate secure token
 */
export const verifySecureToken = async (token, req, options = {}) => {
  try {
    // Verify JWT signature and expiration with explicit algorithm validation
    const decoded = jwt.verify(token, keyPair.publicKey, {
      algorithms: [TOKEN_CONFIG.ALGORITHM] // Only allow ES256 algorithm to prevent algorithm confusion attacks
    });

    // Check token type
    if (options.expectedType && decoded.type !== options.expectedType) {
      throw new Error(`Invalid token type. Expected: ${options.expectedType}, Got: ${decoded.type}`);
    }

    // Check if access token is blacklisted (for logout functionality)
    if (decoded.type === 'access' && decoded.tokenId && options.redisClient) {
      try {
        const isBlacklisted = await options.redisClient.get(`blacklist:access:${decoded.tokenId}`);
        if (isBlacklisted) {
          throw new Error('Access token has been revoked');
        }
      } catch (redisError) {
        // Check if this is a token revocation error (not a Redis connection error)
        if (redisError.message === 'Access token has been revoked') {
          // Re-throw the revocation error - don't treat it as a Redis connection issue
          throw redisError;
        }
        // Redis unavailable - continue without blacklist check (graceful degradation)
        console.log('Redis unavailable for access token blacklist check, continuing...', redisError.message);
      }
    }
    
    // Validate security context if it's an access token
    if (decoded.type === 'access' && decoded.security) {
      const currentSecurityContext = generateSecurityContext(req);
      const validation = validateSecurityContext(
        decoded.security, 
        currentSecurityContext, 
        {
          strictIP: options.strictIP || false,
          strictFingerprint: options.strictFingerprint || true
        }
      );
      
      if (!validation.valid) {
        logSecurityEvent('TOKEN_SECURITY_VALIDATION_FAILED', {
          userId: decoded.id,
          reason: !validation.ipValid ? 'IP_MISMATCH' : 'FINGERPRINT_MISMATCH',
          details: validation.details
        });
        
        throw new Error('Token security validation failed');
      }
      
      logSecurityEvent('TOKEN_VALIDATED', {
        userId: decoded.id,
        type: decoded.type,
        ip: currentSecurityContext.ip
      });
    }
    
    return decoded;
  } catch (error) {
    console.log('🚨 Token verification error:', {
      name: error.name,
      message: error.message,
      expiredAt: error.expiredAt,
      stack: error.stack?.split('\n')[0]
    })
    if (error.name === 'TokenExpiredError') {
      logSecurityEvent('TOKEN_EXPIRED', { error: error.message });
      throw new Error('Token expired');
    } else if (error.name === 'JsonWebTokenError') {
      logSecurityEvent('TOKEN_INVALID', { error: error.message });
      throw new Error('Invalid token');
    } else {
      logSecurityEvent('TOKEN_VERIFICATION_FAILED', { error: error.message });
      throw error;
    }
  }
};

/**
 * Generate token pair (access + refresh)
 */
export const generateTokenPair = (userData, req) => {
  try {
    const accessTokenData = generateSecureAccessToken(userData, req);
    const refreshTokenData = generateRefreshToken(userData, req);

    return {
      accessToken: accessTokenData.token,
      accessTokenId: accessTokenData.tokenId,
      refreshToken: refreshTokenData.token,
      refreshTokenId: refreshTokenData.tokenId,
      expiresIn: TOKEN_CONFIG.ACCESS_TOKEN_EXPIRY,
      tokenType: 'Bearer'
    };
  } catch (error) {
    logSecurityEvent('TOKEN_PAIR_GENERATION_FAILED', {
      userId: userData.id,
      error: error.message
    });
    throw error;
  }
};

/**
 * Refresh access token using refresh token
 */
export const refreshAccessToken = async (refreshToken, req, redisClient) => {
  try {
    // Verify refresh token WITHOUT security context validation
    // Refresh tokens should not be validated against current request context
    const decoded = jwt.verify(refreshToken, keyPair.publicKey, {
      algorithms: [TOKEN_CONFIG.ALGORITHM] // Only allow ES256 algorithm to prevent algorithm confusion attacks
    });

    // Check token type
    if (decoded.type !== 'refresh') {
      throw new Error('Invalid token type for refresh');
    }

    // Check if refresh token is blacklisted (optional - graceful degradation)
    if (redisClient && decoded.tokenId) {
      try {
        const isBlacklisted = await redisClient.get(`blacklist:${decoded.tokenId}`);
        if (isBlacklisted) {
          throw new Error('Refresh token has been revoked');
        }
      } catch (redisError) {
        // Redis unavailable - continue without blacklist check (graceful degradation)
        console.log('Redis unavailable for blacklist check, continuing...', redisError.message);
      }
    }

    // Get fresh user data (you might want to fetch from database)
    // const userData = {
    //   id: decoded.userId,
    //   email: decoded.email
    //   // Add other user fields as needed
    // };

    // Generate new access token with current request context
    const newAccessTokenData = generateSecureAccessToken(decoded, req);

    logSecurityEvent('ACCESS_TOKEN_REFRESHED', {
      userId: decoded.userId,
      refreshTokenId: decoded.tokenId,
      newAccessTokenId: newAccessTokenData.tokenId
    });

    return {
      accessToken: newAccessTokenData.token,
      expiresIn: TOKEN_CONFIG.ACCESS_TOKEN_EXPIRY,
      tokenType: 'Bearer'
    };
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      logSecurityEvent('REFRESH_TOKEN_EXPIRED', { error: error.message });
      throw new Error('Refresh token expired');
    } else if (error.name === 'JsonWebTokenError') {
      logSecurityEvent('REFRESH_TOKEN_INVALID', { error: error.message });
      throw new Error('Invalid refresh token');
    } else {
      logSecurityEvent('TOKEN_REFRESH_FAILED', { error: error.message });
      throw error;
    }
  }
};

/**
 * Revoke access token (logout)
 */
export const revokeAccessToken = async (accessToken, redisClient) => {
  try {
    const decoded = jwt.verify(accessToken, keyPair.publicKey, {
      algorithms: [TOKEN_CONFIG.ALGORITHM] // Only allow ES256 algorithm to prevent algorithm confusion attacks
    });

    if (decoded.type !== 'access') {
      throw new Error('Invalid token type for revocation');
    }

    if (redisClient && decoded.tokenId) {
      // Add to blacklist with expiration matching token expiry
      const expirationTime = decoded.exp - Math.floor(Date.now() / 1000);

      if (expirationTime > 0) {
        const blacklistKey = `blacklist:access:${decoded.tokenId}`;
        await redisClient.setEx(blacklistKey, expirationTime, 'revoked');
      }
    }

    logSecurityEvent('ACCESS_TOKEN_REVOKED', {
      userId: decoded.userId,
      tokenId: decoded.tokenId
    });

    return true;
  } catch (error) {
    console.log('❌ Access token revocation error:', error.message);
    logSecurityEvent('ACCESS_TOKEN_REVOCATION_FAILED', { error: error.message });
    throw error;
  }
};

/**
 * Revoke refresh token (logout)
 */
export const revokeRefreshToken = async (refreshToken, redisClient) => {
  try {
    const decoded = jwt.verify(refreshToken, keyPair.publicKey, {
      algorithms: [TOKEN_CONFIG.ALGORITHM] // Only allow ES256 algorithm to prevent algorithm confusion attacks
    });

    if (decoded.type !== 'refresh') {
      throw new Error('Invalid token type for revocation');
    }

    if (redisClient && decoded.tokenId) {
      // Add to blacklist with expiration matching token expiry
      const expirationTime = decoded.exp - Math.floor(Date.now() / 1000);
      if (expirationTime > 0) {
        await redisClient.setEx(`blacklist:${decoded.tokenId}`, expirationTime, 'revoked');
      }
    }

    logSecurityEvent('REFRESH_TOKEN_REVOKED', {
      userId: decoded.userId,
      tokenId: decoded.tokenId
    });

    return true;
  } catch (error) {
    logSecurityEvent('TOKEN_REVOCATION_FAILED', { error: error.message });
    throw error;
  }
};

export { TOKEN_CONFIG, keyPair };

import axios from 'axios';


/**
 * Windmill API base URL from environment variables
 * @type {string}
 */
const WINDMILL_API_BASE_URL = process.env.WINDMILL_API_BASE_URL;

/**
 * Windmill workspace ID from environment variables
 * @type {string}
 */
const WINDM<PERSON><PERSON>_WORKSPACE_ID = process.env.WINDM<PERSON><PERSON>_WORKSPACE_ID;

/**
 * Windmill authentication token from environment variables
 * @type {string}
 */
const WIN<PERSON><PERSON>L_AUTH_TOKEN = process.env.WINDMILL_AUTH_TOKEN;


/**
 * Executes a preview node via the Windmill API.
 * @async
 * @param {Object} payload - The payload to send to the Windmill API.
 * @returns {Promise<string>} The job ID returned by the Windmill API.
 * @throws {Error} If the API response status is not 201.
 */
export const executePreviewNode = async (payload) => {
    try {

        // Execute the script via Windmill API
        const response = await axios.post(
            `${WINDMILL_API_BASE_URL}/w/${WIN<PERSON><PERSON><PERSON>_WORKSPACE_ID}/jobs/run/preview`,
            payload,
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${process.env.WINDM<PERSON>L_AUTH_TOKEN}`
                }
            }
        );

        // Check if response is 201 (Created)
        if (response.status !== 201) {
            throw new Error(`Windmill API error: ${response.status} - ${response.statusText}`);
        }

        /** @type {string} */
        const job_id = response.data;
        return job_id;
    } catch (error) {
        throw new Error('\nError executing preview node: ' + error.message);
    }
}


/**
 * Executes a script via the Windmill API (non-preview mode).
 * @async
 * @param {string} scriptHashId - The hash ID of the script to execute.
 * @param {Object} payload - The payload to send to the Windmill API.
 * @returns {Promise<string>} The job ID returned by the Windmill API.
 * @throws {Error} If the API response status is not 201.
 */
export const executeRunScript = async (scriptHashId, payload) => {
    try {
        // Execute the script via Windmill API
        const response = await axios.post(
            `${WINDMILL_API_BASE_URL}/w/${WINDMILL_WORKSPACE_ID}/jobs/run/h/${scriptHashId}`,
            payload,
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${process.env.WINDMILL_AUTH_TOKEN}`
                }
            }
        );

        // Check if response is 201 (Created)
        if (response.status !== 201) {
            throw new Error(`Windmill API error: ${response.status} - ${response.statusText}`);
        }

        /** @type {string} */
        const job_id = response.data;
        return job_id;
    } catch (error) {
        throw new Error('\nError executing run script: ' + error.message);
    }
}

/**
 * Executes a flow via the Windmill API.
 * @async
 * @param {string} flowPath - The path of the flow to execute.
 * @param {Object} payload - The payload to send to the Windmill API.
 * @returns {Promise<string>} The job ID returned by the Windmill API.
 * @throws {Error} If the API response status is not 201.
 */
export const executeRunFlow = async (flowPath, payload) => {
    try {
        // Execute the script via Windmill API
        const response = await axios.post(
            `${WINDMILL_API_BASE_URL}/w/${WINDMILL_WORKSPACE_ID}/jobs/run/f/${flowPath}`,
            payload,
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${process.env.WINDMILL_AUTH_TOKEN}`
                }
            }
        );

        // Check if response is 201 (Created)
        if (response.status !== 201) {
            throw new Error(`Windmill API error: ${response.status} - ${response.statusText}`);
        }

        /** @type {string} */
        const job_id = response.data;
        return job_id;
    } catch (error) {
        throw new Error('\nError executing run flow: ' + error.message);
    }
}

/**
 * Retrieves the status of a Windmill job by job ID.
 * @async
 * @param {string} jobId - The job ID to query.
 * @returns {Promise<Object>} The job status data returned by the Windmill API.
 * @throws {Error} If the API response status is not 200.
 */
export const getJobStatus = async (jobId) => {
    try {
        const response = await axios.get(
            `${WINDMILL_API_BASE_URL}/w/${WINDMILL_WORKSPACE_ID}/jobs/${jobId}`,
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${process.env.WINDMILL_AUTH_TOKEN}`
                }
            }
        );

        // Check if response is 200 (OK)
        if (response.status !== 200) {
            throw new Error(`Windmill API error: ${response.status} - ${response.statusText}`);
        }

        return response.data;
    } catch (error) {
        throw new Error('\nError fetching job status: ' + error.message);
    }
}
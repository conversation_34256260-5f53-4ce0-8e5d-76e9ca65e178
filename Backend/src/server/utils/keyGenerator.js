#!/usr/bin/env node

/**
 * ES256 Key Generation Utility
 * Generates ECDSA P-256 key pairs for JWT signing and verification
 */

import crypto from 'crypto';
import fs from 'fs';
import path from 'path';

/**
 * Generate ES256 (ECDSA P-256) key pair
 * @returns {Object} Object containing private and public keys in PEM format
 */
export const generateES256KeyPair = () => {
  try {
    const { privateKey, publicKey } = crypto.generateKeyPairSync('ec', {
      namedCurve: 'prime256v1', // P-256 curve for ES256
      publicKeyEncoding: {
        type: 'spki',
        format: 'pem'
      },
      privateKeyEncoding: {
        type: 'pkcs8',
        format: 'pem'
      }
    });

    return {
      privateKey,
      publicKey,
      algorithm: 'ES256',
      curve: 'prime256v1'
    };
  } catch (error) {
    throw new Error(`Failed to generate ES256 key pair: ${error.message}`);
  }
};

/**
 * Save key pair to files
 * @param {Object} keyPair - Key pair object from generateES256KeyPair
 * @param {string} basePath - Base path for saving keys (default: ./keys)
 */
export const saveKeyPairToFiles = (keyPair, basePath = './keys') => {
  try {
    // Ensure keys directory exists
    if (!fs.existsSync(basePath)) {
      fs.mkdirSync(basePath, { recursive: true });
    }

    const privateKeyPath = path.join(basePath, 'jwt-private.pem');
    const publicKeyPath = path.join(basePath, 'jwt-public.pem');

    // Save private key
    fs.writeFileSync(privateKeyPath, keyPair.privateKey, { mode: 0o600 }); // Read-only for owner
    
    // Save public key
    fs.writeFileSync(publicKeyPath, keyPair.publicKey, { mode: 0o644 }); // Read for all

    console.log('✅ ES256 key pair saved successfully:');
    console.log(`   Private key: ${privateKeyPath}`);
    console.log(`   Public key: ${publicKeyPath}`);
    console.log('');
    console.log('🔐 Add these to your .env file:');
    console.log('JWT_PRIVATE_KEY_PATH=./keys/jwt-private.pem');
    console.log('JWT_PUBLIC_KEY_PATH=./keys/jwt-public.pem');
    console.log('');
    console.log('Or set the keys directly as environment variables:');
    console.log('JWT_PRIVATE_KEY="' + keyPair.privateKey.replace(/\n/g, '\\n') + '"');
    console.log('JWT_PUBLIC_KEY="' + keyPair.publicKey.replace(/\n/g, '\\n') + '"');

    return {
      privateKeyPath,
      publicKeyPath
    };
  } catch (error) {
    throw new Error(`Failed to save key pair: ${error.message}`);
  }
};

/**
 * Load key pair from files
 * @param {string} privateKeyPath - Path to private key file
 * @param {string} publicKeyPath - Path to public key file
 * @returns {Object} Key pair object
 */
export const loadKeyPairFromFiles = (privateKeyPath, publicKeyPath) => {
  try {
    const privateKey = fs.readFileSync(privateKeyPath, 'utf8');
    const publicKey = fs.readFileSync(publicKeyPath, 'utf8');

    return {
      privateKey,
      publicKey,
      algorithm: 'ES256'
    };
  } catch (error) {
    throw new Error(`Failed to load key pair: ${error.message}`);
  }
};

/**
 * Load key pair from environment variables
 * @returns {Object} Key pair object
 */
export const loadKeyPairFromEnv = () => {
  try {
    let privateKey, publicKey;

    // Try to load from file paths first
    if (process.env.JWT_PRIVATE_KEY_PATH && process.env.JWT_PUBLIC_KEY_PATH) {
      privateKey = fs.readFileSync(process.env.JWT_PRIVATE_KEY_PATH, 'utf8');
      publicKey = fs.readFileSync(process.env.JWT_PUBLIC_KEY_PATH, 'utf8');
    }
    // Try to load from direct environment variables
    else if (process.env.JWT_PRIVATE_KEY && process.env.JWT_PUBLIC_KEY) {
      privateKey = process.env.JWT_PRIVATE_KEY.replace(/\\n/g, '\n');
      publicKey = process.env.JWT_PUBLIC_KEY.replace(/\\n/g, '\n');
    }
    else {
      throw new Error('JWT keys not found in environment variables. Please set JWT_PRIVATE_KEY_PATH/JWT_PUBLIC_KEY_PATH or JWT_PRIVATE_KEY/JWT_PUBLIC_KEY');
    }

    return {
      privateKey,
      publicKey,
      algorithm: 'ES256'
    };
  } catch (error) {
    throw new Error(`Failed to load key pair from environment: ${error.message}`);
  }
};

/**
 * Validate key pair (ensure private key matches public key)
 * @param {Object} keyPair - Key pair to validate
 * @returns {boolean} True if valid
 */
export const validateKeyPair = (keyPair) => {
  try {
    const testData = 'test-signature-validation';
    
    // Create signature with private key
    const sign = crypto.createSign('SHA256');
    sign.update(testData);
    const signature = sign.sign(keyPair.privateKey);

    // Verify signature with public key
    const verify = crypto.createVerify('SHA256');
    verify.update(testData);
    const isValid = verify.verify(keyPair.publicKey, signature);

    return isValid;
  } catch (error) {
    console.error('Key pair validation failed:', error.message);
    return false;
  }
};

// CLI functionality - run this script directly to generate keys
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log('🔑 Generating ES256 key pair for JWT...\n');
  
  try {
    const keyPair = generateES256KeyPair();
    
    console.log('✅ Key pair generated successfully');
    console.log(`   Algorithm: ${keyPair.algorithm}`);
    console.log(`   Curve: ${keyPair.curve}`);
    console.log('');

    // Validate the generated key pair
    if (validateKeyPair(keyPair)) {
      console.log('✅ Key pair validation passed');
    } else {
      console.log('❌ Key pair validation failed');
      process.exit(1);
    }

    // Save to files
    const basePath = process.argv[2] || './keys';
    saveKeyPairToFiles(keyPair, basePath);

    console.log('');
    console.log('🚀 Next steps:');
    console.log('1. Add the environment variables to your .env file');
    console.log('2. Restart your application');
    console.log('3. All existing JWT tokens will be invalidated (users need to re-login)');
    console.log('');
    console.log('⚠️  IMPORTANT: Keep the private key secure and never commit it to version control!');

  } catch (error) {
    console.error('❌ Failed to generate key pair:', error.message);
    process.exit(1);
  }
}

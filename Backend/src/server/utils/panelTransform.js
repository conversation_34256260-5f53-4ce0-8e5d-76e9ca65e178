// Configuration: Set the timezone of your data
// Change this if your data is stored in a different timezone
const DATA_TIMEZONE = process.env.DATA_TIMEZONE || 'Asia/Kolkata'; // IST by default

/**
 * Timezone-aware utility function to handle date conversion consistently
 * regardless of server location. Handles timezone differences between server and data.
 * @param {string|number} dateInput - Date string or timestamp
 * @param {string} dataTimezone - Timezone of the data (default: from config)
 * @returns {Date} Date object adjusted for data timezone
 */
const convertToDataTimezone = (dateInput, dataTimezone = DATA_TIMEZONE) => {
    try {
        // If it's already a number (timestamp), create date directly
        if (typeof dateInput === 'number') {
            return new Date(dateInput);
        }

        // For string dates, parse and convert to data timezone
        const date = new Date(dateInput);

        // If the date is invalid, return as-is
        if (isNaN(date.getTime())) {
            return date;
        }

        // Convert to data timezone to ensure consistent interpretation
        // This handles the case where server is in different timezone than data
        const utcTime = date.getTime() + (date.getTimezoneOffset() * 60000);
        const dataTimezoneOffset = getTimezoneOffset(dataTimezone);
        return new Date(utcTime + dataTimezoneOffset);
    } catch (error) {
        console.error('Error in convertToDataTimezone:', error);
        return new Date(dateInput); // Fallback to original behavior
    }
};

/**
 * Get timezone offset in milliseconds for a given timezone
 * @param {string} timezone - Timezone identifier (e.g., 'Asia/Kolkata')
 * @returns {number} Offset in milliseconds
 */
const getTimezoneOffset = (timezone) => {
    try {
        // Create a date in UTC and the target timezone
        const now = new Date();
        const utc = new Date(now.toLocaleString('en-US', { timeZone: 'UTC' }));
        const target = new Date(now.toLocaleString('en-US', { timeZone: timezone }));
        return target.getTime() - utc.getTime();
    } catch (error) {
        // Fallback for IST if timezone parsing fails
        return 5.5 * 60 * 60 * 1000; // IST offset: +5:30 hours in milliseconds
    }
};

/**
 * Transforms time series data into a format suitable for panel display.
 * OPTIMIZED VERSION - Processes data in single pass for better performance
 * @param {*} data data to be transformed
 * @returns
 */
export const transformTimeSeries = (data) => {
    if (!data || data.length == 0 || !Array.isArray(data)) {
        return {
            columnOptions: {},
            metadata: {
                columnNames: [],
                hasData: false
            }
        };
    }

    // Assuming first column is date/time and other columns are values
    const headers = Array.isArray(data[0]) ? data[0] : Object.keys(data[0]);
    const rows = Array.isArray(data[0]) ? data.slice(1) : data;

    // Find datetime column - exact match only (case insensitive)
    const dateColumnIndex = headers.findIndex(h => h === 'DateTime' || h === 'datetime' || h === 'Datetime' || h === 'date' || h === 'Date' || h === '_time');

    if (dateColumnIndex < 0) {
        // No DateTime column found, return raw data for frontend to handle
        return {
            columnOptions: {},
            metadata: {
                columnNames: [],
                hasData: false
            }
        };
    }

    const columnOptions = {};
    const columnNames = [];

    // OPTIMIZATION: Cache timezone offset calculation (expensive operation)
    const timezoneOffset = getTimezoneOffset(DATA_TIMEZONE);

    // OPTIMIZATION: Pre-calculate decimation parameters
    const MAX_POINTS = 2000;
    const totalRows = rows.length;
    const decimationStep = totalRows > MAX_POINTS ? Math.ceil(totalRows / MAX_POINTS) : 1;
    const shouldDecimate = decimationStep > 1;

    // OPTIMIZATION: Process all columns in a single pass through the data
    // Initialize data structures for all numeric columns
    const numericColumns = [];
    for (let i = 0; i < headers.length; i++) {
        if (i !== dateColumnIndex) {
            numericColumns.push({
                index: i,
                name: headers[i],
                validData: [],
                minY: Infinity,
                maxY: -Infinity,
                validCount: 0,
                dateRange: { min: Infinity, max: -Infinity }
            });
        }
    }

    // OPTIMIZATION: Single pass through all rows - process all columns simultaneously
    const dateCache = new Map(); // Cache converted dates to avoid repeated conversions

    for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {
        // Apply decimation early - skip rows that won't be included
        if (shouldDecimate && rowIndex % decimationStep !== 0) {
            continue;
        }

        const row = rows[rowIndex];
        const rawDateValue = Array.isArray(row) ? row[dateColumnIndex] : row[headers[dateColumnIndex]];

        // OPTIMIZATION: Cache date conversion to avoid repeated expensive operations
        let convertedDate;
        if (dateCache.has(rawDateValue)) {
            convertedDate = dateCache.get(rawDateValue);
        } else {
            convertedDate = convertToDataTimezoneOptimized(rawDateValue, timezoneOffset);
            dateCache.set(rawDateValue, convertedDate);
        }

        const dateTimestamp = convertedDate.getTime();

        // Process all numeric columns for this row
        for (const column of numericColumns) {
            const rawValue = Array.isArray(row) ? row[column.index] : row[headers[column.index]];
            const numValue = parseFloat(rawValue);

            if (!isNaN(numValue)) {
                column.validData.push([rawDateValue, numValue]);
                column.validCount++;

                // Update min/max values in single pass
                if (numValue < column.minY) column.minY = numValue;
                if (numValue > column.maxY) column.maxY = numValue;

                // Update date range
                if (dateTimestamp < column.dateRange.min) column.dateRange.min = dateTimestamp;
                if (dateTimestamp > column.dateRange.max) column.dateRange.max = dateTimestamp;
            }
        }
    }

    // OPTIMIZATION: Process results for each column (no more loops through raw data)
    for (const column of numericColumns) {
        if (column.validCount > 0) {
            // Calculate y-axis range with padding
            let yAxisMin = 'dataMin';
            let yAxisMax = 'dataMax';

            const range = column.maxY - column.minY;
            let padding;

            if (range === 0 || range < 0.000001) {
                // Flat line case
                padding = Math.max(1, Math.abs(column.maxY) * 0.5);
            } else {
                // Normal case
                padding = range < 0.01 ? Math.max(0.5, range) : range * 0.2;
            }

            yAxisMin = Math.round((column.minY - padding) * 100) / 100;
            yAxisMax = Math.round((column.maxY + padding) * 100) / 100;

            // Create individual ECharts configuration for this column
            columnOptions[column.name] = {
                animation: false,
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    },
                    formatter: function (params) {
                        let result = `${convertToDataTimezone(params[0].value[0]).toLocaleString()}<br/>`;
                        result += `${params[0].seriesName}: ${params[0].value[1]}<br/>`;
                        return result;
                    }
                },
                legend: {
                    data: [column.name],
                    top: 30,
                    type: 'scroll'
                },
                toolbox: {
                    show: true
                },
                xAxis: {
                    type: 'time',
                    boundaryGap: false,
                    axisLine: {
                        onZero: false
                    },
                    splitLine: {
                        show: false
                    }
                },
                yAxis: {
                    type: 'value',
                    min: yAxisMin,
                    max: yAxisMax,
                    scale: true,
                    splitArea: {
                        show: true
                    }
                },
                dataZoom: [
                    {
                        type: 'inside',
                        xAxisIndex: [0],
                        filterMode: 'filter'
                    },
                    {
                        type: 'slider',
                        xAxisIndex: [0],
                        filterMode: 'filter'
                    }
                ],
                brush: {
                    toolbox: ['rect', 'polygon', 'clear'],
                    xAxisIndex: 0
                },
                series: [{
                    name: column.name,
                    type: 'line',
                    data: column.validData,
                    smooth: false,
                    symbol: 'circle',
                    symbolSize: 4,
                    lineStyle: {
                        width: 2,
                    },
                    emphasis: {
                        focus: 'series'
                    }
                }],
                hasData: true,
                // Column-specific metadata
                metadata: {
                    columnName: column.name,
                    dataPoints: column.validData.length,
                    yRange: { min: yAxisMin, max: yAxisMax },
                    dateRange: column.validData.length > 0 ? {
                        min: column.dateRange.min,
                        max: column.dateRange.max
                    } : null
                }
            };

            columnNames.push(column.name);
        }
    }

    // Return individual column configurations
    return {
        columnOptions: columnOptions,
        metadata: {
            columnNames: columnNames,
            hasData: columnNames.length > 0,
            totalColumns: columnNames.length
        }
    };
}

/**
 * OPTIMIZATION: Faster timezone conversion that reuses calculated offset
 * @param {string|number} dateInput - Date string or timestamp
 * @param {number} timezoneOffsetMs - Pre-calculated timezone offset in milliseconds
 * @returns {Date} Date object adjusted for data timezone
 */
const convertToDataTimezoneOptimized = (dateInput, timezoneOffsetMs) => {
    try {
        // If it's already a number (timestamp), create date directly
        if (typeof dateInput === 'number') {
            return new Date(dateInput);
        }

        // For string dates, parse and convert to data timezone
        const date = new Date(dateInput);

        // If the date is invalid, return as-is
        if (isNaN(date.getTime())) {
            return date;
        }

        // Convert to data timezone using pre-calculated offset
        const utcTime = date.getTime() + (date.getTimezoneOffset() * 60000);
        return new Date(utcTime + timezoneOffsetMs);
    } catch (error) {
        console.error('Error in convertToDataTimezoneOptimized:', error);
        return new Date(dateInput); // Fallback to original behavior
    }
};

/**
 * Transforms data into histogram format with Plotly configurations.
 * @param {*} data data to be transformed
 * @returns Object containing columnOptions with Plotly configurations
 */
export const transformHistogram = (data) => {

    if (!data || !Array.isArray(data) || data.length === 0) {
        return {
            columnOptions: {},
            metadata: {
                hasData: false,
                totalColumns: 0,
                processedColumns: 0
            }
        };
    }

    try {
        // Extract headers and rows from data
        const headers = Array.isArray(data[0]) ? data[0].map(String) : Object.keys(data[0]);
        const rows = Array.isArray(data[0]) ? data.slice(1) : data;

        const columnOptions = {};
        let processedColumns = 0;

        // Process all numeric columns except DateTime columns
        const columnsToProcess = headers
            .map((name, index) => ({ index, name }))
            .filter(({ name }) => !name.toLowerCase().includes('datetime'));

        columnsToProcess.forEach(({ index, name }) => {

            try {
                // Extract values for this column
                const values = rows.map(row => {
                    const value = Array.isArray(row) ? row[index] : row[name];
                    return value !== undefined && value !== null ? value : null;
                });

                // Extract numeric values and filter positive values only
                const numericValues = values
                    .map(value => typeof value === 'string' ? parseFloat(value) : value)
                    .filter(value => value !== null && !isNaN(value) && value > 0); // Only positive values

                if (numericValues.length === 0) {
                    return;
                }

                // Calculate statistics
                const min = Math.min(...numericValues);
                const max = Math.max(...numericValues);
                const mean = numericValues.reduce((sum, val) => sum + val, 0) / numericValues.length;

                // Calculate median
                const sortedValues = [...numericValues].sort((a, b) => a - b);
                const median = sortedValues.length % 2 === 0
                    ? (sortedValues[sortedValues.length / 2 - 1] + sortedValues[sortedValues.length / 2]) / 2
                    : sortedValues[Math.floor(sortedValues.length / 2)];

                // Calculate standard deviation
                const variance = numericValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / numericValues.length;
                const stdDev = Math.sqrt(variance);

                // Dynamic bin calculation
                const binCount = Math.min(50, Math.max(10, Math.ceil(Math.sqrt(numericValues.length))));
                const binWidth = (max - min) / binCount;


                // Handle edge case where all values are the same (min === max)
                if (binWidth === 0 || !isFinite(binWidth)) {

                    // Create a single bin for identical values
                    const plotlyData = [{
                        x: [min],
                        y: [numericValues.length],
                        type: 'bar',
                        name: name,
                        marker: {
                            color: 'rgba(55, 128, 191, 0.7)',
                            line: {
                                color: 'rgba(55, 128, 191, 1.0)',
                                width: 1
                            }
                        },
                        hovertemplate: '<b>%{fullData.name}</b><br>' +
                            'Value: %{x:.2f}<br>' +
                            'Count: %{y}<br>' +
                            '<extra></extra>'
                    }];

                    // Create Plotly layout for single value
                    const plotlyLayout = {
                        title: {
                            text: `Histogram: ${name}`,
                            font: { size: 16 }
                        },
                        xaxis: {
                            title: name,
                            showgrid: true,
                            zeroline: false,
                            range: [min - 1, min + 1] // Add some padding around the single value
                        },
                        yaxis: {
                            title: 'Frequency',
                            showgrid: true,
                            zeroline: false
                        },
                        bargap: 0.1,
                        showlegend: false,
                        margin: { l: 60, r: 30, t: 60, b: 60 },
                        plot_bgcolor: 'rgba(0,0,0,0)',
                        paper_bgcolor: 'rgba(0,0,0,0)'
                    };

                    // Store complete Plotly configuration for single value case
                    columnOptions[name] = {
                        plotlyData: plotlyData,
                        plotlyLayout: plotlyLayout,
                        statistics: {
                            mean: mean.toFixed(2),
                            median: median.toFixed(2),
                            min: min.toFixed(2),
                            max: max.toFixed(2),
                            stdDev: stdDev.toFixed(2),
                            count: numericValues.length,
                            binCount: 1
                        },
                        metadata: {
                            columnName: name,
                            dataPoints: numericValues.length,
                            originalDataPoints: values.length,
                            positiveValuesOnly: true,
                            singleValue: true
                        }
                    };

                    processedColumns++;
                    return; // Skip the normal binning process
                }

                // Create bins for normal case
                const bins = Array.from({ length: binCount }, (_, i) => ({
                    start: min + i * binWidth,
                    end: min + (i + 1) * binWidth,
                    count: 0
                }));

                // Count values in each bin with proper bounds checking
                numericValues.forEach((value, valueIndex) => {
                    try {
                        let binIndex = Math.floor((value - min) / binWidth);

                        // Ensure binIndex is within valid range
                        binIndex = Math.max(0, Math.min(binCount - 1, binIndex));

                        // Additional safety check
                        if (bins && bins[binIndex] && typeof bins[binIndex].count === 'number') {
                            bins[binIndex].count++;
                        } else {
                            console.warn(`Backend - transformHistogram: Invalid bin access - binIndex: ${binIndex}, bins length: ${bins?.length}, bin exists: ${!!bins[binIndex]}, value: ${value}, column: ${name}`);
                        }
                    } catch (binError) {
                        console.error(`Backend - transformHistogram: Error processing value ${value} at index ${valueIndex} for column ${name}:`, binError);
                    }
                });

                // Create Plotly data with safety checks
                const plotlyData = [{
                    x: bins.map(bin => bin ? (bin.start + bin.end) / 2 : 0), // Bin centers with safety check
                    y: bins.map(bin => bin ? bin.count : 0), // Bin counts with safety check
                    type: 'bar',
                    name: name,
                    marker: {
                        color: 'rgba(55, 128, 191, 0.7)',
                        line: {
                            color: 'rgba(55, 128, 191, 1.0)',
                            width: 1
                        }
                    },
                    hovertemplate: '<b>%{fullData.name}</b><br>' +
                        'Range: %{x:.2f}<br>' +
                        'Count: %{y}<br>' +
                        '<extra></extra>'
                }];

                // Create Plotly layout
                const plotlyLayout = {
                    title: {
                        text: `Histogram: ${name}`,
                        font: { size: 16 }
                    },
                    xaxis: {
                        title: name,
                        showgrid: true,
                        zeroline: false
                    },
                    yaxis: {
                        title: 'Frequency',
                        showgrid: true,
                        zeroline: false
                    },
                    bargap: 0.1,
                    showlegend: false,
                    margin: { l: 60, r: 30, t: 60, b: 60 },
                    plot_bgcolor: 'rgba(0,0,0,0)',
                    paper_bgcolor: 'rgba(0,0,0,0)'
                };

                // Store complete Plotly configuration
                columnOptions[name] = {
                    plotlyData: plotlyData,
                    plotlyLayout: plotlyLayout,
                    statistics: {
                        mean: mean.toFixed(2),
                        median: median.toFixed(2),
                        min: min.toFixed(2),
                        max: max.toFixed(2),
                        stdDev: stdDev.toFixed(2),
                        count: numericValues.length,
                        binCount: binCount
                    },
                    metadata: {
                        columnName: name,
                        dataPoints: numericValues.length,
                        originalDataPoints: values.length,
                        positiveValuesOnly: true
                    }
                };

                processedColumns++;
            } catch (columnError) {
                console.error(`Backend - transformHistogram: Error processing column ${name}:`, columnError);
                // Continue with next column instead of failing entire operation
            }
        });

        const result = {
            columnOptions: columnOptions,
            metadata: {
                hasData: Object.keys(columnOptions).length > 0,
                totalColumns: headers.length,
                processedColumns: processedColumns,
                excludedColumns: headers.length - processedColumns,
                processedRows: rows.length
            }
        };

        return result;

    } catch (error) {
        return {
            columnOptions: {},
            metadata: {
                hasData: false,
                totalColumns: 0,
                processedColumns: 0,
                error: error.message
            }
        };
    }
};

/**
 * Transforms data into overview format with column statistics.
 * @param {*} data data to be transformed
 * @returns Object containing columnStatistics array with pre-calculated statistics
 */
export const transformOverview = (data) => {

    if (!data || !Array.isArray(data) || data.length === 0) {
        return {
            columnStatistics: [],
            metadata: {
                hasData: false,
                totalColumns: 0,
                processedColumns: 0
            }
        };
    }

    try {
        // Extract headers and rows from data
        const headers = Array.isArray(data[0]) ? data[0].map(String) : Object.keys(data[0]);
        const rows = Array.isArray(data[0]) ? data.slice(1) : data;

        const columnStatistics = [];
        let processedColumns = 0;

        // Process all columns except DateTime columns (used for filtering and x-axis)
        const columnsToProcess = headers
            .map((name, index) => ({ index, name }))
            .filter(({ name }) => !name.toLowerCase().includes('datetime'));

        columnsToProcess.forEach(({ index, name }) => {
            // Extract values for this column
            const values = rows.map(row => {
                const value = Array.isArray(row) ? row[index] : row[name];
                return value !== undefined && value !== null ? value : null;
            });

            // Extract numeric values for calculations
            const numericValues = values
                .map(value => typeof value === 'string' ? parseFloat(value) : value)
                .filter(value => value !== null && !isNaN(value));

            // Count missing values
            const missingCount = values.filter(value =>
                value === null || value === undefined || value === ''
            ).length;

            // Count distinct values
            const distinctCount = new Set(
                values.filter(value =>
                    value !== null && value !== undefined && value !== ''
                )
            ).size;

            // Calculate statistics
            let mean = 'N/A';
            let min = 'N/A';
            let max = 'N/A';
            let stdDev = 'N/A';

            if (numericValues.length > 0) {
                // Calculate mean
                const sum = numericValues.reduce((acc, val) => acc + val, 0);
                const meanValue = sum / numericValues.length;
                mean = meanValue.toFixed(2);

                // Calculate min and max
                min = Math.min(...numericValues).toFixed(2);
                max = Math.max(...numericValues).toFixed(2);

                // Calculate standard deviation
                const squaredDifferences = numericValues.map(value => Math.pow(value - meanValue, 2));
                const variance = squaredDifferences.reduce((acc, val) => acc + val, 0) / numericValues.length;
                stdDev = Math.sqrt(variance).toFixed(2);
            }

            columnStatistics.push({
                name,
                mean,
                min,
                max,
                stdDev,
                missingValues: missingCount.toString(),
                distinctValues: distinctCount.toString(),
                // Additional metadata for potential future use
                totalValues: values.length,
                numericValues: numericValues.length,
                dataType: numericValues.length > 0 ? 'numeric' : 'categorical'
            });

            processedColumns++;
        });

        const result = {
            columnStatistics,
            metadata: {
                hasData: columnStatistics.length > 0,
                totalColumns: headers.length,
                processedColumns: processedColumns,
                excludedColumns: headers.length - processedColumns,
                processedRows: rows.length
            }
        };

        return result;

    } catch (error) {
        return {
            columnStatistics: [],
            metadata: {
                hasData: false,
                totalColumns: 0,
                processedColumns: 0,
                error: error.message
            }
        };
    }
};

/**
 * Transforms data into scatter plot format with ECharts configurations.
 * Creates scatter plots using target variable as X-axis and other columns as Y-axis.
 * @param {*} data data to be transformed
 * @param {*} payload optional payload containing filters and target variable configuration
 * @returns Object containing columnOptions with ECharts configurations
 */
export const transformScatterPlot = (data, payload = null) => {
    if (!data || !Array.isArray(data) || data.length === 0) {
        return {
            columnOptions: {},
            metadata: {
                hasData: false,
                totalColumns: 0,
                processedColumns: 0
            }
        };
    }

    try {
        // Extract headers and rows from data
        const headers = Array.isArray(data[0]) ? data[0].map(String) : Object.keys(data[0]);
        const rows = Array.isArray(data[0]) ? data.slice(1) : data;

        // Get target variable from configuration
        const targetVariable = payload?.configuration?.targetVariable;

        // Check if target variable is provided
        if (!targetVariable) {
            return {
                columnOptions: {},
                metadata: {
                    hasData: false,
                    totalColumns: 0,
                    processedColumns: 0,
                    error: "No target variable selected. Please configure a target variable to plot scatter plot."
                }
            };
        }

        // Find target variable column (case-insensitive search)
        const targetColumnIndex = headers.findIndex(h => h.toLowerCase() === targetVariable.toLowerCase());
       
        if (targetColumnIndex < 0) {
            return {
                columnOptions: {},
                metadata: {
                    hasData: false,
                    totalColumns: 0,
                    processedColumns: 0,
                    error: `Target variable '${targetVariable}' not found in CSV file`
                }
            };
        }

        const columnOptions = {};
        let processedColumns = 0;

        // Process all numeric columns except the target variable
        headers.forEach((columnName, columnIndex) => {
            if (columnIndex === targetColumnIndex || columnName === targetVariable) return;

            try {
                // Extract values for this column
                const values = rows.map(row => {
                    const targetValue = Array.isArray(row) ? row[targetColumnIndex] : row[targetVariable];
                    const columnValue = Array.isArray(row) ? row[columnIndex] : row[columnName];
                    return {
                        target: targetValue,
                        value: columnValue !== undefined && columnValue !== null ? columnValue : null
                    };
                });

                // Filter out null/undefined values and convert to numbers
                const validData = values
                    .filter(item => item.target && item.value !== null && item.value !== undefined)
                    .map(item => {
                        const targetNum = typeof item.target === 'string' ? parseFloat(item.target) : item.target;
                        const numValue = typeof item.value === 'string' ? parseFloat(item.value) : item.value;
                        return { target: targetNum, value: numValue };
                    })
                    .filter(item => !isNaN(item.target) && !isNaN(item.value));

                if (validData.length === 0) return;

                // Prepare data points for ECharts scatter plot [targetValue, columnValue]
                const scatterData = validData.map(item => [item.target, item.value]);

                // Calculate axis ranges with proper rounding
                const xValues = validData.map(item => item.target);
                const yValues = validData.map(item => item.value);
                const xMin = Math.min(...xValues);
                const xMax = Math.max(...xValues);
                const yMin = Math.min(...yValues);
                const yMax = Math.max(...yValues);
                const xRange = xMax - xMin;
                const yRange = yMax - yMin;
                const xPadding = xRange * 0.1;
                const yPadding = yRange * 0.1;

                // Round the axis limits to avoid long decimal numbers
                const xAxisMin = Math.round((xMin - xPadding) * 100) / 100;
                const xAxisMax = Math.round((xMax + xPadding) * 100) / 100;
                const yAxisMin = Math.round((yMin - yPadding) * 100) / 100;
                const yAxisMax = Math.round((yMax + yPadding) * 100) / 100;

                // Create ECharts configuration for this column's scatter plot
                columnOptions[columnName] = {
                    animation: false,
                    tooltip: {
                        trigger: 'item',
                        formatter: function (params) {
                            return `${targetVariable}: ${params.value[0]}<br/>${columnName}: ${params.value[1]}`;
                        }
                    },
                    legend: {
                        data: [columnName],
                        top: 30,
                        type: 'scroll'
                    },
                    grid: {
                        left: '10%',
                        right: '10%',
                        bottom: '15%',
                        top: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'value',
                        name: targetVariable,
                        nameLocation: 'middle',
                        nameGap: 30,
                        min: xAxisMin,
                        max: xAxisMax,
                        axisLabel: {
                            formatter: function (value) {
                                return parseFloat(value.toFixed(2));
                            }
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                type: 'dashed',
                                color: '#E5E5E5'
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: columnName,
                        nameLocation: 'middle',
                        nameGap: 50,
                        min: yAxisMin,
                        max: yAxisMax,
                        scale: true,
                        splitLine: {
                            show: true,
                            lineStyle: {
                                type: 'dashed',
                                color: '#E5E5E5'
                            }
                        },
                        axisLabel: {
                            formatter: function (value) {
                                // Format Y-axis values to 2 decimal places maximum
                                const num = Number(value);
                                if (num === 0) return '0';
                                if (Number.isInteger(num)) return num.toString();
                                return num.toFixed(2);
                            }
                        }
                    },
                    dataZoom: [
                        {
                            type: 'inside',
                            xAxisIndex: [0],
                            filterMode: 'filter'
                        },
                        {
                            type: 'slider',
                            xAxisIndex: [0],
                            filterMode: 'filter',
                            bottom: '5%'
                        }
                    ],
                    series: [{
                        name: columnName,
                        type: 'scatter',
                        data: scatterData,
                        symbolSize: 8,
                        itemStyle: {
                            color: '#4682B4', // Using color from your existing scatter plot
                            opacity: 0.8
                        },
                        emphasis: {
                            itemStyle: {
                                color: '#FFD700', // Golden color for emphasis
                                opacity: 1,
                                shadowBlur: 10,
                                shadowColor: 'rgba(0, 0, 0, 0.3)'
                            }
                        }
                    }],
                    hasData: true,
                    metadata: {
                        columnName: columnName,
                        targetVariable: targetVariable,
                        dataPoints: scatterData.length,
                        xRange: { min: xMin, max: xMax },
                        yRange: { min: yMin, max: yMax }
                    }
                };

                processedColumns++;
            } catch (columnError) {
                console.error(`Backend - transformScatterPlot: Error processing column ${columnName}:`, columnError);
            }
        });

        return {
            columnOptions: columnOptions,
            metadata: {
                hasData: Object.keys(columnOptions).length > 0,
                totalColumns: headers.length - 1, // Exclude target variable column
                processedColumns: processedColumns,
                targetVariable: targetVariable
            }
        };

    } catch (error) {
        console.error('Backend - transformScatterPlot: Error processing data:', error);
        return {
            columnOptions: {},
            metadata: {
                hasData: false,
                totalColumns: 0,
                processedColumns: 0,
                error: error.message
            }
        };
    }
};

/**
 * Transforms data into X̄-R control chart format with ECharts configurations.
 * @param {*} data data to be transformed
 * @param {*} payload optional payload containing filters and other parameters
 * @returns Object containing columnOptions with ECharts configurations
 */
export const transformXbarRbar = (data, payload = null) => {
    if (!data || !Array.isArray(data) || data.length === 0) {
        return {
            columnOptions: {},
            metadata: {
                hasData: false,
                totalColumns: 0,
                processedColumns: 0
            }
        };
    }

    try {
        // Extract headers and rows from data
        const headers = Array.isArray(data[0]) ? data[0].map(String) : Object.keys(data[0]);
        const rows = Array.isArray(data[0]) ? data.slice(1) : data;

        const columnOptions = {};
        let processedColumns = 0;

        // Process all numeric columns except DateTime columns
        const numericColumns = headers
            .map((name, index) => ({ index, name }))
            .filter(({ name }) => !name.toLowerCase().includes('datetime'));

        numericColumns.forEach(({ index, name }) => {
            try {
                // Extract values for this column
                const values = rows.map(row => {
                    const value = Array.isArray(row) ? row[index] : row[name];
                    return value !== undefined && value !== null ? value : null;
                });

                // Extract numeric values
                const numericValues = values
                    .map(value => typeof value === 'string' ? parseFloat(value) : value)
                    .filter(value => value !== null && !isNaN(value));

                if (numericValues.length < 10) return; // Need at least 10 points for control charts

                // Group data into samples using configurable subgroup size
                const sampleSize = payload?.configuration?.subgroupSize || 5;
                const samples = [];
                for (let i = 0; i < numericValues.length; i += sampleSize) {
                    const sample = numericValues.slice(i, i + sampleSize);
                    if (sample.length === sampleSize) {
                        samples.push(sample);
                    }
                }

                if (samples.length < 2) return; // Need at least 2 samples

                // Calculate sample means (X̄) and ranges (R)
                const sampleMeans = samples.map(sample =>
                    sample.reduce((sum, val) => sum + val, 0) / sample.length
                );
                const sampleRanges = samples.map(sample =>
                    Math.max(...sample) - Math.min(...sample)
                );

                // Calculate control limits
                const grandMean = sampleMeans.reduce((sum, mean) => sum + mean, 0) / sampleMeans.length;
                const averageRange = sampleRanges.reduce((sum, range) => sum + range, 0) / sampleRanges.length;

                // Control chart constants for sample size 5
                const A2 = 0.577; // For X̄ chart
                const D3 = 0; // For R chart lower limit
                const D4 = 2.114; // For R chart upper limit

                const xbarUCL = grandMean + (A2 * averageRange);
                const xbarLCL = grandMean - (A2 * averageRange);
                const rUCL = D4 * averageRange;
                const rLCL = D3 * averageRange;

                // Create sample indices for x-axis
                const sampleIndices = samples.map((_, index) => index + 1);

                // Create ECharts configuration for X̄-R charts
                columnOptions[name] = {
                    animation: false,
                    tooltip: {
                        trigger: 'axis',
                        formatter: function (params) {
                            let result = `Sample ${params[0].axisValue}<br/>`;
                            params.forEach(param => {
                                result += `${param.seriesName}: ${param.value.toFixed(3)}<br/>`;
                            });
                            return result;
                        }
                    },
                    legend: [
                        {
                            data: ['X̄ (Sample Mean)', 'X̄ UCL', 'X̄ LCL', 'X̄ Center Line'],
                            top: 10,
                            left: 'center',
                            textStyle: { fontSize: 12 }
                        },
                        {
                            data: ['R (Range)', 'R UCL', 'R Center Line'],
                            top: '55%',
                            left: 'center',
                            textStyle: { fontSize: 12 }
                        }
                    ],
                    grid: [
                        {
                            left: '10%',
                            right: '10%',
                            top: '15%',
                            height: '35%',
                            containLabel: true
                        },
                        {
                            left: '10%',
                            right: '10%',
                            top: '60%',
                            height: '35%',
                            containLabel: true
                        }
                    ],
                    xAxis: [
                        {
                            type: 'category',
                            data: sampleIndices,
                            gridIndex: 0
                        },
                        {
                            type: 'category',
                            data: sampleIndices,
                            gridIndex: 1
                        }
                    ],
                    yAxis: [
                        {
                            type: 'value',
                            gridIndex: 0,
                            min: Math.round(Math.min(xbarLCL, ...sampleMeans) * 0.95 * 100) / 100,
                            max: Math.round(Math.max(xbarUCL, ...sampleMeans) * 1.05 * 100) / 100,
                            axisLabel: {
                                formatter: function (value) {
                                    // Format Y-axis values to 2 decimal places maximum
                                    const num = Number(value);
                                    if (num === 0) return '0';
                                    if (Number.isInteger(num)) return num.toString();
                                    return num.toFixed(2);
                                }
                            }
                        },
                        {
                            type: 'value',
                            gridIndex: 1,
                            min: 0,
                            max: Math.round(rUCL * 1.1 * 100) / 100,
                            axisLabel: {
                                formatter: function (value) {
                                    // Format Y-axis values to 2 decimal places maximum
                                    const num = Number(value);
                                    if (num === 0) return '0';
                                    if (Number.isInteger(num)) return num.toString();
                                    return num.toFixed(2);
                                }
                            }
                        }
                    ],
                    series: (() => {
                        // Create data with custom styling for out-of-control points
                        const xbarData = sampleMeans.map((value, index) => {
                            const isOutOfControl = value > xbarUCL || value < xbarLCL;
                            return {
                                value: [index, value],
                                itemStyle: isOutOfControl ? { color: '#ff0000' } : { color: '#1f77b4' }
                            };
                        });

                        const rData = sampleRanges.map((value, index) => {
                            const isOutOfControl = value > rUCL || value < rLCL;
                            return {
                                value: [index, value],
                                itemStyle: isOutOfControl ? { color: '#ff0000' } : { color: '#ff7f0e' }
                            };
                        });

                        return [
                            // X̄ Chart - Connected line with individual point styling
                            {
                                name: 'X̄ (Sample Mean)',
                                type: 'line',
                                data: xbarData,
                                xAxisIndex: 0,
                                yAxisIndex: 0,
                                symbol: 'circle',
                                symbolSize: 6,
                                lineStyle: { width: 2, color: '#1f77b4' },
                                emphasis: {
                                    focus: 'series'
                                }
                            },
                            // X̄ Chart Control Lines
                            {
                                name: 'X̄ UCL',
                                type: 'line',
                                data: new Array(sampleMeans.length).fill(xbarUCL),
                                xAxisIndex: 0,
                                yAxisIndex: 0,
                                lineStyle: { type: 'dashed', color: '#d62728', width: 2 },
                                symbol: 'none',
                                itemStyle: { color: '#d62728' }
                            },
                            {
                                name: 'X̄ LCL',
                                type: 'line',
                                data: new Array(sampleMeans.length).fill(xbarLCL),
                                xAxisIndex: 0,
                                yAxisIndex: 0,
                                lineStyle: { type: 'dashed', color: '#d62728', width: 2 },
                                symbol: 'none',
                                itemStyle: { color: '#d62728' }
                            },
                            {
                                name: 'X̄ Center Line',
                                type: 'line',
                                data: new Array(sampleMeans.length).fill(grandMean),
                                xAxisIndex: 0,
                                yAxisIndex: 0,
                                lineStyle: { type: 'solid', color: '#2ca02c', width: 2 },
                                symbol: 'none',
                                itemStyle: { color: '#2ca02c' }
                            },
                            // R Chart - Connected line with individual point styling
                            {
                                name: 'R (Range)',
                                type: 'line',
                                data: rData,
                                xAxisIndex: 1,
                                yAxisIndex: 1,
                                symbol: 'circle',
                                symbolSize: 6,
                                lineStyle: { width: 2, color: '#ff7f0e' },
                                emphasis: {
                                    focus: 'series'
                                }
                            },
                            // R Chart Control Lines
                            {
                                name: 'R UCL',
                                type: 'line',
                                data: new Array(sampleRanges.length).fill(rUCL),
                                xAxisIndex: 1,
                                yAxisIndex: 1,
                                lineStyle: { type: 'dashed', color: '#d62728', width: 2 },
                                symbol: 'none',
                                itemStyle: { color: '#d62728' }
                            },
                            {
                                name: 'R LCL',
                                type: 'line',
                                data: new Array(sampleRanges.length).fill(rLCL),
                                xAxisIndex: 1,
                                yAxisIndex: 1,
                                lineStyle: { type: 'dashed', color: '#d62728', width: 2 },
                                symbol: 'none',
                                itemStyle: { color: '#d62728' }
                            },
                            {
                                name: 'R Center Line',
                                type: 'line',
                                data: new Array(sampleRanges.length).fill(averageRange),
                                xAxisIndex: 1,
                                yAxisIndex: 1,
                                lineStyle: { type: 'solid', color: '#2ca02c', width: 2 },
                                symbol: 'none',
                                itemStyle: { color: '#2ca02c' }
                            }
                        ];
                    })(),
                    hasData: true,
                    metadata: {
                        columnName: name,
                        sampleCount: samples.length,
                        sampleSize: sampleSize,
                        grandMean: grandMean,
                        averageRange: averageRange,
                        controlLimits: {
                            xbar: { ucl: xbarUCL, lcl: xbarLCL, center: grandMean },
                            r: { ucl: rUCL, lcl: rLCL, center: averageRange }
                        }
                    }
                };

                processedColumns++;
            } catch (columnError) {
                console.error(`Backend - transformXbarRbar: Error processing column ${name}:`, columnError);
            }
        });

        return {
            columnOptions: columnOptions,
            metadata: {
                hasData: Object.keys(columnOptions).length > 0,
                totalColumns: numericColumns.length,
                processedColumns: processedColumns
            }
        };

    } catch (error) {
        console.error('Backend - transformXbarRbar: Error processing data:', error);
        return {
            columnOptions: {},
            metadata: {
                hasData: false,
                totalColumns: 0,
                processedColumns: 0,
                error: error.message
            }
        };
    }
};


/**
 * Transforms data into process capability report format with ECharts configurations.
 * @param {*} data data to be transformed
 * @param {*} payload optional payload containing filters and configuration
 * @returns Object containing columnOptions with ECharts configurations
 */
export const transformProcessCapabilityReport = (data, payload = null) => {
    if (!data || !Array.isArray(data) || data.length === 0) {
        return {
            columnOptions: {},
            metadata: {
                hasData: false,
                totalColumns: 0,
                processedColumns: 0
            }
        };
    }

    try {
        // Extract headers and rows from data
        const headers = Array.isArray(data[0]) ? data[0].map(String) : Object.keys(data[0]);
        const rows = Array.isArray(data[0]) ? data.slice(1) : data;

        // Get configuration from payload
        const config = payload?.configuration || {};
        const { subgroupSize = 5, targetVariable, LSL, USL } = config;

        // Validate configuration
        if (!targetVariable) {
            return {
                columnOptions: {},
                metadata: {
                    hasData: false,
                    totalColumns: 0,
                    processedColumns: 0,
                    error: "No target variable selected. Please configure target variable to generate process capability report."
                }
            };
        }

        if (LSL === undefined || LSL === null || USL === undefined || USL === null) {
            return {
                columnOptions: {},
                metadata: {
                    hasData: false,
                    totalColumns: 0,
                    processedColumns: 0,
                    error: "Please configure valid LSL and USL values."
                }
            };
        }

        if (LSL >= USL) {
            return {
                columnOptions: {},
                metadata: {
                    hasData: false,
                    totalColumns: 0,
                    processedColumns: 0,
                    error: "LSL must be less than USL. Please check your specification limits."
                }
            };
        }

        // Find target variable column
        const targetColumnIndex = headers.findIndex(h => h.toLowerCase() === targetVariable.toLowerCase());
        
        if (targetColumnIndex < 0) {
            return {
                columnOptions: {},
                metadata: {
                    hasData: false,
                    totalColumns: 0,
                    processedColumns: 0,
                    error: `Target variable '${targetVariable}' not found in CSV file`
                }
            };
        }

        // Extract values for target variable
        const values = rows.map(row => {
            const value = Array.isArray(row) ? row[targetColumnIndex] : row[targetVariable];
            return value !== undefined && value !== null ? value : null;
        });

        // Extract numeric values
        const numericValues = values
            .map(value => typeof value === 'string' ? parseFloat(value) : value)
            .filter(value => value !== null && !isNaN(value));

        if (numericValues.length < 10) {
            return {
                columnOptions: {},
                metadata: {
                    hasData: false,
                    totalColumns: 0,
                    processedColumns: 0,
                    error: "Insufficient data for process capability analysis. Need at least 10 data points."
                }
            };
        }

        // Calculate basic statistics
        const n = numericValues.length;
        const mean = numericValues.reduce((sum, val) => sum + val, 0) / n;
        const variance = numericValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / (n - 1);
        const stdOverall = Math.sqrt(variance);

        // Group data into subgroups for within calculation
        const subgroups = [];
        for (let i = 0; i < numericValues.length; i += subgroupSize) {
            const subgroup = numericValues.slice(i, i + subgroupSize);
            if (subgroup.length === subgroupSize) {
                subgroups.push(subgroup);
            }
        }

        if (subgroups.length < 2) {
            return {
                columnOptions: {},
                metadata: {
                    hasData: false,
                    totalColumns: 0,
                    processedColumns: 0,
                    error: `Insufficient data for subgroup analysis. Need at least 2 complete subgroups of size ${subgroupSize}.`
                }
            };
        }

        // Calculate within-subgroup standard deviation using range method
        const subgroupRanges = subgroups.map(subgroup =>
            Math.max(...subgroup) - Math.min(...subgroup)
        );
        const averageRange = subgroupRanges.reduce((sum, range) => sum + range, 0) / subgroupRanges.length;
        
        // Control chart constant for estimating standard deviation from range
        const d2 = subgroupSize === 2 ? 1.128 : subgroupSize === 3 ? 1.693 : subgroupSize === 4 ? 2.059 : subgroupSize === 5 ? 2.326 : 2.534;
        const stdWithin = averageRange / d2;

        // Calculate process capability indices
        const specWidth = USL - LSL;
        const Cp = specWidth / (6 * stdWithin);
        const Pp = specWidth / (6 * stdOverall);
        
        const CpkUpper = (USL - mean) / (3 * stdWithin);
        const CpkLower = (mean - LSL) / (3 * stdWithin);
        const Cpk = Math.min(CpkUpper, CpkLower);
        
        const PpkUpper = (USL - mean) / (3 * stdOverall);
        const PpkLower = (mean - LSL) / (3 * stdOverall);
        const Ppk = Math.min(PpkUpper, PpkLower);

        // Determine capability status
        const getCapabilityStatus = (value) => {
            if (value >= 1.33) return { status: 'Capable', color: '#52c41a' };
            if (value >= 1.00) return { status: 'Marginally Capable', color: '#faad14' };
            return { status: 'Not Capable', color: '#ff4d4f' };
        };

        const cpStatus = getCapabilityStatus(Cp);
        const cpkStatus = getCapabilityStatus(Cpk);
        const ppStatus = getCapabilityStatus(Pp);
        const ppkStatus = getCapabilityStatus(Ppk);

        // Create histogram data for visualization
        const min = Math.min(...numericValues);
        const max = Math.max(...numericValues);
        const binCount = Math.min(20, Math.max(8, Math.ceil(Math.sqrt(numericValues.length))));
        const binWidth = (max - min) / binCount;

        const bins = Array.from({ length: binCount }, (_, i) => ({
            start: min + i * binWidth,
            end: min + (i + 1) * binWidth,
            count: 0
        }));

        // Count values in each bin
        numericValues.forEach(value => {
            let binIndex = Math.floor((value - min) / binWidth);
            binIndex = Math.max(0, Math.min(binCount - 1, binIndex));
            bins[binIndex].count++;
        });

        // Create ECharts configuration
        const echartsOption = {
            animation: false,
            tooltip: {
                trigger: 'axis',
                formatter: function (params) {
                    let result = '';
                    params.forEach(param => {
                        if (param.seriesName === 'Histogram') {
                            result += `${param.seriesName}: ${param.value[1]} data points<br/>`;
                            result += `Range: ${param.value[0].toFixed(3)} - ${(param.value[0] + binWidth).toFixed(3)}<br/>`;
                        } else if (param.seriesName === 'Overall σ' || param.seriesName === 'Within σ') {
                            result += `${param.seriesName}: ${param.value[1].toFixed(2)}<br/>`;
                        } else if (param.seriesName === 'LSL' || param.seriesName === 'USL' || param.seriesName === 'Mean') {
                            // Skip these as they are vertical lines, no meaningful tooltip data
                        } else {
                            result += `${param.seriesName}: ${param.value}<br/>`;
                        }
                    });
                    return result;
                }
            },
            legend: {
                data: ['Histogram', 'LSL', 'USL', 'Mean', 'Overall σ', 'Within σ'],
                top: 10
            },
            grid: {
                left: '10%',
                right: '10%',
                top: '15%',
                bottom: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'value',
                name: targetVariable,
                nameLocation: 'middle',
                nameGap: 30,
                min: Math.min(LSL, min) * 0.95,
                max: Math.max(USL, max) * 1.05,
                axisLabel: {
                    formatter: function(value) { return Number(value).toFixed(3); }
                }
            },
            yAxis: {
                type: 'value',
                name: 'Frequency',
                nameLocation: 'middle',
                nameGap: 50
            },
            series: [
                // Histogram
                {
                    name: 'Histogram',
                    type: 'bar',
                    data: bins.map(bin => [bin.start + binWidth/2, bin.count]),
                    barWidth: '90%',
                    itemStyle: {
                        color: 'rgba(24, 144, 255, 0.6)',
                        borderColor: 'rgba(24, 144, 255, 1)',
                        borderWidth: 1
                    }
                },
                // LSL Line (markLine-only with label)
                {
                    name: 'LSL',
                    type: 'line',
                    data: [],
                    tooltip: { show: false },
                    markLine: {
                        silent: true,
                        symbol: 'none',
                        data: [{ xAxis: LSL }],
                        lineStyle: { color: '#ff7875', width: 3, type: 'dashed' },
                        label: { show: true, formatter: 'LSL', color: '#ff7875', position: 'end' }
                    }
                },
                // USL Line (markLine-only with label)
                {
                    name: 'USL',
                    type: 'line',
                    data: [],
                    tooltip: { show: false },
                    markLine: {
                        silent: true,
                        symbol: 'none',
                        data: [{ xAxis: USL }],
                        lineStyle: { color: '#ff7875', width: 3, type: 'dashed' },
                        label: { show: true, formatter: 'USL', color: '#ff7875', position: 'end' }
                    }
                },
                // Mean Line (markLine only, no line series)
                {
                    name: 'Mean',
                    type: 'line',
                    data: [],
                    tooltip: { show: false },
                    markLine: {
                        silent: true,
                        data: [{
                            xAxis: mean,
                            lineStyle: { color: '#52c41a', width: 2 },
                            label: { show: true, position: 'start', formatter: 'Mean', color: '#52c41a' }
                        }]
                    }
                },
                // Overall normal curve (using stdOverall)
                {
                    name: 'Overall σ',
                    type: 'line',
                    smooth: true,
                    showSymbol: false,
                    lineStyle: { color: '#fa8c16', width: 2 },
                    data: (function(){
                        const points = [];
                        const minX = Math.min(LSL, min) * 0.95;
                        const maxX = Math.max(USL, max) * 1.05;
                        const step = (maxX - minX) / 100;
                        const peak = Math.max(...bins.map(b => b.count));
                        for (let x = minX; x <= maxX; x += step) {
                            const y = (1/(stdOverall*Math.sqrt(2*Math.PI))) * Math.exp(-0.5*Math.pow((x-mean)/stdOverall,2));
                            const normalizedY = y * peak / Math.max.apply(null, (function(){
                                const xs = [];
                                for (let t = minX; t <= maxX; t += step) {
                                    xs.push((1/(stdOverall*Math.sqrt(2*Math.PI))) * Math.exp(-0.5*Math.pow((t-mean)/stdOverall,2)));
                                }
                                return xs;
                            })());
                            points.push([parseFloat(x.toFixed(4)), parseFloat(normalizedY.toFixed(2))]);
                        }
                        return points;
                    })()
                },
                // Within normal curve (using stdWithin)
                {
                    name: 'Within σ',
                    type: 'line',
                    smooth: true,
                    showSymbol: false,
                    lineStyle: { color: '#595959', width: 2, type: 'dashed' },
                    data: (function(){
                        const points = [];
                        const minX = Math.min(LSL, min) * 0.95;
                        const maxX = Math.max(USL, max) * 1.05;
                        const step = (maxX - minX) / 100;
                        const peak = Math.max(...bins.map(b => b.count));
                        for (let x = minX; x <= maxX; x += step) {
                            const y = (1/(stdWithin*Math.sqrt(2*Math.PI))) * Math.exp(-0.5*Math.pow((x-mean)/stdWithin,2));
                            const normalizedY = y * peak / Math.max.apply(null, (function(){
                                const xs = [];
                                for (let t = minX; t <= maxX; t += step) {
                                    xs.push((1/(stdWithin*Math.sqrt(2*Math.PI))) * Math.exp(-0.5*Math.pow((t-mean)/stdWithin,2)));
                                }
                                return xs;
                            })());
                            points.push([parseFloat(x.toFixed(4)), parseFloat(normalizedY.toFixed(2))]);
                        }
                        return points;
                    })()
                }
            ]
        };

        // Return the complete configuration
        const result = {
            columnOptions: {
                [targetVariable]: {
                    echartsOption,
                    statistics: {
                        sampleSize: n,
                        mean: mean.toFixed(4),
                        stdWithin: stdWithin.toFixed(4),
                        stdOverall: stdOverall.toFixed(4),
                        Cp: Cp.toFixed(3),
                        Cpk: Cpk.toFixed(3),
                        Pp: Pp.toFixed(3),
                        Ppk: Ppk.toFixed(3),
                        LSL: LSL,
                        USL: USL,
                        cpStatus,
                        cpkStatus,
                        ppStatus,
                        ppkStatus
                    },
                    metadata: {
                        targetVariable,
                        subgroupSize,
                        subgroupCount: subgroups.length,
                        totalDataPoints: n
                    }
                }
            },
            metadata: {
                hasData: true,
                totalColumns: 1,
                processedColumns: 1,
                targetVariable,
                configuration: config
            }
        };

        return result;

    } catch (error) {
        console.error('Backend - transformProcessCapabilityReport: Error processing data:', error);
        return {
            columnOptions: {},
            metadata: {
                hasData: false,
                totalColumns: 0,
                processedColumns: 0,
                error: error.message
            }
        };
    }
};

/**
 * Transforms panel data based on the type of panel.
 * @param {*} data data to be transformed
 * @param {*} type type of panel (e.g., 'TimeSeriesPanel', 'OverviewPanel', 'HistogramPanel')
 * @param {*} payload optional payload containing filters and other parameters
 * @returns
 */
export const transformPanelData = (data, type, payload = null) => {
    if (!data || !Array.isArray(data)) {
        return [];
    }

    switch (type) {
        case 'TimeSeriesPanel':
            const transformedData = transformTimeSeries(data);
            return transformedData;
        case 'HistogramPanel':
            return transformHistogram(data);
        case 'OverviewPanel':
            return transformOverview(data);
        case 'DataTablePanel':
            return data;
        case 'ScatterPlotPanel':
            return transformScatterPlot(data, payload);
        case 'XbarRbarPanel':
            return transformXbarRbar(data, payload);
        case 'ProcessCapabilityReportPanel':
            return transformProcessCapabilityReport(data, payload);
        default:
            return data;
    }
}

/**
 * Filters the panel data based on the provided filters.
 *
 * @param {Array} results - The array of panel data to be filtered.
 * @param {Object} filters - The filters to apply to the panel data.
 * @returns {Array} The filtered array of panel data. Returns an empty array if results is not a valid array.
 */
export const filterPanelData = (results, filters) => {
    if (!results || !Array.isArray(results)) {
        return [];
    }

    let filteredResults =  [...results];

    // Filter based on conditional filters
    if (filters['conditionalFilters'] && Array.isArray(filters['conditionalFilters']) && filters['conditionalFilters'].length > 0) {
        filteredResults =  filterByConditionalFilters(filteredResults, filters['conditionalFilters']);
    }

    // Filter based on date range if provided
    if (filters['dateFilter'] && filters['dateFilter']['startDate'] && filters['dateFilter']['endDate']) {
        filteredResults =  filterByDateRange(filteredResults, filters['dateFilter']['startDate'], filters['dateFilter']['endDate']);
    }

    // Filter based on annotation filters if provided
    if (filters['annotationFilters'] && Array.isArray(filters['annotationFilters']) && filters['annotationFilters'].length > 0) {
        filteredResults =  filterByAnnotations(filteredResults, filters['annotationFilters']);
    }

    // Filter based on operation filters if provided
    if (filters['operationFilters'] && Array.isArray(filters['operationFilters']) && filters['operationFilters'].length > 0) {
        filteredResults =  filterByOperations(filteredResults, filters['operationFilters']);
    }

    // If no specific panel type is requested, return all results
    return filteredResults;
}

/**
 * Filters the panel data based on the provided filters.
 *
 * @param {Array} results - The array of panel data to be filtered.
 * @param {Object} filters - The filters to apply to the panel data.
 * @returns {Array} The filtered array of panel data. Returns an empty array if results is not a valid array.
 */
export const filterPanelDataNew = (results, filters) => {
    if (!results || !Array.isArray(results)) {
        return [];
    }

    // Parse headers and rows
    const headers = Array.isArray(results[0]) ? results[0].map(String) : Object.keys(results[0]);
    const rows = Array.isArray(results[0]) ? results.slice(1) : results;

    // Prepare filter conditions with timezone-aware date conversion
    const dateColumnIndex = headers.findIndex(h => h === 'DateTime' || h === 'datetime' || h === 'Datetime' || h === 'date' || h === 'Date' || h === '_time');
    const startTimestamp = filters.dateFilter?.startDate ? convertToDataTimezone(filters.dateFilter.startDate).getTime() : null;
    const endTimestamp = filters.dateFilter?.endDate ? convertToDataTimezone(filters.dateFilter.endDate).getTime() : null;

    // Build a single filter function
    const shouldInclude = (row) => {
        // Date filter
        if (dateColumnIndex >= 0 && startTimestamp && endTimestamp) {
            const dateValue = Array.isArray(row) ? row[dateColumnIndex] : row[headers[dateColumnIndex]];
            const dateTimestamp = new Date(dateValue).getTime();
            if (isNaN(dateTimestamp) || dateTimestamp < startTimestamp || dateTimestamp > endTimestamp) return false;
        }

        // Annotation filters
        if (filters.annotationFilters?.length) {
            for (const annotation of filters.annotationFilters) {
                const { columnName, x0, x1, y0, y1 } = annotation;
                const colIdx = headers.indexOf(columnName);
                if (colIdx < 0) continue;
                const xValue = Array.isArray(row) ? row[colIdx] : row[columnName];
                const xTimestamp = new Date(xValue).getTime();
                const featureValue = Array.isArray(row) ? row[colIdx + 1] : row[headers[colIdx + 1]];
                if (isNaN(xTimestamp) || xTimestamp < x0 || xTimestamp > x1 || featureValue < y0 || featureValue > y1) return false;
            }
        }

        // Operation filters
        if (filters.operationFilters?.length) {
            for (const operation of filters.operationFilters) {
                const { columnName, y0, y1 } = operation;
                const colIdx = headers.indexOf(columnName);
                if (colIdx < 0) continue;
                const featureValue = Array.isArray(row) ? row[colIdx + 1] : row[headers[colIdx + 1]];
                if (featureValue < y0 || featureValue > y1) return false;
            }
        }

        return true;
    };

    // Filter rows in a single pass
    const filteredRows = rows.filter(shouldInclude);

    // Return in the same format as input
    return filteredRows.length > 0 && Array.isArray(results[0])
        ? [headers, ...filteredRows]
        : filteredRows;
}

/**
 * Filters the data by a specified date range.
 *
 * @param {Array} data - The data to filter.
 * @param {Date} startDate - The start date of the range.
 * @param {Date} endDate - The end date of the range.
 * @returns {Array} Filtered data within the specified date range.
 */
export const filterByDateRange = (data, startDate, endDate) => {
    if (!data || !Array.isArray(data) || data.length === 0) {
        return [];
    }

    try {
        // Extract headers and rows from data
        const headers = Array.isArray(data[0]) ? data[0].map(String) : Object.keys(data[0]);
        const rows = Array.isArray(data[0]) ? data.slice(1) : data;

        const dateColumnIndex = headers.findIndex(h => h.toLowerCase() === 'datetime');
        if (dateColumnIndex < 0) {
            console.warn('No DateTime column found for filtering by date range.');
            return data; // No filtering possible, return original data
        }

        // Convert start and end dates to timestamps with timezone awareness
        const startTimestamp = typeof startDate === 'number' ? startDate : convertToDataTimezone(startDate).getTime();
        const endTimestamp = typeof endDate === 'number' ? endDate : convertToDataTimezone(endDate).getTime();

        if (isNaN(startTimestamp) || isNaN(endTimestamp)) {
            console.error('Invalid date range provided for filtering:', startDate, endDate);
            return []; // Invalid date range, return empty array
        }

        // Filter rows based on the date range
        const filteredRows = rows.filter(row => {
            const dateValue = Array.isArray(row) ? row[dateColumnIndex] : row[headers[dateColumnIndex]];
            const dateTimestamp = new Date(dateValue).getTime();
            return dateTimestamp >= startTimestamp && dateTimestamp <= endTimestamp;
        });

        // Return headers with filtered rows or empty array if no data matches
        return filteredRows.length > 0 ? [headers, ...filteredRows] : [];

    } catch (error) {
        console.error('Error filtering data by date range:', error);
        return [];
    }
}

export const filterByConditionalFilters = (data, conditionalFilters) => {
    if (!data || !Array.isArray(data) || data.length === 0) {
        return [];
    }

    try {
        const originalHeaders = data[0] && Array.isArray(data[0]) ? data[0] : [];

        conditionalFilters.forEach(filter => {
            if (
                filter.enabled &&
                filter.type === 'conditional-column' &&
                filter.column &&
                filter.operator === 'by_value' &&
                Array.isArray(filter.value)
            ) {
                data = data.filter(row => {
                    // Support both object and array row formats
                    if (typeof row === 'object' && !Array.isArray(row)) {
                        // Object format
                        return filter.value.includes(String(row[filter.column]));
                    } else if (Array.isArray(row)) {
                        const colIdx = originalHeaders.findIndex(h => h === filter.column);
                        if (colIdx === -1) return false;
                        return filter.value.includes(String(row[colIdx]));
                    }
                    return false;
                });
            }
        });
        return data;
    }
    catch(error) {
        console.error('Error filtering data by conditional filters:', error);
        return [];
    }
}

export const filterByAnnotations = (data, annotations) => {
    if (!data || !Array.isArray(data) || data.length === 0) {
        return [];
    }

    try {
        // Parse headers and rows to handle both data formats
        const headers = Array.isArray(data[0]) ? data[0].map(String) : Object.keys(data[0]);
        const rows = Array.isArray(data[0]) ? data.slice(1) : data;

        annotations.forEach(annotation => {
            const { columnName, x0, x1, y0, y1 } = annotation;
            const columnIndex = headers.indexOf(columnName);
            
            // Find DateTime column
            const dateColumnIndex = headers.findIndex(h => 
                h === 'DateTime' || h === 'datetime' || h === 'Datetime' || 
                h === 'date' || h === 'Date' || h === '_time'
            );

            // Check if columns exist and coordinates are valid
            if (columnIndex < 0 || dateColumnIndex < 0 || isNaN(x0) || isNaN(x1) || x0 >= x1) {
                console.warn(`Invalid annotation for column ${columnName}:`, annotation);
                return; // Skip invalid annotations
            }

            // Filter rows - remove points within annotation bounds
            const filteredRows = rows.filter(row => {
                const xValue = Array.isArray(row) ? row[dateColumnIndex] : row[headers[dateColumnIndex]];
                const xTimestamp = new Date(xValue).getTime();
                const featureValue = Array.isArray(row) ? row[columnIndex] : row[columnName];
                const numFeatureValue = parseFloat(featureValue);
                
                // Check if point is within annotation bounds
                const isWithinBounds = xTimestamp >= x0 && xTimestamp <= x1 && 
                                     numFeatureValue >= y0 && numFeatureValue <= y1;
                
                // remove points within annotation (return false to exclude them)
                return !isWithinBounds;
            });

            // Update data for next iteration
            data = Array.isArray(data[0]) ? [headers, ...filteredRows] : filteredRows;
        });

        return data;
    } catch (error) {
        console.error('Error filtering data by annotations:', error);
        return [];
    }
}

export const filterByOperations = (data, operations) => {
    if (!data || !Array.isArray(data) || data.length === 0) {
        return [];
    }

    try {
        operations.forEach(operation => {
            const { columnName, y0, y1 } = operation;

            if (!columnName || !(columnName in data[0])) {
                console.warn(`Invalid column: ${columnName}`);
                return; // skip invalid operation
            }

            // Filter rows based on the operation's y0 and y1 values
            data = data.filter(row => {
                const featureValue = Number(row[columnName]); // convert to number if numeric
                if (isNaN(featureValue)) return false; // skip non-numeric values
                return featureValue >= y0 && featureValue <= y1;
            });
        });

        return data;
    } catch (error) {
        console.error('Error filtering data by operations:', error);
        return [];
    }
}
import { get } from "mongoose";
import Operations from "../models/operation.model.js";

/**
 * Prepares the function code, dependencies, and name for a given node.
 * If the node is a custom code node, extracts code and dependencies from its settings.
 * Otherwise, fetches the operation config from the database.
 * @param {Object} node - The node object containing nodeType and settings.
 * @returns {Promise<{code: string, dependencies: string[], name: string}>} The function code, dependencies, and name.
 */
export const prepareFunctionCode = async (node) => {
    try {
        // Check if custom-code node
        if (node.data.nodeType === 'monaco-input') {
            // Extract python code and dependencies from the code
            const code = node.data.settings.monacoCode;
            const importRegex = /import\s+.*\s+from\s+['"](.*)['"]/g;

            let match;
            let dependencies = [];
            while ((match = importRegex.exec(code)) !== null) {
                dependencies.push(match[1]);
            }
    
            // Extract function name. Assuming function is defined as `def functionName(...):`
            const functionNameRegex = /def\s+(\w+)\s*\(/;
            const functionMatch = functionNameRegex.exec(code);
            let functionName = '';
    
            if (functionMatch) {
                functionName = functionMatch[1];
            }
            return {
                'code': code,
                'dependencies': dependencies,
                'name': functionName
            };
        }
    
        // Get node config from operations
        const operationConfig = await Operations.findOne({ where: { alias: node.data.nodeType } });
        if (!operationConfig) {
            console.error(`Operation with type ${node.data.nodeType} not found`);
            return { 'code': '', 'dependencies': [], 'name': '' };
        }
    
        return {
            'code': operationConfig.dataValues.config.functionCode,
            'dependencies': operationConfig.dataValues.config.dependencies,
            'name': operationConfig.dataValues.config.functionName
        };
    } catch (error) {
        throw new Error('\nError preparing function code: ' + error.message);
    }
}

/**
 * Prepares the full script code for a list of nodes, including imports, function definitions, and main function body.
 * @param {Array<Object>} nodeList - List of node objects to process in sequence.
 * @returns {Promise<string>} The complete script code as a string.
 */
export const prepareScriptCode = async (nodeList) => {
    let scriptBody = 'def main(data, config):\n';
    let importDependencies = '';
    let definitions = '';
    let varCount = 1;

    try {
        if (nodeList.length === 0) {
            throw new Error('\nNo nodes found in workflow structure');
        }

        // Process each node in sequence
        for (const node of nodeList) {
            const { code, dependencies, name } = await prepareFunctionCode(node);

            if (dependencies && dependencies.length > 0) {
                dependencies.forEach(dep => {
                    // Handle dependencies with aliasing
                    if (dep.includes(' as ')) {
                        const [packageName, alias] = dep.split(' as ').map(s => s.trim());
                        if (!importDependencies.includes(packageName)) {
                            importDependencies += `import ${packageName} as ${alias};\n`;
                        }
                    }
                    // Avoid duplicate imports
                    if (!importDependencies.includes(dep)) {
                        importDependencies += `import ${dep};\n`;
                    }
                });
            }
            if (code) {
                definitions += `\n${code}\n`;
            }
            if (name && name !== '') {
                if (varCount === 1) {
                    scriptBody += `    df_${varCount} = ${name}(config["${node.data.nodeType}"]);\n`;
                } else {
                    scriptBody += `    df_${varCount} = ${name}(df_${varCount - 1}, config["${node.data.nodeType}"]);\n`;
                }
                varCount += 1;
            }
        }

        scriptBody += `    return df_${varCount - 1}\n`;

        return importDependencies + definitions + '\n' + scriptBody;

    } catch (error) {
        throw new Error('\nError preparing script code: ' + error.message);
    }
}

/**
 * Prepares a configuration object from a list of node data.
 * @param {Array<Object>} nodeData - List of node objects with type and settings.
 * @param {Object} workflowStructure - The workflow structure containing nodes and edges arrays.
 * @returns {Promise<Object>} The configuration object mapping node types to settings.
 */
export const prepareScriptConfig = async (nodeData, workflowStructure) => {
    let config = {}
    const { nodes, edges } = workflowStructure;

    nodeData.map((node) => {
        // console.log('node in prepareScriptConfig', node);
        // Check if node has data.
        if (!node.data) {
            const {type, settings} = node;
            config[type] = settings;
            console.log('No data in node, using type and settings directly:');
        } else {
            const {nodeType, settings } = node.data;
            config[nodeType] = settings;

            // If node is a custom node, get node config from connected json node.
            if (nodeType === 'monaco-input') {
                // Find connected json node
                const connectedEdge = edges.find(e => e.target === node.id && nodes.find(n => n.id === e.source && n.data && n.data.nodeType === 'json-script'));
                // const connectedEdge = edges.find(e => e.target === node.id);
                if (connectedEdge) {
                    const sourceNodeId = connectedEdge.source;
                    const sourceNode = nodes.find(n => n.id === sourceNodeId);
                    if (sourceNode && sourceNode.data && sourceNode.data.nodeType === 'json-script') {
                        config[nodeType] = sourceNode.data.settings.data;
                    }
                }
            }
        }
    })

    return config;
}

/**
 * Returns the ordered list of nodes from a workflow structure based on edges.
 * Finds the source node and traverses the workflow in sequence.
 * @param {Object} workflowStructure - The workflow structure containing nodes and edges arrays.
 * @returns {Array<Object>} Ordered list of nodes as per workflow sequence.
 */
export const getNodesFromStructure = (workflowStructure) => {
    const { nodes, edges } = workflowStructure;

    if (nodes.length === 0) return [];
    if (edges.length === 0) return nodes; // No edges, return all nodes as isolated

    // Find source node (node with no incoming edges)
    const targetIds = new Set(edges.map(e => e.target));
    const sourceNode = nodes.find(n => !targetIds.has(n.id));
    if (!sourceNode) return [];

    const nodeOrder = [];
    let currentNode = sourceNode;
    nodeOrder.push(currentNode);

    // Traverse edges to find the next node in sequence
    while (true) {
        const nextEdge = edges.find(e => e.source === currentNode.id);
        if (!nextEdge) break;
        const nextNode = nodes.find(n => n.id === nextEdge.target);
        if (!nextNode) break;
        nodeOrder.push(nextNode);
        currentNode = nextNode;
    }
    return nodeOrder;
}
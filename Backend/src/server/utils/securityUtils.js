import crypto from 'crypto';
import { UAParser } from 'ua-parser-js';

/**
 * Extract client IP address from request
 * Handles various proxy scenarios and headers
 */
export const extractClientIP = (req) => {
  // Check for IP from various headers (proxy scenarios)
  const forwarded = req.headers['x-forwarded-for'];
  const realIP = req.headers['x-real-ip'];
  const clientIP = req.headers['x-client-ip'];
  
  if (forwarded) {
    // X-Forwarded-For can contain multiple IPs, take the first one
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  if (clientIP) {
    return clientIP;
  }
  
  // Fallback to connection remote address
  return req.connection?.remoteAddress || 
         req.socket?.remoteAddress || 
         req.ip || 
         'unknown';
};

/**
 * Generate browser fingerprint from request headers
 * Creates a hash from various browser characteristics
 */
export const generateBrowserFingerprint = (req) => {
  const userAgent = req.headers['user-agent'] || '';
  const acceptLanguage = req.headers['accept-language'] || '';
  const acceptEncoding = req.headers['accept-encoding'] || '';
  const accept = req.headers['accept'] || '';
  
  // Parse user agent for more detailed info
  const parser = new UAParser(userAgent);
  const result = parser.getResult();
  
  // Create fingerprint components
  const fingerprintData = {
    browser: `${result.browser.name || 'unknown'}_${result.browser.version || 'unknown'}`,
    os: `${result.os.name || 'unknown'}_${result.os.version || 'unknown'}`,
    device: `${result.device.type || 'desktop'}_${result.device.model || 'unknown'}`,
    userAgent: userAgent.substring(0, 200), // Limit length
    acceptLanguage,
    acceptEncoding: 'br,gzip', // for now using this static to avoid frequent changes
    accept: accept.substring(0, 100) // Limit length
  };
  
  // Create hash from fingerprint data
  const fingerprintString = JSON.stringify(fingerprintData);
  return crypto.createHash('sha256').update(fingerprintString).digest('hex');
};

/**
 * Validate if current IP matches the token IP
 * Allows for some flexibility in IP validation
 */
export const validateIP = (tokenIP, currentIP, strictMode = false) => {
  if (!tokenIP || !currentIP || tokenIP === 'unknown' || currentIP === 'unknown') {
    return !strictMode; // Allow if not in strict mode
  }
  
  // Exact match
  if (tokenIP === currentIP) {
    return true;
  }
  
  if (strictMode) {
    return false;
  }
  
  // Allow for IPv4 subnet flexibility (same /24 network)
  if (isIPv4(tokenIP) && isIPv4(currentIP)) {
    const tokenParts = tokenIP.split('.');
    const currentParts = currentIP.split('.');
    
    // Check if first 3 octets match (same /24 subnet)
    if (tokenParts.length === 4 && currentParts.length === 4) {
      return tokenParts[0] === currentParts[0] && 
             tokenParts[1] === currentParts[1] && 
             tokenParts[2] === currentParts[2];
    }
  }
  
  return false;
};

/**
 * Check if IP is IPv4 format
 */
const isIPv4 = (ip) => {
  const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
  return ipv4Regex.test(ip);
};

/**
 * Validate browser fingerprint
 */
export const validateBrowserFingerprint = (tokenFingerprint, currentFingerprint, strictMode = false) => {
  if (!tokenFingerprint || !currentFingerprint) {
    return !strictMode; // Allow if not in strict mode
  }
  
  // Exact match
  if (tokenFingerprint === currentFingerprint) {
    return true;
  }
  
  if (strictMode) {
    return false;
  }
  
  // In non-strict mode, allow minor browser updates
  // This is a simplified approach - you might want more sophisticated logic
  return false; // For now, require exact match for fingerprint
};

/**
 * Generate security context for token
 */
export const generateSecurityContext = (req) => {
  const ip = extractClientIP(req);
  const fingerprint = generateBrowserFingerprint(req);
  
  return {
    ip,
    fingerprint,
    timestamp: Date.now(),
    userAgent: req.headers['user-agent'] || 'unknown'
  };
};

/**
 * Validate security context
 */
export const validateSecurityContext = (tokenContext, currentContext, options = {}) => {
  const { strictIP = false, strictFingerprint = false } = options;
  
  const ipValid = validateIP(tokenContext.ip, currentContext.ip, strictIP);
  const fingerprintValid = validateBrowserFingerprint(
    tokenContext.fingerprint, 
    currentContext.fingerprint, 
    strictFingerprint
  );
  
  return {
    valid: ipValid && fingerprintValid,
    ipValid,
    fingerprintValid,
    details: {
      tokenIP: tokenContext.ip,
      currentIP: currentContext.ip,
      tokenFingerprint: tokenContext.fingerprint,
      currentFingerprint: currentContext.fingerprint
    }
  };
};

/**
 * Log security event
 */
export const logSecurityEvent = (event, details = {}) => {
  const timestamp = new Date().toISOString();
  console.log(`[SECURITY] ${timestamp} - ${event}:`, details);
  
  // Here you could integrate with your logging system
  // or send to a security monitoring service
};

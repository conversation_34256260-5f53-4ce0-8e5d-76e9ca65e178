{"version": 3, "file": "static/css/main.0512e49c.css", "mappings": "AA8Bo9U,gBAAgG,C,6GC9BpjV,mDAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd;;CAAc,CAAd,uCAAc,CAAd,qBAAc,CAAd,8BAAc,CAAd,wCAAc,CAAd,4BAAc,CAAd,uCAAc,CAAd,gHAAc,CAAd,8BAAc,CAAd,eAAc,CAAd,UAAc,CAAd,wBAAc,CAAd,QAAc,CAAd,uBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,4DAAc,CAAd,gCAAc,CAAd,mCAAc,CAAd,mBAAc,CAAd,eAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,8CAAc,CAAd,mGAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,sBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,mEAAc,CAAd,aAAc,CAAd,mBAAc,CAAd,cAAc,CAAd,+BAAc,CAAd,mBAAc,CAAd,sBAAc,CAAd,mBAAc,CAAd,QAAc,CAAd,SAAc,CAAd,iCAAc,CAAd,gHAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,gCAAc,CAAd,+BAAc,CAAd,mEAAc,CAAd,0CAAc,CAAd,mBAAc,CAAd,mDAAc,CAAd,sDAAc,CAAd,YAAc,CAAd,yBAAc,CAAd,2DAAc,CAAd,iBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,QAAc,CAAd,SAAc,CAAd,gBAAc,CAAd,wBAAc,CAAd,sDAAc,CAAd,SAAc,CAAd,mCAAc,CAAd,wBAAc,CAAd,4DAAc,CAAd,qBAAc,CAAd,qBAAc,CAAd,cAAc,CAAd,uDAAc,CACd,iCAAoB,CAApB,qBAAoB,CAApB,+DAAoB,CAApB,0BAAoB,EAApB,+DAAoB,CAApB,0BAAoB,EAApB,iEAAoB,CAApB,2BAAoB,EAApB,iEAAoB,CAApB,2BAAoB,EAApB,iEAAoB,CAApB,2BAAoB,EACpB,2BAAmB,CAAnB,yBAAmB,CAAnB,WAAmB,CAAnB,eAAmB,CAAnB,SAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,SAAmB,CAAnB,wCAAmB,CAAnB,uCAAmB,CAAnB,2BAAmB,CAAnB,4BAAmB,CAAnB,yCAAmB,CAAnB,6BAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,2BAAmB,CAAnB,uBAAmB,CAAnB,gBAAmB,CAAnB,qBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,wBAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,cAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,oBAAmB,CAAnB,mBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,YAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,sBAAmB,CAAnB,wBAAmB,CAAnB,sBAAmB,CAAnB,qBAAmB,CAAnB,qBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,kBAAmB,CAAnB,sBAAmB,CAAnB,aAAmB,CAAnB,iBAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,iBAAmB,CAAnB,4CAAmB,CAAnB,4CAAmB,CAAnB,wCAAmB,CAAnB,iCAAmB,CAAnB,sCAAmB,CAAnB,qCAAmB,CAAnB,oCAAmB,CAAnB,8BAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,qBAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,8BAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,2BAAmB,CAAnB,0BAAmB,CAAnB,2BAAmB,CAAnB,wBAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,uBAAmB,CAAnB,uBAAmB,CAAnB,wBAAmB,CAAnB,sBAAmB,CAAnB,yBAAmB,CAAnB,wBAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,wBAAmB,CAAnB,wBAAmB,CAAnB,yBAAmB,CAAnB,uBAAmB,CAAnB,iCAAmB,CAAnB,kBAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,wBAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,4BAAmB,CAAnB,wBAAmB,CAAnB,yCAAmB,CAAnB,wCAAmB,CAAnB,eAAmB,CAAnB,oBAAmB,CAAnB,kCAAmB,CAAnB,sBAAmB,CAAnB,kBAAmB,CAAnB,gCAAmB,CAAnB,oBAAmB,CAAnB,kBAAmB,CAAnB,gCAAmB,CAAnB,oBAAmB,CAAnB,+BAAmB,CAAnB,kCAAmB,CAAnB,8BAAmB,CAAnB,oCAAmB,CAAnB,8BAAmB,CAAnB,qCAAmB,CAAnB,+BAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,gBAAmB,CAAnB,mBAAmB,CAAnB,uBAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,qBAAmB,CAAnB,yBAAmB,CAAnB,uBAAmB,CAAnB,yBAAmB,CAAnB,qBAAmB,CAAnB,yBAAmB,CAAnB,qBAAmB,CAAnB,yBAAmB,CAAnB,qBAAmB,CAAnB,uBAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,oDAAmB,CAAnB,kDAAmB,CAAnB,kDAAmB,CAAnB,mBAAmB,CAAnB,mBAAmB,CAAnB,qBAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,mCAAmB,CAAnB,iCAAmB,CAAnB,iCAAmB,CAAnB,iCAAmB,CAAnB,iCAAmB,CAAnB,iCAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,+BAAmB,CAAnB,iCAAmB,CAAnB,+BAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,wBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,eAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,eAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,wBAAmB,CAAnB,qBAAmB,CAAnB,wBAAmB,CAAnB,wBAAmB,CAAnB,qBAAmB,CAAnB,sBAAmB,CAAnB,mDAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,6BAAmB,CAAnB,yBAAmB,CAAnB,uBAAmB,CAAnB,yBAAmB,CAAnB,uBAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,8BAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,gCAAmB,CAAnB,kCAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,4BAAmB,CAAnB,sBAAmB,CAAnB,6BAAmB,CAAnB,wCAAmB,CAAnB,4NAAmB,CAAnB,8BAAmB,CAAnB,4BAAmB,CAAnB,mNAAmB,CAAnB,0CAAmB,EAAnB,+CAAmB,CAAnB,wBAAmB,CAAnB,wBAAmB,CAAnB,sCAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,uCAAmB,CAAnB,+BAAmB,CAAnB,0DAAmB,CAAnB,0DAAmB,CAAnB,0DAAmB,CAAnB,4BAAmB,CAAnB,+BAAmB,CAAnB,yBAAmB,CAAnB,mCAAmB,CAAnB,+BAAmB,CAAnB,gCAAmB,CAAnB,yCAAmB,CAAnB,qCAAmB,CAAnB,sCAAmB,CAAnB,8CAAmB,CAAnB,wBAAmB,CAAnB,qBAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,qBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,uBAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,oHAAmB,CAAnB,oEAAmB,CAAnB,sDAAmB,CAAnB,4BAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,oCAAmB,CAAnB,yBAAmB,CAAnB,kBAAmB,CAAnB,+CAAmB,CAAnB,qCAAmB,CAAnB,yCAAmB,CAAnB,yCAAmB,CAAnB,+BAAmB,CAAnB,6BAAmB,CAAnB,+BAAmB,CAAnB,oCAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,+BAAmB,CAAnB,iCAAmB,CAAnB,iCAAmB,CAAnB,gCAAmB,CAAnB,kFAAmB,CAAnB,8EAAmB,CAAnB,0CAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wBAAmB,CAAnB,0CAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,mCAAmB,CAAnB,+BAAmB,CAAnB,iCAAmB,CAAnB,gCAAmB,CAAnB,kCAAmB,CAAnB,8BAAmB,CAAnB,8BAAmB,CAAnB,4CAAmB,CAAnB,gCAAmB,CAAnB,kCAAmB,CAAnB,0CAAmB,CAAnB,8BAAmB,CAAnB,mDAAmB,CAAnB,gEAAmB,CAAnB,iDAAmB,CAAnB,6DAAmB,CAAnB,6CAAmB,CAAnB,yCAAmB,CAAnB,0CAAmB,CAAnB,mDAAmB,CAAnB,0CAAmB,CAAnB,qDAAmB,CAAnB,0CAAmB,CAAnB,mDAAmB,CAAnB,0CAAmB,CAAnB,sDAAmB,CAAnB,0CAAmB,CAAnB,sDAAmB,CAAnB,0CAAmB,CAAnB,sDAAmB,CAAnB,0CAAmB,CAAnB,sDAAmB,CAAnB,0CAAmB,CAAnB,sDAAmB,CAAnB,0CAAmB,CAAnB,sDAAmB,CAAnB,0CAAmB,CAAnB,sDAAmB,CAAnB,0CAAmB,CAAnB,sDAAmB,CAAnB,0CAAmB,CAAnB,sDAAmB,CAAnB,0CAAmB,CAAnB,sDAAmB,CAAnB,0CAAmB,CAAnB,sDAAmB,CAAnB,uCAAmB,CAAnB,sDAAmB,CAAnB,6CAAmB,CAAnB,0CAAmB,CAAnB,sDAAmB,CAAnB,sCAAmB,CAAnB,sDAAmB,CAAnB,sCAAmB,CAAnB,qDAAmB,CAAnB,sCAAmB,CAAnB,sDAAmB,CAAnB,sCAAmB,CAAnB,sDAAmB,CAAnB,sCAAmB,CAAnB,sDAAmB,CAAnB,uCAAmB,CAAnB,sDAAmB,CAAnB,uCAAmB,CAAnB,sDAAmB,CAAnB,qCAAmB,CAAnB,mDAAmB,CAAnB,wCAAmB,CAAnB,qDAAmB,CAAnB,qCAAmB,CAAnB,sDAAmB,CAAnB,qCAAmB,CAAnB,sDAAmB,CAAnB,qCAAmB,CAAnB,oDAAmB,CAAnB,sCAAmB,CAAnB,mCAAmB,CAAnB,sDAAmB,CAAnB,yCAAmB,CAAnB,6DAAmB,CAAnB,oDAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,kDAAmB,CAAnB,kCAAmB,CAAnB,mDAAmB,CAAnB,kCAAmB,CAAnB,mDAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,mDAAmB,CAAnB,kCAAmB,CAAnB,mDAAmB,CAAnB,kCAAmB,CAAnB,qDAAmB,CAAnB,kCAAmB,CAAnB,oDAAmB,CAAnB,kCAAmB,CAAnB,sDAAmB,CAAnB,kCAAmB,CAAnB,sDAAmB,CAAnB,kCAAmB,CAAnB,sDAAmB,CAAnB,kCAAmB,CAAnB,sDAAmB,CAAnB,kCAAmB,CAAnB,sDAAmB,CAAnB,kCAAmB,CAAnB,sDAAmB,CAAnB,kCAAmB,CAAnB,sDAAmB,CAAnB,kCAAmB,CAAnB,sDAAmB,CAAnB,kCAAmB,CAAnB,sDAAmB,CAAnB,kCAAmB,CAAnB,sDAAmB,CAAnB,kCAAmB,CAAnB,sDAAmB,CAAnB,kCAAmB,CAAnB,sDAAmB,CAAnB,kCAAmB,CAAnB,sDAAmB,CAAnB,kCAAmB,CAAnB,sDAAmB,CAAnB,kCAAmB,CAAnB,sDAAmB,CAAnB,kCAAmB,CAAnB,sDAAmB,CAAnB,kCAAmB,CAAnB,sDAAmB,CAAnB,kCAAmB,CAAnB,sDAAmB,CAAnB,kCAAmB,CAAnB,sDAAmB,CAAnB,kCAAmB,CAAnB,sDAAmB,CAAnB,kCAAmB,CAAnB,oDAAmB,CAAnB,iEAAmB,CAAnB,2BAAmB,CAAnB,gDAAmB,CAAnB,8BAAmB,CAAnB,sDAAmB,CAAnB,6BAAmB,CAAnB,sDAAmB,CAAnB,8BAAmB,CAAnB,qDAAmB,CAAnB,8BAAmB,CAAnB,oDAAmB,CAAnB,8BAAmB,CAAnB,sDAAmB,CAAnB,8BAAmB,CAAnB,sDAAmB,CAAnB,8BAAmB,CAAnB,sDAAmB,CAAnB,6BAAmB,CAAnB,sDAAmB,CAAnB,8BAAmB,CAAnB,sDAAmB,CAAnB,8BAAmB,CAAnB,mDAAmB,CAAnB,8BAAmB,CAAnB,mDAAmB,CAAnB,+BAAmB,CAAnB,sDAAmB,CAAnB,8BAAmB,CAAnB,sDAAmB,CAAnB,2CAAmB,CAAnB,+BAAmB,CAAnB,oDAAmB,CAAnB,+BAAmB,CAAnB,oDAAmB,CAAnB,6BAAmB,CAAnB,mDAAmB,CAAnB,gCAAmB,CAAnB,qDAAmB,CAAnB,6BAAmB,CAAnB,sDAAmB,CAAnB,4BAAmB,CAAnB,sDAAmB,CAAnB,6BAAmB,CAAnB,oDAAmB,CAAnB,+BAAmB,CAAnB,oDAAmB,CAAnB,wCAAmB,CAAnB,2BAAmB,CAAnB,sDAAmB,CAAnB,gCAAmB,CAAnB,sDAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,sFAAmB,CAAnB,+EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,8DAAmB,CAAnB,0BAAmB,CAAnB,8BAAmB,CAAnB,cAAmB,CAAnB,mBAAmB,CAAnB,oBAAmB,CAAnB,oBAAmB,CAAnB,kBAAmB,CAAnB,uBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,oBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,wBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,0BAAmB,CAAnB,oBAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,6BAAmB,CAAnB,qBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,0BAAmB,CAAnB,qBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,8BAAmB,CAAnB,kBAAmB,CAAnB,4BAAmB,CAAnB,iBAAmB,CAAnB,4BAAmB,CAAnB,iBAAmB,CAAnB,mDAAmB,CAAnB,8CAAmB,CAAnB,mDAAmB,CAAnB,4CAAmB,CAAnB,mDAAmB,CAAnB,8CAAmB,CAAnB,0CAAmB,CAAnB,gDAAmB,CAAnB,0CAAmB,CAAnB,iDAAmB,CAAnB,iDAAmB,CAAnB,8CAAmB,CAAnB,2BAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,yBAAmB,CAAnB,wBAAmB,CAAnB,wBAAmB,CAAnB,uBAAmB,CAAnB,0BAAmB,CAAnB,8BAAmB,CAAnB,yBAAmB,CAAnB,8BAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,wBAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,8BAAmB,CAAnB,4BAAmB,CAAnB,mCAAmB,CAAnB,8GAAmB,CAAnB,yCAAmB,CAAnB,sCAAmB,CAAnB,6BAAmB,CAAnB,qCAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,4BAAmB,CAAnB,mBAAmB,CAAnB,2BAAmB,CAAnB,kBAAmB,CAAnB,6BAAmB,CAAnB,6BAAmB,CAAnB,6BAAmB,CAAnB,6BAAmB,CAAnB,6BAAmB,CAAnB,6BAAmB,CAAnB,6BAAmB,CAAnB,6BAAmB,CAAnB,yBAAmB,CAAnB,kBAAmB,CAAnB,2BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,wCAAmB,CAAnB,0BAAmB,CAAnB,4BAAmB,CAAnB,4BAAmB,CAAnB,8BAAmB,CAAnB,mCAAmB,CAAnB,mCAAmB,CAAnB,qCAAmB,CAAnB,yBAAmB,CAAnB,uCAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,oCAAmB,CAAnB,kDAAmB,CAAnB,oDAAmB,CAAnB,+CAAmB,CAAnB,oDAAmB,CAAnB,2CAAmB,CAAnB,iDAAmB,CAAnB,8CAAmB,CAAnB,uDAAmB,CAAnB,8CAAmB,CAAnB,oDAAmB,CAAnB,8CAAmB,CAAnB,oDAAmB,CAAnB,2CAAmB,CAAnB,uDAAmB,CAAnB,sCAAmB,CAAnB,0CAAmB,CAAnB,sCAAmB,CAAnB,0CAAmB,CAAnB,sCAAmB,CAAnB,0CAAmB,CAAnB,sCAAmB,CAAnB,0CAAmB,CAAnB,sCAAmB,CAAnB,0CAAmB,CAAnB,mCAAmB,CAAnB,0CAAmB,CAAnB,sCAAmB,CAAnB,0CAAmB,CAAnB,sCAAmB,CAAnB,0CAAmB,CAAnB,sCAAmB,CAAnB,2CAAmB,CAAnB,sCAAmB,CAAnB,0CAAmB,CAAnB,sCAAmB,CAAnB,6CAAmB,CAAnB,sCAAmB,CAAnB,6CAAmB,CAAnB,sCAAmB,CAAnB,6CAAmB,CAAnB,sCAAmB,CAAnB,6CAAmB,CAAnB,+BAAmB,CAAnB,uCAAmB,CAAnB,kCAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,2CAAmB,CAAnB,kCAAmB,CAAnB,2CAAmB,CAAnB,kCAAmB,CAAnB,2CAAmB,CAAnB,kCAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,0CAAmB,CAAnB,kCAAmB,CAAnB,0CAAmB,CAAnB,kCAAmB,CAAnB,0CAAmB,CAAnB,kCAAmB,CAAnB,0CAAmB,CAAnB,mCAAmB,CAAnB,2CAAmB,CAAnB,mCAAmB,CAAnB,2CAAmB,CAAnB,mCAAmB,CAAnB,2CAAmB,CAAnB,oCAAmB,CAAnB,2CAAmB,CAAnB,iCAAmB,CAAnB,0CAAmB,CAAnB,iCAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,2CAAmB,CAAnB,iCAAmB,CAAnB,2CAAmB,CAAnB,iCAAmB,CAAnB,2CAAmB,CAAnB,iCAAmB,CAAnB,2CAAmB,CAAnB,+BAAmB,CAAnB,6CAAmB,CAAnB,yCAAmB,CAAnB,yDAAmB,CAAnB,oBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,kEAAmB,CAAnB,4FAAmB,CAAnB,2GAAmB,CAAnB,kGAAmB,CAAnB,kFAAmB,CAAnB,mDAAmB,CAAnB,0EAAmB,CAAnB,iGAAmB,CAAnB,qEAAmB,CAAnB,kGAAmB,CAAnB,wEAAmB,CAAnB,+FAAmB,CAAnB,kCAAmB,CAAnB,6BAAmB,CAAnB,uEAAmB,CAAnB,kGAAmB,CAAnB,4CAAmB,CAAnB,sDAAmB,CAAnB,2EAAmB,CAAnB,kGAAmB,CAAnB,+CAAmB,CAAnB,kGAAmB,CAAnB,qCAAmB,CAAnB,kBAAmB,CAAnB,2CAAmB,CAAnB,sMAAmB,CAAnB,gCAAmB,CAAnB,oMAAmB,CAAnB,wLAAmB,CAAnB,0LAAmB,CAAnB,6IAAmB,CAAnB,qKAAmB,CAAnB,kDAAmB,CAAnB,gEAAmB,CAAnB,kDAAmB,CAAnB,qIAAmB,CAAnB,kDAAmB,CAAnB,wEAAmB,CAAnB,kDAAmB,CAAnB,mDAAmB,CAAnB,kDAAmB,CAAnB,4DAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,+DAAmB,CAAnB,2DAAmB,CAFnB,gDAGA,CAHA,iBAGA,CAHA,oDAGA,CAHA,QAGA,CAHA,qDAGA,CAHA,sBAGA,CAHA,oDAGA,CAHA,QAGA,CAHA,mDAGA,CAHA,OAGA,CAHA,2CAGA,CAHA,cAGA,CAHA,gEAGA,CAHA,wBAGA,CAHA,2CAGA,CAHA,aAGA,CAHA,iDAGA,CAHA,SAGA,CAHA,yEAGA,CAHA,+DAGA,CAHA,mDAGA,CAHA,gFAGA,CAHA,+CAGA,CAHA,gFAGA,CAHA,wCAGA,CAHA,gFAGA,CAHA,sDAGA,CAHA,gDAGA,CAHA,kDAGA,CAHA,8CAGA,CAHA,yBAGA,CAHA,sCAGA,CAHA,mDAGA,CAHA,sDAGA,CAHA,+CAGA,CAHA,mDAGA,CAHA,+CAGA,CAHA,mDAGA,CAHA,+CAGA,CAHA,sDAGA,CAHA,+CAGA,CAHA,sDAGA,CAHA,0CAGA,CAHA,sDAGA,CAHA,uDAGA,CAHA,2CAGA,CAHA,oDAGA,CAHA,2CAGA,CAHA,sDAGA,CAHA,2CAGA,CAHA,sDAGA,CAHA,2CAGA,CAHA,sDAGA,CAHA,2CAGA,CAHA,sDAGA,CAHA,0CAGA,CAHA,sDAGA,CAHA,2CAGA,CAHA,mDAGA,CAHA,4CAGA,CAHA,oDAGA,CAHA,4CAGA,CAHA,oDAGA,CAHA,0CAGA,CAHA,mDAGA,CAHA,uDAGA,CAHA,6CAGA,CAHA,qDAGA,CAHA,0CAGA,CAHA,sDAGA,CAHA,+CAGA,CAHA,mDAGA,CAHA,0CAGA,CAHA,+CAGA,CAHA,2CAGA,CAHA,+CAGA,CAHA,2CAGA,CAHA,+CAGA,CAHA,0CAGA,CAHA,+CAGA,CAHA,0CAGA,CAHA,8CAGA,CAHA,2CAGA,CAHA,8CAGA,CAHA,2CAGA,CAHA,4CAGA,CAHA,6CAGA,CAHA,sDAGA,CAHA,oDAGA,CAHA,mCAGA,CAHA,mCAGA,CAHA,mDAGA,CAHA,qDAGA,CAHA,kDAGA,CAHA,mDAGA,CAHA,qDAGA,CAHA,qDAGA,CAHA,mDAGA,CAHA,0CAGA,CAHA,mDAGA,CAHA,kDAGA,CAHA,kBAGA,CAHA,+HAGA,CAHA,wGAGA,CAHA,iHAGA,CAHA,wFAGA,CAHA,+HAGA,CAHA,wGAGA,CAHA,+CAGA,CAHA,sDAGA,CAHA,iDAGA,CAHA,sDAGA,CAHA,8CAGA,CAHA,oDAGA,CAHA,iDAGA,CAHA,uDAGA,CAHA,sDAGA,CAHA,yDAGA,CAHA,yCAGA,CAHA,8CAGA,CAHA,4CAGA,CAHA,gDAGA,CAHA,8DAGA,CAHA,mDAGA,CAHA,gFAGA,CAHA,yBAGA,CAHA,6LAGA,CAHA,4EAGA,CAHA,gFAGA,CAHA,6DAGA,CAHA,kBAGA,CAHA,uFAGA,CAHA,uBAGA,CAHA,8BAGA,CAHA,qBAGA,CAHA,6BAGA,CAHA,8BAGA,CAHA,mBAGA,EAHA,uFAGA,CAHA,8DAGA,CAHA,8DAGA,EAHA,wFAGA,CAHA,8DAGA,CAHA,8BAGA,CAHA,qBAGA,EAHA,2FAGA,EAHA,oHAGA,CAHA,yBAGA,CAHA,6LAGA,CAHA,yEAGA,CAHA,kBAGA,CAHA,uGAGA,CAHA,8FAGA,CAHA,sDAGA,CAHA,kQAGA,CAHA,qDAGA,CAHA,gFAGA,CAHA,6CAGA,CAHA,gEAGA,CAHA,kDAGA,CAHA,gEAGA,CCEA,eAEE,gCACF,CAEA,oBAEE,UAAW,CADX,SAEF,CAGA,0BACE,kBACF,CAGA,0BACE,eACF,CAGA,gCACE,eACF,CACA,WACE,0BACF,CACA,qBACE,oBACF,CACA,WACE,aACF,CACA,SAGE,qBAAsB,CAFtB,WAAY,CAGZ,QAAS,CAFT,SAGF,CACA,eAKE,kBAAmB,CAJnB,wBAAyB,CAEzB,iBAAkB,CAGlB,oBAAsB,CAFtB,mBAAoB,CAFpB,gBAKF,CACA,YACE,YAAa,CACb,qBAAsB,CACtB,OAAQ,CACR,WAAY,CACZ,aACF,CACA,4DAKE,UAAc,CACd,gCAAoC,CAHpC,cAAe,CACf,eAAgB,CAFhB,QAKF,CACA,kBACE,kCACF,CACA,wEAEE,aACF,CACA,gDAEE,oBACF,CACA,eACE,aAAsB,CACtB,YAAa,CAEb,gCAAoC,CAEpC,cAAe,CADf,eAAgB,CAFhB,OAIF,CACA,gBAEE,oBAAqB,CADrB,aAEF,CACA,oBAEE,YAAa,CADb,qBAEF,CACA,WACE,YAAa,CACb,OACF,CACA,gBAOE,kBAAmB,CANnB,wBAAoC,CACpC,4BAA2C,CAC3C,YAAa,CACb,cAAe,CAEf,WAAY,CADZ,6BAA8B,CAG9B,iBACF,CACA,2BACE,cACF,CACA,WAEE,kBAAmB,CAKnB,2BAA+B,CAH/B,WAAY,CADZ,eAAgB,CAEhB,iBAAkB,CAClB,eAAgB,CALhB,YAOF,CACA,kCACE,YAAa,CACb,qBAAsB,CACtB,yBAA0B,CAC1B,SACF,CACA,wBACE,kCAAwC,CACxC,SACF,CACA,cAQE,kBAAmB,CALnB,iBAAkB,CAUlB,aAAc,CAFd,cAAe,CAVf,YAAa,CASb,cAAe,CAEf,eAAgB,CAHhB,sBAAuB,CALvB,eAAgB,CAChB,cAAe,CAEf,iBAOF,CACA,oBAEE,qBACF,CAEA,qBACE,kBAAmB,CAEnB,yBAA0B,CAD1B,UAEF,CACA,WACE,kCAAwC,CAExC,qBAAuB,CADvB,SAEF,CAEA,cAKE,2BAA4B,CAC5B,YAAa,CAEb,qBAAsB,CAJtB,WAAY,CAFZ,eAAgB,CAChB,eAAgB,CAIhB,iCAAmC,CANnC,UAQF,CACA,oBAQE,kBAAmB,CANnB,gBAAuB,CAEvB,WAAgC,CAAhC,4BAAgC,CAOhC,aAAc,CALd,mBAAoB,CALpB,QAAO,CASP,cAAe,CAEf,eAAgB,CAHhB,OAAQ,CAFR,sBAAuB,CAFvB,YAQF,CACA,gCACE,2BACF,CACA,aAEE,0BAA2B,CAD3B,iBAEF,CACA,YACE,YAAa,CAEb,qBAAsB,CADtB,QAEF,CACA,4DAQE,eAAiB,CANjB,qBAAyB,CAEzB,iBAAkB,CAClB,gCAAoC,CAEpC,cAAe,CAJf,WAAY,CAGZ,cAGF,CACA,wBACE,SACF,CACA,kBAEE,UAAc,CADd,YAAa,CAEb,OAGF,CACA,qCAFE,gCAAoC,CADpC,cAeF,CAZA,mBAIE,wBAA6B,CAF7B,wBAAyB,CACzB,iBAAkB,CAElB,aAAc,CAKd,mBAAoB,CADpB,OAAQ,CARR,WAAY,CAUZ,gBAAiB,CAHjB,cAIF,CACA,yBACE,qBACF,CACA,iBAUE,kBAAmB,CANnB,wBAAyB,CAFzB,wBAAyB,CACzB,iBAAkB,CAElB,UAAW,CAOX,mBAAoB,CALpB,gCAAoC,CADpC,cAAe,CAGf,OAAQ,CARR,WAAY,CAUZ,sBAAuB,CAEvB,gBAAiB,CALjB,cAMF,CACA,aACE,kBAAmB,CACnB,eACF,CACA,WACE,aAAc,CACd,cAAe,CACf,iBACF,CACA,kBAEE,aAAc,CAGd,gCAAoC,CAFpC,cAAe,CACf,eAAgB,CAHhB,QAKF,CACA,sCACE,WACF,CACA,yBAEE,+BAAgC,CAChC,iBAAkB,CAFlB,kBAGF,CACA,+BAEE,UAAc,CACd,gCAAoC,CAFpC,cAGF,CACA,mBAIE,wBAA6B,CAH7B,WAAY,CACZ,WAAY,CACZ,SAEF,CAEA,YACE,OACF,CACA,aACE,eACF,CACA,gBAEE,aAAc,CAGd,gCAAoC,CAFpC,cAAe,CACf,eAAgB,CAHhB,cAKF,CACA,gBAIE,0BAA2B,CAD3B,oBAAqB,CADrB,QAAS,CAGT,aAAc,CAJd,SAKF,CACA,kBACE,aAAc,CAKd,oBAAqB,CAFrB,gCAAoC,CAFpC,cAAe,CACf,eAAgB,CAIhB,iBAAkB,CAFlB,UAGF,CACA,6BAQE,2BAA4B,CAC5B,YAAa,CACb,oBAAqB,CATrB,UAAc,CAMd,mBAAoB,CAHpB,gCAAoC,CAFpC,cAAe,CACf,eAAgB,CAGhB,gBAAiB,CADjB,eAAgB,CAMhB,eACF,CACA,gBAEE,+BAAgC,CADhC,WAEF,CACA,YAIE,kBAAmB,CAHnB,QAAO,CACP,WAAY,CAIZ,aAAc,CAHd,sBAAuB,CAEvB,wBAEF,CACA,YAEE,iBAAkB,CADlB,iBAEF,CACA,kBAGE,eAAgB,CAChB,WAAY,CAEZ,mBAAqB,CADrB,kBAEF,CAEA,+BARE,qBAAyB,CACzB,UAuBF,CAhBA,aAEE,kBAAmB,CAEnB,kBAAmB,CAUnB,aAAc,CAbd,YAAa,CAWb,gCAAoC,CAGpC,cAAe,CAFf,eAAgB,CAVhB,QAAS,CAKT,WAAY,CAEZ,MAAO,CAJP,eAAgB,CAChB,cAAe,CAEf,iBAOF,CACA,eAEE,eAAgB,CAChB,qBAAyB,CAGzB,QAAO,CAFP,YAAe,CAHf,SAOF,CACA,kBAEE,UAAW,CACX,gCAAoC,CAFpC,cAAe,CAGf,eACF,CACA,gBAEE,+BAAgC,CAMhC,kBAAmB,CALnB,iBAMF,CACA,gCATE,8BAA+B,CAI/B,UAAc,CACd,gCAAoC,CAFpC,cAAe,CAGf,eAgBF,CAbA,gBAOE,kBAAmB,CANnB,YAAa,CAGb,wBAAyB,CAQzB,kBAAmB,CANnB,iBAAkB,CADlB,SAAU,CAHV,UAWF,CAWA,UAEE,WAAY,CADZ,iBAEF,CACA,gBAgBE,+BAAgC,CAfhC,8BAA+B,CAa/B,QAAS,CAPT,UAAc,CAGd,QAAO,CAFP,gCAAoC,CAFpC,cAAe,CAGf,eAAgB,CAPhB,WAAY,CAUZ,MAAO,CACP,OAAQ,CAER,KAEF,CACA,4BAdE,kBAAmB,CADnB,YAAa,CAEb,sBAAuB,CAMvB,iBAqBF,CAdA,YAQE,eAAgB,CADhB,qBAAyB,CAJzB,kBAAmB,CADnB,WAAY,CAOZ,SAAU,CAEV,WAAY,CACZ,OAAQ,CAXR,UAAW,CAYX,UACF,CACA,gBACE,gBAAiB,CACjB,iCACF,CACA,8BAGE,WAAY,CAFZ,WAAY,CACZ,WAEF,CACA,wFAEE,sBACF,CACA,2CACE,SACF,CACA,uDACE,sBAAwB,CAExB,WAAY,CADZ,QAEF,CACA,kEACE,uBAAyB,CACzB,QAAS,CACT,yBACF,CACA,wJAEE,qBACF,CACA,cACE,wBAAyB,CACzB,yBAA6B,CAI7B,QAAS,CAGT,aAAc,CAEd,gCAAoC,CAHpC,cAAe,CADf,MAAO,CAJP,gBAAiB,CACjB,iBAAkB,CAMlB,OAAQ,CALR,SAQF,CACA,gCACE,0BACF,CACA,UAEE,iBACF,CAGA,gBAEE,aAAc,CAEd,gCAAoC,CADpC,cAAe,CAEf,eAAgB,CAEhB,gBAAiB,CADjB,kBAEF,CACA,cAGE,0BAA2B,CAD3B,gBAEF,CAEA,cAIE,kBAAmB,CAEnB,UAAc,CAJd,YAAa,CAMb,gCAAoC,CADpC,cAAe,CANf,eAAgB,CAIhB,OAAQ,CAFR,gBAMF,CACA,yCAEE,kBACF,CACA,eAGE,oBAAqB,CAFrB,QAAS,CAIT,eAAgB,CAHhB,iBAAkB,CAElB,gBAEF,CACA,kBACE,gBACF,CACA,kBAGE,UAAc,CACd,gCAAoC,CAFpC,cAAe,CAGf,kBAAmB,CAEnB,iBAAkB,CADlB,iBAEF,CACA,qBAEE,iBACF,CACA,yBAEE,UAAW,CAKX,QAAS,CAJT,UAKF,CACA,iDALE,qBAAsB,CACtB,UAAW,CAJX,MAAO,CAKP,iBAWF,CARA,wBAEE,WAAY,CAKZ,SAAU,CAJV,SAKF,CACA,uBAIE,2BAA4B,CAC5B,oBAAqB,CAFrB,mBAAoB,CAGpB,eAAgB,CALhB,UAAW,CACX,qBAKF,CACA,kBACE,iBAAkB,CAClB,QACF,CACA,UAIE,kBAAmB,CAHnB,oBAAqB,CAErB,UAAW,CAEX,aAAc,CAHd,UAIF,CAEA,gBAME,gBAAiB,CAHjB,oBAAqB,CADrB,QAAS,CAET,8BAA+B,CAC/B,aAAc,CAJd,SAMF,CACA,kBACE,aAAc,CAKd,oBAAqB,CAFrB,gCAAoC,CAFpC,cAAe,CACf,eAAgB,CAIhB,iBAAkB,CAFlB,UAGF,CACA,6BAQE,2BAA4B,CAC5B,YAAa,CACb,oBAAqB,CATrB,UAAc,CAMd,mBAAoB,CAHpB,gCAAoC,CAFpC,cAAe,CACf,eAAgB,CAGhB,gBAAiB,CADjB,QAAS,CAMT,eAAgB,CAChB,eACF,CACA,gBACE,WACF,CACA,sBAGE,WAAY,CADZ,iBAAkB,CAElB,QACF,CACA,sBAEE,0BACF,CACA,YAEE,iBACF,CACA,YAEE,oBAAqB,CADrB,eAAgB,CAEhB,UACF,CACA,mBAKE,+BAA2C,CAJ3C,yBAA0B,CAE1B,aAAc,CAKd,qBAAsB,CADtB,cAAe,CADf,eAAiB,CAJjB,YAAa,CAEb,UAKF,CACA,wBAEE,UACF,CACA,0BAEE,cACF,CACA,sBAKE,YAAa,CACb,qBAAsB,CACtB,QAAS,CALT,oBAAqB,CACrB,QAAS,CACT,SAIF,CACA,yBAEE,cACF,CACA,yCAGE,UAAc,CACd,gCAAoC,CAFpC,cAAe,CAGf,QACF,CACA,6BAEE,iBAAkB,CAClB,QACF,CACA,cAKE,gCAAoC,CADpC,wBAA0B,CAD1B,yBAA2B,CAG3B,gBAAiB,CAJjB,4BAKF,CACA,+CAEE,aACF,CACA,iBAEE,kCAAoC,CACpC,8BACF,CACA,uBAEE,8BAAgC,CAChC,uBACF,CACA,KAEE,YAAa,CACb,qBAAsB,CACtB,YACF,CACA,WACE,YAAa,CACb,UACF,CAEA,YAEE,2BAA4B,CAD5B,UAEF,CACA,YACE,SACF,CACA,eAEE,wBACF,CACA,gBAKE,qBAAsB,CADtB,oBAAqB,CAFrB,gBAAiB,CADjB,gBAAiB,CAEjB,UAGF,CACA,+BAGE,0BAA2B,CAD3B,uBAEF,CACA,gCACE,kBAAmB,CAEnB,mBAAoB,CADpB,sBAAuB,CAEvB,iBACF,CAEA,aACE,YAAa,CACb,WAAY,CAEZ,eAAgB,CADhB,UAEF,CAEA,2BACE,UACF,CAEA,uCACE,kBAAmB,CAEnB,aAAc,CACd,gCAAoC,CAFpC,eAGF,CAEA,uCACE,gCACF,CAEA,6CACE,kBACF,CAEA,gBAEE,aAAc,CADd,SAEF,CAGA,eACE,YACF,CAEA,eAIE,UAAW,CACX,gCAAoC,CAJpC,cAAe,CACf,eAAgB,CAChB,kBAGF,CAEA,uBACE,kBACF,CAEA,yBAKE,+BAAgC,CAHhC,UAAW,CAEX,gCAAoC,CADpC,cAAe,CAFf,WAKF,CAIA,gBAEE,eAAgB,CADhB,eAEF,CAEA,yCACE,cACF,CAEA,6BAEE,eAAgB,CAChB,kBACF,CACA,sCAEE,kCAAoC,CACpC,yBAA2B,CAE3B,uBAAyB,CAEzB,gCAAoC,CAHpC,wBAA0B,CAE1B,yBAA2B,CAE3B,2BACF,CACA,yCAOE,0BAAkC,CADlC,2BAA6B,CAH7B,UAAc,CAEd,gCAAoC,CADpC,cAAe,CAFf,2BAMF,CACA,kEAEE,qBAAuB,CACvB,WACF,CACA,uEAEE,YACF,CAKA,wEAEE,kCACF,CACA,oDAEE,qBACF,CACA,oBAEE,YACF,CACA,+BAEE,kBACF,CAGA,sCAGE,4BAA6B,CAF7B,kBAAmB,CAGnB,mBAAoB,CAFpB,UAGF,CACA,iDAEE,WACF,CACA,qFAEE,wBAAyB,CACzB,oBACF,CACA,8OACE,oBACF,CACA,mFAEE,OACF,CACA,iLACE,wBAAyB,CACzB,kBACF,CACA,WACE,WAAY,CAGZ,aAAc,CADd,iBAEF,CACA,YACE,mBACF,CACA,mBAEE,uBAAyB,CAOzB,wBAAyB,CAHzB,iBAAkB,CADlB,kBAAmB,CAFnB,qBAAsB,CAFtB,aAAc,CAGd,aAGF,CAIA,oBAGE,oBAAqB,CAFrB,0BAA2B,CAC3B,qBAEF,CACA,iBAGE,oBAAqB,CAFrB,iBAAkB,CAClB,sBAEF,CACA,kCAGE,4BAA6B,CAF7B,cAAgB,CAChB,kBAAmB,CAEnB,kBACF,CACA,uBAEE,kBAAmB,CADnB,qBAEF,CACA,aAEE,0BACF,CACA,UAEE,0BAA2B,CAC3B,aACF,CACA,aAEE,4CACF,CACA,iBAEE,gBAAiB,CACjB,uBACF,CACA,mBAEE,WAAY,CACZ,aAAc,CACd,wBACF,CACA,oCACE,4BAA8B,CAC9B,8BAAgC,CAChC,2BACF,CACA,iDAEE,UACF,CACA,yBACE,gBAAuB,CACvB,WAAY,CAEZ,QAAS,CADT,SAEF,CACA,yEAIE,kBAAmB,CAFnB,wBAAyB,CACzB,iBAAkB,CAFlB,WAIF,CAKA,gCACE,4BAA8B,CAE9B,wBAA6B,CAC7B,yBAA2B,CAD3B,wBAA6B,CAG7B,oBAAsB,CADtB,yBAEF,CACA,sCACE,kBACF,CACA,0BAEE,8BAA+B,CAD/B,0BAEF,CACA,kCACE,4BAA8B,CAC9B,kCAAoC,CAGpC,gCAAoC,CADpC,cAAe,CADf,yBAA2B,CAG3B,oBACF,CAGA,yBAEE,eAAgB,CADhB,UAEF,CACA,yBACE,kBAAmB,CACnB,oBAAqB,CACrB,eACF,CACA,6CAIE,oBAA+B,CAF/B,oBAAqB,CAIrB,eAAgB,CAHhB,eAA0B,CAF1B,kBAAmB,CAInB,UAEF,CACA,qCAEE,2BAEE,OACF,CACA,gBAEE,gBACF,CACF,CACA,qCAEE,2BAEE,OACF,CAEA,YAEE,aACF,CACA,eAEE,gBACF,CACA,eAGE,cAAe,CADf,iBAEF,CACA,gBACE,aACF,CACA,WACI,aACJ,CACF,CAEA,gBACE,2BACF,CAEA,+BAIE,iBAAkB,CAHlB,WAAY,CACZ,gBAAiB,CACjB,YAEF,CAEA,qCACE,wBACF,CAEA,wCAGE,eACF,CAEA,mBAEE,qBAAsB,CADtB,WAEF,CAEA,6BACE,qBAAsB,CACtB,2BACF,CAEA,6BACE,qBAAsB,CACtB,YACF,CAEA,+BAGE,2BAA6B,CAF7B,sBAAwB,CACxB,iCAEF,CAEA,wCACE,kCAAoC,CACpC,uBAAyB,CACzB,yBACF,CAEA,qCACE,uBACF,CAEA,aACE,wBACF,CAEA,eACE,gBACF,CAEA,cACE,uBACF,CAEA,+BACE,wBAAyB,CAIzB,iBAAkB,CAHlB,UAAY,CACZ,cAAe,CACf,gBAAiB,CAEjB,uBACF,CAEA,qCACE,UACF,CAEA,aACE,kCAAoC,CACpC,wBAAyB,CAGzB,iBAAkB,CAFlB,UAAc,CACd,gBAAiB,CAEjB,uBACF,CAEA,mBACE,kCACF,CAEA,2BAEE,kBAAmB,CADnB,YAAa,CAEb,OACF,CAEA,yBACE,eACF,CAKA,qBAGE,+BAAgC,CAFhC,QAAS,CACT,aAEF,CAEA,oBACE,YACF,CACA,0BACE,YACF,CACA,kGAEE,wBAA6B,CAD7B,oBAEF,CACA,qFAEE,wBAAyB,CAEzB,WAAY,CACZ,uBAAwB,CACxB,wBAAyB,CAHzB,UAIF,CACA,4KACE,oBACF,CAKA,wDACE,oBACF,CAEA,cAII,cACJ,CAEA,MAEE,6BAAoC,CACpC,uBAAwB,CAFxB,oBAGF,CAEA,gBACE,uBACF,CAEA,cAEE,yBACF,CAEA,yBAJE,iCAMF,CAMA,4BAEE,kBACF,CAEA,4BACE,oBAAqB,CACrB,sBACF,CAEA,SASI,kBAAmB,CAHnB,0BAAoC,CADpC,QAAS,CAET,YAAa,CACb,sBAAuB,CALvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,SACJ,CAEA,iBACI,eAAiB,CAEjB,iBAAkB,CAElB,6BAAuC,CAHvC,YAAa,CAEb,iBAEJ,CAEA,YACE,mCAAoC,CACpC,WACF,CAMA,mCACE,kCACF,CACA,sBACE,kBAAmB,CAGnB,gBAAiB,CAFjB,eAAgB,CAChB,gBAEF,CAEA,sBAOE,eAAiB,CAFjB,WAAY,CADZ,UAIF,CAEA,kBACE,YACF,CA+EA,iCACE,YACF,CAEA,cACE,uBAAwB,CACxB,oBACF,CAGA,eAEE,kCAAoC,CADpC,gCAAiC,CAEjC,UACF,CAEA,cAEE,sBAAuB,CADvB,UAEF,CAGA,cAEE,qBAAyB,CAEzB,wBAAyB,CACzB,iBAAkB,CAClB,0BAAwC,CAHxC,UAAY,CAFZ,cAAe,CAMf,YACF,CAEA,mBAGE,UAAc,CADd,cAAe,CADf,yBAA0B,CAI1B,wBAAiB,CAAjB,gBAAiB,CADjB,kBAEF,CAEA,yBACE,wBAEF,CACA,2BAEE,UACF,CCp5CA,YACE,aACF,CACA,uBAGE,WAAY,CAEZ,MAAO,CAJP,iBAAkB,CAGlB,KAAM,CAFN,UAIF,CACA,kBAGE,WAAY,CAFZ,SAGF,CACA,4BACI,cACF,CACF,2BAEI,eACF,CACF,sBAGE,mBAAoB,CAFpB,oBAAqB,CACrB,SAEF,CACA,sBACE,SACF,CACA,uBACE,SACF,CACA,sFAEE,YACF,CACA,+BAEE,gBAAiB,CADjB,mBAEF,CACA,oDAEE,cAAe,CACf,cAAe,CACf,SACF,CACA,kBAEE,cAAe,CADf,4BAEF,CACA,gCACI,kBAAmB,CAEX,sCACV,CACF,6DACI,qBAAsB,CAEd,cACV,CACF,2BACI,mBACF,CACF,mFAGI,YACF,CACF,wJAGI,WACF,CACF,8BACI,kBACF,CACF,yBACI,SACF,CACF,yCACI,mBAAoB,CACpB,wBAAyB,CAEjB,gBACV,CACF,wBACE,mBACF,CACA,kCACI,kBAAmB,CAEX,sCACV,CACF,4BACE,YACF,CACA,mBACE,mBAAoB,CACpB,oBACF,CACA,kBAOE,qBAAsB,CAEtB,WAAY,CAJZ,kBAAmB,CAJnB,iBAAkB,CAKlB,oBAAqB,CAJrB,wBAAyB,CAEjB,gBAMV,CACA,2BAEI,eACF,CACF,4BAGE,mBAAoB,CADpB,yBAA0B,CAD1B,SAGF,CACA,iCAII,WAAY,CAFZ,kBAAmB,CADnB,iBAIF,CACF,oBAOE,kBAAmB,CACnB,qBAAuB,CACvB,kBAAmB,CAHnB,UAAW,CAFX,cAAe,CADf,aAAc,CADd,mBAAoB,CADpB,iBAAkB,CAIlB,SAKF,CACA,wCAEI,gBAAiB,CADjB,kBAEF,CACF,2BAGI,WAAY,CADZ,QAAS,CADT,QAAS,CAGT,yBACF,CACF,wBACI,QAAS,CACT,QAAS,CACT,yBACF,CAMF,mDAEI,OAAQ,CACR,0BACF,CACF,yBACE,WAAY,CACZ,kBACF,CACA,mBAGE,WAAY,CAFZ,iBAAkB,CAClB,SAEF,CACA,uBACI,KACF,CACF,0BACI,QACF,CACF,wBACI,MACF,CACF,yBACI,OACF,CACF,0BACI,QAAS,CACT,0BACF,CACF,yBAEE,oBAAoC,CADpC,cAAe,CAGf,QAAS,CADT,eAEF,CACA,2BAEI,UAAW,CADX,oBAEF,CAMF,oBACE,GACE,oBACF,CACF,CACA,gCAGE,WAAY,CACZ,mBAAoB,CAHpB,iBAAkB,CAIlB,wBAAyB,CAEjB,gBAAiB,CALzB,UAMF,CACA,kDACM,WACF,CACJ,uBACI,cACF,CACF,8EAEI,YACF,CACF,mGAaE,qBAAuB,CADvB,wBAAqB,CAPrB,iBAAkB,CAGlB,UAAW,CADX,cAAe,CAHf,YAAa,CAKb,iBAAkB,CAHlB,WAQF,CACA,uKACM,kCACF,CACJ,iiBAYM,6BACF,CACJ,wBACE,0BACF,CACA,wDAEE,oBAAkC,CAClC,2BACF,CACA,wJAII,YACF,CACF,sBACE,gCACF,CACA,6BAOI,kBAAmB,CALnB,kBAAmB,CACnB,WAA6B,CAA7B,4BAA6B,CAC7B,kBAAuB,CAMvB,cAAe,CALf,YAAa,CAIb,WAAY,CAHZ,sBAAuB,CAQvB,WAAY,CAHZ,wBAAyB,CAEjB,gBAAiB,CALzB,UAOF,CACF,mCACM,kBACF,CACJ,iCAGM,eAAgB,CADhB,cAAe,CADf,UAGF,CACJ,sCACM,mBACF,CACJ,0CACQ,eACF,CACN,qBACE,qBACF,CACA,yBACE,aACF,CACA,4BACE,iBACF,CACA,mEAEE,gBACF,CACA,mEAEE,gBACF,CACA,8EAEE,kBACF,CACA,8EAEE,kBACF,CAEA,mCAKE,wBAAyB,CAFzB,qBAAsB,CACtB,iBAAkB,CAFlB,UAAW,CAIX,8BAAgC,CALhC,SAMF,CACA,wCACE,MAAO,CACP,OACF,CACA,yCACE,SAAU,CACV,OACF,CACA,uCACE,QAAS,CACT,KACF,CACA,0CACE,QAAS,CACT,QACF,CAIA,2FACE,MACF,CAIA,6FACE,SACF,CAEA,iCAGE,sBACF,CACA,6EAKE,WAAY,CADZ,KAAM,CADN,yBAA6B,CAD7B,SAIF,CACA,sCAEE,qBAAsB,CADtB,MAEF,CACA,uCAEE,sBAAuB,CADvB,SAEF,CACA,6EAEE,UAAW,CAEX,MAAO,CADP,0BAA6B,CAE7B,UACF,CACA,qCAEE,oBAAqB,CADrB,KAEF,CACA,wCACE,uBAAwB,CACxB,QACF,CCrZA,YACE,wBAAyB,CAEzB,WAAY,CADZ,UAEF,CAEA,aAEE,iBAAkB,CAClB,eAAgB,CAFhB,iBAAkB,CAGlB,iBACF,CACA,WACE,wBAAyB,CACzB,wBAAyB,CACzB,gCACF,CAEA,gBACE,wBACF,CAEA,cAEE,kBAAmB,CADnB,YAAa,CAEb,OACF,CAEA,WAEE,WAAY,CADZ,UAEF,CAEA,YAEE,UAAW,CAIX,QAAO,CALP,cAAe,CAEf,eAAgB,CAChB,gBAAiB,CACjB,gBAEF,CAEA,kBASE,kBAAmB,CAJnB,UAAc,CAEd,YAAa,CAHb,cAAe,CAIf,OAAQ,CALR,MAAO,CAGP,SAAU,CALV,iBAAkB,CAClB,SAQF,CAEA,uBAKE,kBAAmB,CAFnB,iBAAkB,CAClB,oBAAqB,CAFrB,WAAY,CADZ,UAKF,CACA,oBAGE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAHlB,WAAY,CADZ,UAKF,CAEA,oDAEE,cACF,CACA,yBACE,SACF,CAEA,0BACE,UACF,CAEA,eAEE,YAAa,CACb,qBAAsB,CACtB,WAAY,CAHZ,iBAIF,CAEA,gBACE,QAAO,CACP,YAAa,CACb,iBACF,CAEA,cAQE,wBAAyB,CAHzB,qBAAyB,CACzB,kBAAmB,CAInB,iCAAsC,CAHtC,eAAgB,CAEhB,WAAY,CARZ,iBAAkB,CAElB,UAAW,CADX,QAAS,CAET,SAOF,CACA,iBASE,4BAA8B,CAD9B,wBAA6B,CAL7B,qBAAuB,CACvB,2BAA6B,CAG7B,yBAA2B,CAD3B,sBAAwB,CAKxB,gCAAoC,CADpC,OAAO,CARP,qBAAuB,CAGvB,yBAA2B,CAJ3B,mBAYF,CAEA,kCAME,kBAAmB,CACnB,aAAc,CAJd,OAAQ,CACR,cAMF,CACA,iEATE,kBAAmB,CADnB,YAAa,CAOb,cAAe,CACf,eAAgB,CAJhB,WAgBF,CAVA,+BAKE,kBAAmB,CAEnB,iBAAkB,CADlB,UAAY,CAHZ,cAOF,CACA,wCACE,kBACF,CAEA,qCACE,kBACF,CAEA,0BACE,cACF,CACA,oBAEE,wBACF,CAKA,mCACE,kBACF,CAEA,cAIE,kBAAmB,CAGnB,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CAFf,eAAgB,CALhB,iBAAkB,CAElB,OAAQ,CADR,SAOF,CAEA,UAKE,kBAAmB,CAGnB,eAAgB,CADhB,qBAAyB,CAJzB,kBAAmB,CACnB,YAAa,CAFb,WAAY,CAIZ,sBAAuB,CAKvB,UAAW,CAFX,SAAU,CARV,UAWF,CAEA,mBACE,YAAa,CACb,QAAS,CACT,kBACF,CAEA,gBAEE,kBAAmB,CADnB,UAEF,CAEA,2BAGE,kBAAmB,CADnB,YAAa,CADb,iBAGF,CAEA,2BAEE,kBAAmB,CADnB,YAAa,CAEb,OACF,CAEA,0BACE,UAAY,CACZ,sBACF,CAEA,gCACE,SACF,CAEA,iBACE,iBAEF,CAGA,qCAJE,kCAOA,CACA,4CAEE,eAAgB,CADhB,iBAIF,CAEA,oCAEE,oCAAsC,CADtC,eAEF,CAMA,6FAEE,WAAY,CADZ,UAEF,CACA,wBACE,wBAA0B,CAC1B,yBACF,CACA,8BACE,qBACF,CAGD,2CACC,oCACD,CAEA,gBACC,iBAAkB,CAClB,YAAa,CAGb,SAAU,CADV,uBAAwB,CADxB,SAGF,CAEA,uBACE,eAAgB,CAChB,eACF,CAEA,gCACE,eACF,CACA,8BACE,kBAAmB,CACnB,oBAAqB,CACrB,UACF,CAEA,sBACE,4BAA8B,CAC9B,oBAAqB,CACrB,oBACF,CAEA,8DACE,YACF,CAMA,6CACE,qBACF,CAEA,gCACE,oCACF,CAEA,SACE,8BACF,CAEA,eACE,0BACF,CCvSA,WAKE,iBADA,aAbiB,CAgBjB,sCANA,aACA,sBACA,SAbY,CAsBV,iCACE,aAIJ,8CAIE,mBAFA,aACA,SACA,CAIA,wDAGE,qBAFA,aACA,SACA,CAIJ,yBACE,aAGF,yBACE,aACA,sBAEA,2BACE,UAQJ,wCAEE,oBA7DU,CA+DZ,sBACE,iBAhEU,CAuEZ,uCACE,iBAGF,0EAEE,kBAEA,8KAKE,gBAHA,WAEA,wBAEA,kBAHA,WAGA,CAGF,wFAGE,yBADA,yBADA,UAEA,CAGF,sFAGE,uBADA,WADA,OAEA,CAGF,4GACE,aAKF,uGAEE,wBAGF,oDAEE,+BADA,sBACA,CAGF,mDACE,uBAOF,4CAIE,gBAIA,uBAPA,WAMA,0BAJA,wBAEA,kBACA,WAJA,WAMA,CCrDN,WAIE,qBAHA,oBAjCiB,CAmCjB,oBAjCkB,CAgClB,kBAnCqB,CAwCnB,2BACE,uBACA,YACA,eAQJ,wCAcE,8BAOA,wDACE,yBASJ,sBAaE,2BAOA,8BACE,sBAaJ,yCACE,WAGF,mEACE,YASA,8KAEE,oBAnIa,CAoIb,kBAnIa,CAsIf,8GACE,gCAtIc,CA0IlB,qCACE,kBAEA,4CACE,oBAhJa,CAiJb,kBAhJa,CC3DnB,yBACE,eAAiB,CACjB,iBAAkB,CAElB,8BAAwC,CACxC,aAAc,CAFd,YAGF,CAEA,yBACE,cACF,CAEA,qCACE,wBAAyB,CAIzB,+BAAgC,CAHhC,eAAgB,CAEhB,gBAAiB,CADjB,iBAGF,CAEA,qCAGE,+BAAgC,CAFhC,gBAAiB,CACjB,iBAEF,CAEA,2CACE,wBACF,CAEA,gDACE,wBACF,CAEA,yDACE,wBACF,CAGA,kCACE,gCACF,CAEA,kBACE,GACE,SAAU,CACV,2BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAGA,yBACE,yBAEE,aAAc,CADd,YAEF,CAEA,yBACE,cACF,CAEA,0EAEE,eACF,CACF,CAGA,kCACE,gBACF,CAGA,oCACE,8BACF,CAEA,6DACE,wBACF,CAEA,6DACE,qBACF,CAEA,mEACE,wBACF,CAEA,2EACE,wBACF,CAEA,iFACE,wBACF,CCnGA,wBACE,kBAAmB,CACnB,gBACF,CAEA,yBACE,eAAiB,CACjB,iBAAkB,CAElB,8BAAwC,CACxC,aAAc,CAFd,YAGF,CAEA,0BACE,cACF,CAEA,sCACE,wBAAyB,CAIzB,+BAAgC,CAChC,UAAW,CAJX,eAAgB,CAEhB,iBAAkB,CADlB,iBAIF,CAEA,sCAGE,+BAAgC,CAFhC,iBAAkB,CAClB,iBAAkB,CAElB,qBACF,CAGA,2BACE,qBAAuB,CACvB,oCACF,CAEA,oCACE,wBACF,CAGA,4BACE,kCAAoC,CACpC,6BAA8B,CAC9B,oCACF,CAEA,qCACE,kCACF,CAEA,+BACE,kCAAoC,CACpC,+BACF,CAGA,mCACE,qCACF,CAcA,4CAKE,2BAA4B,CAJ5B,kCAAoC,CAEpC,kCAAoC,CADpC,uBAAyB,CAEzB,eAEF,CAEA,8CACE,kCAAoC,CAEpC,kCAAoC,CADpC,uBAAyB,CAEzB,eACF,CAEA,iBACE,MACE,SACF,CACA,IACE,UACF,CACF,CAGA,qCACE,8BACF,CAEA,8DACE,wBACF,CAEA,8DACE,qBACF,CAEA,uDACE,kCACF,CAGA,mCACE,gBACF,CAGA,yBACE,yBAEE,aAAc,CADd,YAEF,CAEA,0BACE,cACF,CAEA,4EAEE,gBACF,CACF,CAGA,kBAEE,kBAAmB,CADnB,mBAAoB,CAEpB,OAAQ,CACR,kBACF,CAEA,iCAGE,iBAAkB,CADlB,UAAW,CADX,SAGF,CAEA,8BACE,wBACF,CAEA,+BAEE,2BAA4B,CAD5B,wBAEF,CAEA,iBACE,OACE,SACF,CACA,OACE,UACF,CACF,CAGA,2BACE,aAAc,CACd,iBACF,CAEA,0BACE,aAAc,CACd,eACF,CAGA,+BAEE,+BAAgC,CADhC,oBAEF,CAEA,kDAEE,UAAW,CADX,SAEF,CAEA,wDACE,kBAAmB,CACnB,iBACF,CAEA,wDACE,kBAAmB,CACnB,iBACF,CAEA,8DACE,kBACF,CAGA,0BACE,iBACF,CAEA,sCACE,aACF,CCtNA,wBACE,kBAAmB,CACnB,gBACF,CAEA,+BACE,cACF,CAEA,2CACE,wBAAyB,CAIzB,+BAAgC,CAHhC,eAAgB,CAEhB,iBAAkB,CADlB,iBAGF,CAEA,2CAGE,+BAAgC,CAFhC,iBAAkB,CAClB,iBAEF,CAEA,iDACE,wBACF,CAEA,sDACE,wBACF,CAEA,+DACE,wBACF,CAGA,wCACE,qCACF,CAEA,0BACE,GACE,SAAU,CACV,2BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAGA,kCAEE,iBAAkB,CADlB,+BAEF,CAEA,uCACE,+BACF,CAGA,iCACE,iBAAkB,CAClB,eACF,CAEA,yCACE,kBAAmB,CACnB,oBACF,CAEA,+CACE,kBAAmB,CACnB,oBACF,CAGA,yBACE,wBACE,YACF,CAEA,+BACE,cACF,CAEA,sFAEE,gBACF,CAEA,8BACE,qBAAsB,CACtB,QACF,CAEA,2CACE,sBACF,CACF,CAGA,wCACE,gBACF,CAGA,oCACE,eAAgB,CAChB,iBACF,CAGA,mCACE,wBAAyB,CACzB,wBAAyB,CACzB,iBACF,CAEA,sCACE,aACF,CAGA,wCACE,aACF,CAEA,uCACE,aACF,CAGA,mCACE,8CACF,CAEA,uCACE,eACF,CAEA,qCACE,eACF;CT/IC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA6BC,CAAwzF,8CAA8C,UAAU,CAAixO,gBAAkC,OAAO,4CAA4C,CAAC,wBAAwB,CAAC,+BAA+B,CAAC,WAAW,CAAC,UAAU,CAAC;AACzyU;;EAEE;;AAEF;;EAEE,CAA+sD,2GAA2G,gBAAgB,CAAC,iBAAiB;;AAE91D;;EAEE,CAAsmL,iHAAiH,iBAAiB;;AAE1uL;;EAEE,CAAm1F,uNAAuN,UAAU;;AAEtjG;;;EAGE,CAAqwE,4DAAkD,CUjDzzE,gBAGE,wBAAyB,CAEzB,aAAc,CACd,iBACF,CAmBA,2BAtBE,iBAAkB,CAFlB,WAAY,CADZ,UAkCF,CATA,WAGE,qBAAuB,CAEvB,kDAAwE,CACxE,YAAa,CACb,qBAAsB,CACtB,eACF,CAGA,kBAGE,kBAAmB,CAEnB,wBAAyB,CACzB,4BAA6B,CAL7B,YAAa,CACb,6BAA8B,CAE9B,gBAGF,CAEA,iBAGE,WAAY,CACZ,WAAY,CAFZ,cAAe,CADf,eAIF,CAEA,oBACE,YAAa,CACb,OACF,CAEA,mBACE,eAAgB,CAChB,WAAY,CAKZ,iBAAkB,CADlB,UAAW,CAHX,cAAe,CAEf,cAAe,CADf,eAIF,CAEA,yBACE,wBACF,CAGA,4CACE,aAAc,CACd,cACF,CAGA,kDACE,aAAc,CACd,oBACF,CAGA,sBAEE,kBAAmB,CAEnB,eAAgB,CAChB,WAAY,CAKZ,iBAAkB,CADlB,UAAW,CAHX,cAAe,CALf,YAAa,CAOb,cAAe,CAGf,WAAY,CARZ,sBAAuB,CAIvB,eAAgB,CAKhB,UACF,CAEA,4BACE,wBACF,CAGA,mBACE,WAAY,CACZ,aAAc,CACd,WACF,CAGA,sBAKE,YAAa,CAFb,MAAO,CAFP,cAAe,CACf,KAAM,CAEN,WAAY,CAEZ,YACF,CAGA,aACE,WACF,CAGA,SACE,cACF,CAGA,gBACE,eAAgB,CAEhB,QAAS,CADT,SAEF,CAEA,gBAUE,kBAAmB,CAPnB,qBAAuB,CACvB,qBAAsB,CACtB,iBAAkB,CAClB,cAAe,CAEf,YAAa,CACb,6BAA8B,CAP9B,iBAAkB,CADlB,gBAAiB,CAMjB,+BAIF,CAEA,sBACE,wBACF,CAEA,yBAEE,kBAAmB,CADnB,UAEF,CAEA,aAGE,wBAAyB,CACzB,iBAAkB,CAClB,UAAW,CAJX,cAAe,CACf,eAIF,CAOA,+BACE,oCACF,CAGA,qBAEE,WAAY,CADZ,UAEF,CAGA,kBACE,YACF,CAEA,YAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,mCAEF,CAEA,WACE,wBAAyB,CACzB,iBAAkB,CAClB,YAAa,CACb,iBACF,CAEA,YAEE,UAAW,CADX,cAAe,CAEf,iBACF,CAEA,YACE,cAAe,CACf,eACF,CAGA,mBACE,kBACF,CAGA,cAEE,8BAA+B,CAD/B,WAEF,CAGA,cACE,yBACF,CAGA,iBACE,aACF,CAEA,0BAKE,kBAAmB,CADnB,mBAAoB,CAEpB,cAAe,CAJf,iBAAkB,CADlB,gBAAiB,CAEjB,eAIF,CAEA,yCAEE,cAAe,CADf,eAEF,CAEA,+BACE,0BAAyC,CACzC,oBAAqB,CACrB,aACF,CAEA,gCACE,0BAAwC,CACxC,oBAAqB,CACrB,aACF,CAEA,iCACE,0BAAyC,CACzC,oBAAqB,CACrB,aACF;;;;ACzQA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA6BE,CAID,gDACC,eACF,CAEA,iBACE,eACF,CAEA,iBACE,wBACF,CAEA,wUAME,yBACF,CAGA,8BACE,QAAS,CACT,uBACF,CAEA,kEAEE,gBACF,CAEA,0FAEE,0BACF,CAEA,sMAME,gBAAiB,CACjB,SACF,CAEA,4lCAQE,cAAe,CACf,eACF,CAEA,sDACE,qBACF,CAEA,oBACE,YACF,CAEA,wBACE,iBACF,CAEA,uBACE,iBAAkB,CAClB,OACF,CAEA,0BAIE,WAAY,CAHZ,iBAAkB,CAElB,OAEF,CAEA,2JAQE,kBAAuB,CACvB,8BAA+B,CAC/B,2BACF,CAEA,2CAEE,YAAmB,CAAnB,eACF,CAEA,2BACE,uBAAyB,CAUzB,gBAAiB,CAEjB,cAAe,CAIf,cAAe,CALf,QAAS,CAQT,eAAgB,CADhB,cAAe,CAHf,eAAgB,CAFhB,kBAAmB,CACnB,OAMF,CAMA,8CACE,UACF,CAEA,kCAWE,qBAAsB,CANtB,4BAA6B,CAF7B,mBAAoB,CACpB,2BAA4B,CAF5B,kBAAmB,CAKnB,gBAAiB,CADjB,WAAY,CAEZ,gBAAiB,CAMjB,YAAa,CACb,eAAgB,CAFhB,eAAgB,CAJhB,aAAoB,CAGpB,kBAAmB,CAInB,oBACF,CAEA,oDAEE,0BAA2B,CAD3B,oBAEF,CAEA,4BAIE,4BAA6B,CAF7B,gBAAiB,CACjB,2BAEF,CAEA,qCAGE,0BAA2B,CAD3B,iBAEF,CAEA,4DAEE,0BACF,CAEA,8EACE,2BACF,CAGA,4CACE,mBAAoB,CACpB,2BACF,CAEA,qDAEE,0BAA2B,CAD3B,oBAEF,CAEA,oDACE,0BACF,CAEA,6DACE,2BACF,CAEA,gEAEE,yBACF,CAEA,yYAKE,0BAA2B,CAD3B,oBAEF,CAEA,6aACE,mBAAoB,CACpB,2BACF,CAMA,sVAIE,qBACF,CAEA,iBACE,wBAAyB,CACzB,UAAW,CAEX,eAAmB,CADnB,iBAAkB,CAElB,kBACF,CAEA,uBACE,SACF,CAEA,wBACE,qBACF,CAEA,iCACE,eACF,CAEA,6BACE,oBAAqB,CACrB,eACF,CAGA,wBAEE,WAAY,CADZ,iBAEF,CAEA,+BACE,sBACF,CAGA,gCACE,UACF,CAEA,6BACE,SACF,CAEA,6BACE,SACF,CAGA,+BAEE,gBAAiB,CADjB,WAEF,CAEA,iBACE,WACF,CAEA,uBACE,WACF,CAEA,iBACE,WACF,CAEA,qCACE,WACF,CAEA,cACE,WACF,CAEA,kCACE,WACF,CAEA,yBACE,iBACF,CAEA,8EAGE,YACF,CAEA,iEAIE,eACF,CAEA,qBACE,aACF,CAEA,sJAGE,iBACF,CAEA,oFAGE,eACF,CAIA,cAME,aAAc,CAHd,0GAA6H,CAC7H,cAAe,CACf,eAAmB,CAJnB,iBAAkB,CAClB,yBAKF,CAEA,gBACE,aACF,CAEA,yBAEE,aAAc,CACd,iBAAkB,CAClB,YAAa,CAHb,iBAIF,CAEA,2BAEE,kCACF,CAEA,0BACE,kBACF,CAEA,wEAEE,YACF,CAEA,8FAEE,gBACF,CAEA,0BAIE,aAAc,CAHd,0GAA6H,CAC7H,cAAe,CACf,eAAmB,CAEnB,iBAAoB,CACpB,eACF,CAEA,4BACE,aAAc,CACd,cACF,CAEA,8BAIE,QAAS,CAIT,QAAS,CAHT,QAAS,CAFT,SAAU,CAGV,SAAU,CALV,iBAAkB,CAMlB,OAAQ,CALR,UAOF,CAIA,mCAGE,iBAAkB,CAGlB,WAAY,CALZ,iBAAkB,CAClB,KAAM,CAGN,SAAU,CADV,WAGF,CAEA,gCAGE,iBAAkB,CAElB,UAAW,CAHX,MAAO,CADP,iBAAkB,CAKlB,UAAW,CAFX,WAGF,CAEA,gKAIE,wBACF,CAEA,wCAIE,wBAAyB,CAIzB,gBAAiB,CADjB,4BAA6B,CAF7B,YAAa,CAIb,eAAgB,CAChB,cAAmB,CATnB,iBAAkB,CAClB,UAAY,CACZ,KAAM,CAGN,OAKF,CAEA,iDAEE,2BAA4B,CAC5B,iBAAkB,CAFlB,SAAW,CAIX,aAAkB,CADlB,gBAEF,CAEA,qCAIE,wBAAyB,CAGzB,6BAA8B,CAJ9B,QAAS,CAET,YAAa,CACb,QAAS,CAJT,MAAO,CAMP,cAAe,CAPf,iBAQF,CAEA,2FAEE,aAAc,CACd,WACF,CAEA,8LAQE,iBACF,CAEA,sPAcE,kBAAmB,CADnB,QAAS,CALT,UAAW,CAGX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAKF,CAEA,6BACE,UACF,CAEA,+BACE,UACF,CAEA,+BACE,WACF,CAEA,+BACE,WACF,CAEA,+BACE,WACF,CAEA,+BACE,WACF,CAEA,+BACE,WACF,CAEA,+BACE,WACF,CAEA,8DAEE,kCACF,CAEA,0EAEE,wBACF,CAEA,wFAEE,wBAAyB,CACzB,UACF,CAEA,mBAcE,qBAAsB,CAbtB,WAAY,CAYZ,eAAgB,CALhB,kCAAmC,CAOnC,+BAAiC,CAHjC,UAAW,CADX,aAAc,CANd,mBAAoB,CAEpB,iBAAkB,CADlB,gBAAiB,CAHjB,QAAS,CADT,eAAgB,CAEhB,iBAAsB,CAKtB,WAQF,CAEA,yBACE,YACF,CAEA,yBAGE,MAAO,CAFP,iBAAkB,CAClB,KAEF,CAEA,gBACE,iBAAkB,CAElB,OACE,4CAA8C,CAG9C,wBAAyB,CACzB,+BAAiC,CAFjC,WAAY,CADZ,UAIF,CACF,CAEA,6BACE,YACF,CAEA,qCACE,YACF,CAKA,wBACE,UACF,CAEA,yBACE,iBACF,CAEA,gCAEE,UAAW,CADX,WAAY,CAIZ,aAAc,CAFd,iBAAkB,CAClB,SAEF,CAEA,yCACE,UACF,CAEA,0CAEE,UAAW,CADX,WAAY,CAIZ,aAAc,CADd,QAAS,CADT,iBAGF,CAKA,sBACE,eACF,CAEA,wBACE,iBACF,CAEA,uBACE,gBACF,CAEA,yBACE,kBACF,CAKA,qBACE,kBACF,CAEA,wBACE,qBACF,CAEA,wBACE,qBACF,CAKA,6BACE,UACF,CAKA,sBACE,QACF,CAEA,uCAGE,eAAiB,CAFjB,qBAAsB,CACtB,uBAEF,CAEA,yKAKE,4BACF,CAEA,kDAGE,sBAAuB,CADvB,kBAEF,CAEA,kCAEE,aAAc,CADd,cAAe,CAEf,kBACF,CAEA,gCACE,iBACF,CAEA,sEAEE,eACF,CAEA,kBACE,UACF,CAEA,mBACE,WACF,CAEA,gCACE,kBAAmB,CACnB,aACF,CAMA,oDAEE,0BAA2B,CAM3B,yCAA6C,CAC7C,gCAAiC,CANjC,wBAAyB,CAIzB,gBAGF,CAEA,8CAIE,uBAAwB,CADxB,kCAEF,CAGA,8JAIE,aAAc,CACd,UAAY,CACZ,YAAa,CACb,YACF,CAEA,kMAEE,SAAW,CADX,cAEF,CAEA,yBAEE,0BAA4B,CAC5B,4BAA6B,CAC7B,oCACF,CAEA,yBAEE,0BAA4B,CAC5B,4BAA6B,CAC7B,oCACF,CAIA,mCAGE,UAAc,CACd,cAAe,CAHf,WAAY,CACZ,cAAe,CAIf,iBAAkB,CADlB,UAEF,CAEA,4CACE,UACF,CAEA,gDACE,UACF,CAEA,sDACE,aACF,CAEA,4CACE,UACF,CAEA,2CACE,aACF,CAIA,+CACE,UACF,CAEA,uCAGE,cAAe,CACf,oBAAqB,CAHrB,iBAAkB,CAClB,qBAGF,CAEA,iDACE,UACF,CAEA,oCAiBE,eAAgB,CAXhB,wBAAyB,CAGzB,kBAAmB,CAOnB,yBAAwC,CARxC,UAAW,CAKX,cAAe,CAHf,cAAe,CAEf,WAAY,CARZ,SAAW,CAGX,eAAgB,CANhB,iBAAkB,CAIlB,SAAU,CAaV,iBAAkB,CAhBlB,OAAQ,CACR,0BAA8B,CAQ9B,UAQF,CAEA,6CAEE,QAAS,CADT,UAEF,CAEA,sFACE,iBACF,CAEA,qFACE,gBACF,CAEA,6BACE,iBACF,CAEA,qHAGE,iBAAkB,CADlB,iBAEF,CAEA,mHAEE,gBAAiB,CACjB,kBACF,CAEA,2IAGE,iBAAkB,CADlB,iBAEF,CAEA,yIAEE,gBAAiB,CACjB,kBACF,CAEA,8CAEE,cAAe,CADf,yBAEF,CACA,kDAiBE,2BAA4B,CAD5B,2BAA4B,CAD5B,uBAAwB,CAHxB,UAAW,CACX,WAAY,CAFZ,SAAW,CAPX,eAAgB,CAGhB,gBAAiB,CACjB,eAAgB,CAChB,iBAAkB,CAClB,UAAW,CARX,OAAQ,CAYR,SAIF,CAEA,2DAOE,0BAA2B,CAF3B,SAAU,CADV,cAAe,CADf,iBAAkB,CAGlB,UAEF,CAEA,4DAEE,gVACF,CAEA,6DAEE,gVACF,CAEA,kFACE,WAAY,CACZ,oBAAqB,CAIrB,kBAAmB,CAHnB,iBAIF,CAGA,0CACE,qBACF,CAEA,0FAEE,kBACF,CAEA,6BACE,iBACF,CAEA,mCAME,2BAAkC,CAClC,iBAAkB,CAClB,yBAA2B,CAP3B,UAAW,CAIX,SAAW,CAHX,iBAAkB,CAElB,OAAQ,CADR,KAMF,CAEA,4CAIE,gBAAiB,CADjB,4BAAmC,CAFnC,MAAO,CACP,UAGF,CAEA,iCACE,YAAa,CAEb,iBAAkB,CADlB,YAEF,CAEA,wCAcE,uBAAwB,CANxB,qBAAsB,CADtB,WAAkB,CAAlB,0BAAkB,CANlB,gEAAgF,CAGhF,qBAAsB,CAOtB,cAAe,CADf,WAAY,CAGZ,mBAAqB,CADrB,WAAY,CAHZ,WAMF,CAEA,iDAEE,gBAAiB,CADjB,2BAEF,CAEA,8CAEE,6BAA8B,CAC9B,iBAAkB,CAFlB,wFAGF,CAEA,uDAEE,gBAAiB,CADjB,8BAEF;AACA;;EAEE,CACF,kCACE,YAAa,CACb,iBAAkB,CAElB,YACF,CAEA,wMAKE,YACF,CAEA,uCAME,iBAAuB,CAAvB,kBAAuB,CAAvB,4BACF,CAEA,gDAEE,qBAAsB,CADtB,sBAEF,CAEA,kCACE,YACF,CAEA,yBACE,iBACF,CAEA,iCACE,eAAiB,CACjB,cAAe,CAEf,cAAe,CACf,eAAgB,CAFhB,iBAAsB,CAItB,sBAAuB,CADvB,kBAEF,CAEA,6CAIE,cACF,CAEA,sDAEE,mBAAoB,CADpB,oBAEF,CAEA,0CAEE,aAAc,CADd,iBAEF,CAEA,yCACE,kBACF,CAEA,6CACE,4BAA6B,CAG7B,cAAe,CAFf,QAAS,CACT,SAEF,CAEA,4CACE,UAAW,CACX,cACF,CAEA,kDACE,eAAgB,CAChB,UAAW,CACX,cACF,CAEA,uCACE,YACF,CAEA,gDACE,gBAAiB,CACjB,gBACF,CAEA,yDAEE,eAAgB,CADhB,iBAEF,CAEA,mDAGE,QAAS,CAFT,eAAgB,CAChB,iBAAkB,CAElB,OACF,CAEA,4DAEE,MAAO,CADP,SAEF,CAEA,oCACE,eACF,CAEA,+BAKE,SAAU,CACV,wBAA0B,CAF1B,eAAgB,CAHhB,wBAA0B,CAE1B,oBAAsB,CADtB,eAKF;;AAIA;;EAEE,CACF,0BACE,eAAgB,CAEhB,qBAAsB,CADtB,iBAAkB,CAElB,UAAW,CAKX,WAAY,CAJZ,aAAc,CACd,eAAgB,CAEhB,oBAAqB,CADrB,WAGF,CAEA,mCACE,UACF,CAEA,mCACE,oBACF,CAEA,iCACE,YACF,CAEA,gCACE,qBAAsB,CACtB,UAAW,CACX,cACF,CAEA,mCACE,YAAa,CACb,iBAAkB,CAElB,YACF,CAEA,6MAKE,YACF,CAEA,6BAME,iBAAuB,CAAvB,kBAAuB,CAAvB,4BACF,CAEA,sCAEE,qBAAsB,CADtB,sBAEF,CAEA,mCACE,YACF,CAEA,0BACE,iBACF,CAEA,kCACE,eAAiB,CACjB,cAAe,CAEf,cAAe,CACf,eAAgB,CAFhB,iBAAsB,CAItB,sBAAuB,CADvB,kBAEF,CAEA,8CAIE,cACF,CAEA,uDACE,mBAAoB,CACpB,oBACF,CAEA,2CAEE,aAAc,CADd,iBAEF,CAEA,0CACE,kBACF,CAEA,8CACE,4BAA6B,CAG7B,cAAe,CAFf,QAAS,CACT,SAEF,CAEA,6CACE,UACF,CAEA,mDACE,eAAgB,CAChB,UAAW,CACX,cACF,CAEA,2DACE,YACF,CAOA,2GAJE,gBAAiB,CACjB,iBAMF,CAEA,oDAGE,QAAS,CAFT,eAAgB,CAChB,iBAAkB,CAElB,OACF,CAEA,6DAEE,MAAO,CADP,SAEF,CAEA,qCACE,eACF;;AAIA;;EAEE,CAEF,4CACE,YAAa,CACb,iBAAkB,CAClB,YACF,CAEA,0PAKE,YACF,CAEA,sCAGE,iBAAuB,CAAvB,kBAAuB,CAAvB,4BACF,CAEA,mCACE,iBACF,CAEA,2CACE,eAAiB,CACjB,cAAe,CAEf,cAAe,CACf,eAAgB,CAFhB,iBAAsB,CAItB,sBAAuB,CADvB,kBAEF,CAEA,uDAIE,cACF,CAEA,gEACE,mBAAoB,CACpB,oBACF,CAEA,oDAEE,aAAc,CADd,iBAEF,CAEA,mDACE,kBACF,CAEA,uDACE,4BAA6B,CAC7B,QAAS,CACT,SACF,CAEA,sDACE,UACF,CAEA,4DACE,eAAgB,CAChB,UAAW,CACX,cACF,CAEA,0DACE,gBAAiB,CACjB,iBACF,CAEA,6DAGE,QAAS,CAFT,eAAgB,CAChB,iBAEF,CAEA,8CACE,eACF,CAEA,+BACE,6BAA8B,CAC9B,YAAa,CACb,eACF,CAEA,uDACE,qBAAsB,CACtB,WACF,CAGA,kCACE,eACF,CAGA,sCAGE,kBAAmB,CADnB,gBAAiB,CADjB,iBAGF,CAGA,6CACE,uCACF,CAEA,iDACE,eACF,CAGA,kCACE,uCACF,CAEA,4DACE,SACF,CAEA,yHAKE,qBAAsB,CAHtB,mBAAoB,CACpB,eAAiB,CACjB,WAAY,CAEZ,UACF,CAEA,yCACE,iBACF,CAEA,2CAGE,wBAAyB,CAFzB,wBAAyB,CACzB,aAEF,CAEA,6BACE,aAAc,CACd,iBACF,CAEA,sCAEE,gBAAiB,CADjB,cAEF,CAEA,yDACE,oBACF,CAEA,6DACE,eACF,CAEA,qCACE,gBACF,CAEA,8CACE,eACF,CAEA,uCACE,oBAAqB,CAMrB,UAAW,CAFX,gBAAc,CACd,qBAEF,CAEA,gDACE,eAAgB,CAChB,cACF,CAIA,yBACE,iBAAoB,CACpB,iBAAkB,CAClB,iBACF,CAEA,+BAEE,wBAAyB,CADzB,iBAEF,CAEA,6BACE,iBACF,CAGA,oCACE,cAAe,CACf,oBACF,CAEA,0CACE,qBAAsB,CACtB,UAAW,CACX,cAAe,CACf,mBAAoB,CACpB,eAAiB,CACjB,eAAiB,CACjB,WAAY,CACZ,cACF,CAEA,gDACE,oBACF,CAEA,sCACE,aAAc,CACd,iBACF,CAEA,+CAEE,gBAAiB,CADjB,cAEF,CAEA,4CACE,wBAAyB,CACzB,oBAAqB,CACrB,UACF,CAEA,0DACE,wBAAyB,CACzB,oBAAqB,CACrB,UACF,CAEA,kDACE,oBACF,CAGA,0BACE,cAAe,CACf,iBAAkB,CAClB,iBACF,CAEA,iCACE,wBAAyB,CAEzB,wBAAyB,CADzB,iBAAkB,CAElB,mBAAoB,CACpB,eAAiB,CACjB,eAAiB,CAIjB,eAAgB,CAHhB,yBAA0B,CAC1B,sBAAuB,CACvB,kBAEF,CAEA,uCACE,wBAAyB,CACzB,wBACF,CAEA,wCACE,WAAY,CACZ,aAAc,CACd,iBAAkB,CAClB,UAAW,CACX,KACF,CAEA,yCACE,WAAY,CACZ,aAAc,CACd,iBAAkB,CAClB,UAAW,CACX,OACF,CAGA,wDACE,WACF,CAEA,iEACE,wBACF,CAEA,8CAEE,wBAAyB,CADzB,iBAAkB,CAElB,WACF,CAEA,yBACE,oBAAqB,CAGrB,WAAY,CAFZ,aAAc,CACd,gBAEF,CAEA,kCAEE,eAAgB,CADhB,cAEF,CAEA,oCACE,cACF,CAEA,2CACE,aAAc,CACd,iBACF,CAEA,oDAEE,gBAAkB,CADlB,cAEF,CAEA,+BACE,qBACF,CAEA,sCACE,kBACF,CAOA,oCACE,iBACF,CAEA,oFAEE,UAAW,CAGX,aAAc,CAFd,iBAAkB,CAClB,OAAQ,CAER,0BACF,CAEA,mCACE,iBACF,CAEA,uDACE,gBACF,CAMA,iHACE,iBACF,CAEA,wDACE,gBACF,CAEA,0CAEE,WAAY,CADZ,SAEF,CAEA,mDAGE,WAAY,CADZ,QAAS,CADT,UAGF,CAEA,0CAEE,WAAY,CADZ,QAEF,CAEA,mDAGE,WAAY,CADZ,SAAa,CADb,SAGF;;AAIA;;EAEE,CACF,8EAEE,UAAW,CACX,aAAc,CAGd,QAAS,CAFT,eAAgB,CAChB,iBAEF,CAEA,iEAEE,iBACF,CAEA,wCAEE,UAAW,CADX,WAEF,CAEA,sCACE,WAAY,CACZ,OACF,CAEA,8JAEE,UACF,CAEA,gGAEE,yBACF,CAEA,0LAEE,aACF,CAEA,mFACE,WAAY,CAGZ,WACF,CAEA,qIAEE,WAAY,CAGZ,eACF,CAEA,2EACE,YACF,CAEA,8FAIE,YAAa,CADb,WAAY,CADZ,iBAGF,CAEA,+CACE,kBAAmB,CAInB,mBAAoB,CADpB,wBAAyB,CADzB,KAAM,CADN,SAAU,CAIV,WACF,CAEA,+CACE,kBAAmB,CACnB,oBAAkC,CAClC,YAAa,CAEb,mBAAoB,CADpB,WAEF,CAEA,4IAEE,aACF,CAEA,6EACE,WAAY,CAGZ,WACF,CAEA,yHAEE,WAAY,CAGZ,eACF,CAEA,kEACE,YACF,CAEA,wFAIE,YAAa,CAFb,iBAAkB,CAClB,UAEF,CAEA,4CACE,kBAAmB,CACnB,UAAW,CACX,MAAO,CACP,eAAgB,CAChB,WACF,CAEA,4CACE,kBAAmB,CACnB,oBAAkC,CAClC,YAAa,CAEb,mBAAoB,CADpB,WAEF,CAEA,gIAEE,aACF,CAEA,4GACE,SACF,CAMA,uNACE,UACF,CAEA,wGACE,UACF,CAEA,wGACE,WACF,CAEA,wGACE,WACF,CAEA,wGACE,WACF,CAEA,wGACE,WACF,CAEA,wGACE,WACF,CAEA,wGACE,WACF,CAEA,sDAEE,iBAAkB,CADlB,iBAEF,CAEA,qDACE,gBAAiB,CACjB,kBACF,CAEA,iEAEE,iBAAkB,CADlB,iBAEF,CAEA,gEACE,gBAAiB,CACjB,kBACF,CAGA,iDAYE,aAAc,CACd,UAAW,CALX,SAAW,CAMX,eAAgB,CAVhB,eAAgB,CAMhB,gBAAiB,CACjB,eAAoB,CALpB,iBAAkB,CAClB,WAAY,CALZ,OAaF,CAEA,0DACE,UAAW,CAIX,cAAmB,CADnB,iBAAkB,CAFlB,UAIF,CAGA,iIAEE,WACF,CAEA,wDACE,WACF,CAEA,wDACE,WACF,CAEA,wDACE,WACF,CAEA,wDACE,WACF,CAEA,wDACE,WACF,CAEA,wDACE,WACF,CAEA,wDACE,WACF,CAGA,4FACE,iBACF,CAEA,wDACE,YACF,CAIA,kCAEE,gBAAiB,CADjB,eAEF,CAEA,2CAEE,iBAAkB,CADlB,gBAEF,CAEA,sCACE,oBAAqB,CAErB,SAAU,CADV,iBAAkB,CAElB,UACF,CAEA,+CAEE,SAAW,CADX,UAEF,CAEA,wIAGE,SAAW,CADX,UAEF,CAEA,0JACE,SAAU,CACV,UACF,CAEA,sCACE,oBACF,CAEA,4CACE,oBAAqB,CAGrB,UAAW,CADX,UAAW,CADX,UAGF,CAEA,qDACE,WACF,CAEA,4CAKE,UAAW,CAJX,WAAY,CAEZ,oBAAqB,CADrB,aAAc,CAEd,iBAEF,CAEA,sCAKE,cAAe,CAJf,oBAAqB,CAGrB,SAAW,CAFX,iBAAkB,CAClB,UAGF,CAEA,+CACE,SAAU,CACV,UACF,CAEA,6DACE,WACF,CAEA,+DACE,WACF,CAEA,wIAGE,SAAW,CADX,OAEF,CAEA,0JACE,MAAO,CACP,UACF;;AAIA;;;EAGE,CAEF,aAKI,eAAgB,CAEhB,gBAAyB,CAAzB,2BAAyB,CAHzB,UAAW,CAFX,aAAc,CAMd,qDAA2D,CAL3D,iBAAkB,CAFlB,YAQJ,CAMA,uCAEI,WAAY,CACZ,aACJ,CACA,mBAAqB,UAAY,CAEjC,uBACI,YACJ,CAEA,sBAEI,oCAA0C,CAD1C,iBAEJ,CAEA,aACI,UAAW,CAEX,UAAW,CADX,WAEJ,CAEA,YACI,iBAAkB,CAClB,iBACJ,CAEA,YAUI,qBAAsB,CATtB,oBAAqB,CAMrB,cAAe,CAEf,eAAiB,CADjB,gBAAiB,CAHjB,QAAS,CADT,eAAgB,CAEhB,eAAgB,CAJhB,iBAAkB,CAClB,YAQJ,CACA,mBACI,cAAe,CAIf,MAAO,CADP,QAAS,CAGT,SAAU,CALV,iBAAkB,CAIlB,OAAQ,CAHR,YAKJ,CAEA,sBAcI,wBAA6B,CAC7B,uBAAkC,CAClC,2BAA4B,CAC5B,uBAAwB,CAXxB,QAAS,CAHT,cAAe,CADf,aAAc,CAOd,WAAY,CASZ,UAAW,CAbX,YAAa,CAQb,eAAgB,CANhB,SAAU,CAHV,iBAAkB,CAOlB,gBAAiB,CACjB,kBAAmB,CAJnB,UAWJ,CAEA,kCAEI,SACJ,CAEA,8BAGI,wOAAyO,CADzO,UAEJ,CAEA,8BAGI,wOAAyO,CADzO,WAEJ,CAEA,8CAEI,cAAe,CACf,UACJ,CAEA,aACI,oBACJ,CAEA,YAII,QAAS,CAFT,wBAAyB,CACzB,gBAAiB,CAFjB,UAIJ,CAEA,8BAGI,SAAU,CADV,yBAEJ,CAEA,eACI,UAAW,CAGX,eAAiB,CADjB,gBAGJ,CAEA,4BANI,cAAe,CAGf,iBAmBJ,CAhBA,aAcI,kBAAmB,CARnB,QAAS,CAHT,qBAAsB,CACtB,0BAA2B,CAM3B,UAAW,CATX,cAAe,CACf,aAAc,CAad,WAAe,CAHf,gBAAiB,CALjB,QAAS,CAFT,YAAa,CAIb,WAAY,CADZ,UAQJ,CAEA,WAEI,UAAW,CADX,cAEJ,CAEA,uBACI,UAAc,CACd,eACJ,CAEA,kDAII,eAAmB,CAEnB,iBAAkB,CADlB,kCAAmC,CAHnC,UAAW,CACX,eAIJ,CAEA,wBACI,kBAAmB,CACnB,kCACJ,CAEA,mDAEI,kBACJ,CAEA,4BAEI,kBAAmB,CAEnB,iBAAkB,CADlB,eAAgB,CAFhB,UAIJ,CAEA,0BAEI,eAAmB,CAEnB,iBAAkB,CADlB,eAAgB,CAFhB,UAIJ,CAEA,0BAGI,UAAW,CADX,cAAe,CAEf,UAAW,CAHX,mBAIJ,CAEA,uCACI,UAAW,CACX,UACJ,CAEA,uBAEI,cAAe,CADf,mBAEJ,CAEA,gEAGI,kBAAmB,CAEnB,iBAAkB,CADlB,eAAgB,CAFhB,UAIJ,CAGA,iBACI,kBAAmB,CACnB,WACJ,CCxwEA,qBAUE,kBAAmB,CAJnB,0BAAqC,CADrC,QAAS,CAGT,YAAa,CACb,sBAAuB,CANvB,MAAO,CASP,cAAe,CARf,OAAQ,CAFR,KAAM,CAKN,WAMF,CASA,qBACE,qBAAsB,CAGtB,iBAAkB,CAClB,+BAA0C,CAC1C,YAAa,CACb,qBAAsB,CAJtB,UAAW,CAKX,eAAgB,CANhB,SAOF,CAEA,oBAGE,kBAAmB,CAEnB,+BAAgC,CAJhC,YAAa,CACb,6BAA8B,CAE9B,iBAEF,CAEA,uBAEE,cAAe,CADf,QAEF,CAEA,cACE,eAAgB,CAChB,WAAY,CAGZ,UAAW,CADX,cAAe,CADf,cAAe,CAGf,oBACF,CAEA,oBACE,UACF,CAEA,gBACE,YAAa,CACb,OACF,CAEA,kBACE,QAAO,CAGP,wBAAyB,CAFzB,aAAc,CACd,YAEF,CAGA,2BAGE,YAAa,CACb,qBAAsB,CAFtB,WAAY,CADZ,UAIF,CC7EA,mBACE,iBAAkB,CAClB,0BACF,CACA,iBACE,uBAA0B,CAC1B,yCACF,CACA,qBACE,mBAAoB,CACpB,wBAAiB,CAAjB,gBACF,CACA,+BACE,0CACF,CACA,0BACE,eAAgB,CAEhB,wBAA0B,CAD1B,SAEF,CAEA,0CACE,eAAgB,CAEhB,qBAAsB,CADtB,SAEF,CAEA,0BACE,iBACF,CAEA,wCACE,cAAe,CACf,UAAY,CACZ,uBAA0B,CAE1B,wBAAyB,CAGzB,mBAAoB,CACpB,gBAAiB,CALjB,SAMF,CAEA,6DACE,eACF,CAEA,yCAGE,WAAY,CAFZ,iBAAkB,CAClB,UAEF,CAEA,+CAQE,6BAA2C,CAD3C,4BAA0C,CAH1C,UAAW,CAHX,UAAW,CAKX,UAAW,CAJX,iBAAkB,CAClB,SAAU,CAEV,SAIF,CAEA,8CACE,YACF,CAEA,mEACE,QAAS,CAET,gBAAiB,CADjB,MAAO,CAEP,uBACF,CACA,mEACE,QAAS,CAET,gBAAiB,CADjB,OAEF,CACA,mEAGE,gBAAiB,CADjB,MAAO,CADP,KAAM,CAGN,wBACF,CACA,mEAGE,gBAAiB,CADjB,OAAQ,CADR,KAAM,CAGN,wBACF,CACA,oIAIE,gBAAiB,CADjB,gBAAiB,CADjB,OAGF,CACA,kEACE,MAAO,CACP,wBACF,CACA,kEACE,OAAQ,CACR,wBACF,CACA,oIAIE,gBAAiB,CAFjB,QAAS,CACT,iBAEF,CACA,kEACE,KAAM,CACN,wBACF,CACA,kEACE,QAAS,CACT,uBACF,CCtHA,iBACE,iBACF,CACA,wBAOE,wPAAuY,CAFvY,6BAA8B,CAG9B,6BAAiC,CAJjC,2BAA4B,CAE5B,qBAAsB,CAHtB,WAAY,CAMZ,mBAAoB,CARpB,iBAAkB,CAClB,UAQF,CACA,2BACE,QAAS,CAET,gBAAiB,CADjB,MAAO,CAEP,uBACF,CACA,2BACE,QAAS,CAET,gBAAiB,CADjB,OAEF,CACA,2BAGE,gBAAiB,CADjB,MAAO,CADP,KAAM,CAGN,wBACF,CACA,2BAGE,gBAAiB,CADjB,OAAQ,CADR,KAAM,CAGN,wBACF,CACA,oDAIE,gBAAiB,CADjB,gBAAiB,CADjB,OAGF,CACA,0BACE,MAAO,CACP,wBACF,CACA,0BACE,OAAQ,CACR,wBACF,CACA,oDAIE,gBAAiB,CAFjB,QAAS,CACT,iBAEF,CACA,0BACE,KAAM,CACN,wBACF,CACA,0BACE,QAAS,CACT,uBACF,CC7DA,0BAGE,mBAAoB,CADpB,wBAA0B,CAD1B,sBAGF,CAGA,iBACE,sBACF,CAQA,gCAJE,iBAOF,CAHA,YAEE,gBACF,CAOA,sCAJE,uBAWF,CAPA,eACE,eAAiB,CACjB,wBAAyB,CACzB,iBAAkB,CAClB,8BAAyC,CAEzC,eACF,CAEA,qBAEE,oBAAqB,CADrB,+BAEF,CAGA,sBAKE,kBAAmB,CAJnB,iDAA4D,CAC5D,+BAAgC,CAEhC,YAAa,CAEb,6BAA8B,CAC9B,eAAgB,CAJhB,iBAKF,CAEA,iBACE,WAAY,CACZ,QAAO,CACP,wBAAiB,CAAjB,gBACF,CAEA,uBACE,aACF,CAEA,wBAGE,kBAAmB,CAFnB,YAAa,CACb,OAEF,CAEA,iCAME,kBAAmB,CALnB,WAAY,CACZ,eAAgB,CAGhB,YAAa,CADb,WAAY,CAGZ,sBAAuB,CAJvB,eAKF,CAEA,uCACE,0BAAyC,CACzC,aACF,CAGA,uBAEE,wBAAyB,CACzB,aAAc,CAFd,YAGF,CAGA,2CAEE,4BAA8B,CAD9B,2BAEF,CAEA,gDACE,sBACF,CAKA,gBAME,sBAA0C,CAC1C,sBAAuB,CACvB,iBAAkB,CAElB,UAAW,CACX,cAAe,CARf,QAAS,CAET,YAAa,CAJb,iBAAkB,CAQlB,iBAAkB,CAPlB,OAAQ,CAER,8BAAgC,CAQhC,UACF,CAGA,mBAEE,YAAa,CACb,qBAAsB,CAFtB,WAGF,CAEA,0BAEE,eAAiB,CADjB,+BAEF,CAEA,uBACE,kBAAmB,CAEnB,+BAAgC,CADhC,iBAEF,CAEA,sCAIE,aAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,kBAIF,CAEA,wBACE,iBACF,CAEA,eAEE,oBAAqB,CADrB,YAAa,CAEb,OAAQ,CACR,kBACF,CAEA,iBACE,aAAc,CACd,cAAe,CAEf,eAAgB,CADhB,kBAEF,CAEA,gBAGE,4BAA6B,CAF7B,eAAgB,CAChB,gBAEF,CAEA,gCACE,kBACF,CAEA,4CAEE,aAAc,CACd,cAAe,CAFf,eAGF,CAEA,0DAEE,iBACF,CAEA,kCACE,kBAAmB,CACnB,oBAAqB,CACrB,iBAAkB,CAClB,eACF,CAGA,sBAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAGtB,WAAY,CADZ,sBAAuB,CAGvB,iBAAkB,CADlB,iBAEF,CAEA,+BACE,kBAAmB,CACnB,UACF,CAGA,yBACE,sBAEE,eAAgB,CADhB,gBAEF,CAEA,uBAEE,wBAAyB,CADzB,YAEF,CAEA,iCACE,eACF,CACF,CAGA,wBAGE,eAAiB,CAFjB,YAAa,CACb,WAEF,CAEA,+CACE,yBAA0B,CAC1B,YACF,CAGA,qBAQE,kBAAmB,CAFnB,gBAAoC,CADpC,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAQN,UACF,CAKA,0CACE,SACF,CAEA,gDACE,kBAAmB,CACnB,iBACF,CAEA,gDACE,kBAAmB,CACnB,iBACF,CAEA,sDACE,kBACF,CAGA,mBAGE,YAAa,CACb,qBAAsB,CAFtB,WAAY,CADZ,iBAIF,CAEA,6BACE,WACF,CAGA,mCACE,yBACF,CACA,yCACE,4BAA8B,CAC9B,yBACF,CACA,0CAEE,yBAA2B,CAD3B,wBAEF,CACA,gDACE,sBAAwB,CACxB,0BACF,CAGA,qCAEE,eAAgB,CADhB,2HAEF,CACA,+CACE,sBAAwB,CACxB,SAAU,CAEV,mBAAoB,CADpB,2BAEF,CACA,8CACE,2BAA6B,CAC7B,SAAU,CAEV,mBAAoB,CADpB,uBAEF,CAGA,aACE,eACF,CAEA,kBAEE,aAAc,CADd,iBAEF,CAEA,sBAEE,kBAAmB,CADnB,4BAEF,CAOA,kGACE,iBACF,CAEA,8CACE,iBAAkB,CAClB,eACF,CAEA,sDACE,8BACF,CAEA,4DACE,8BACF,CAGA,oDACE,iBACF,CAEA,gDACE,WACF,CAGA,yBACE,aACE,qBACF,CAEA,kBACE,oBAAsB,CACtB,gBACF,CAEA,sBACE,oBAAsB,CACtB,gBACF,CAEA,mCACE,gBACF,CAEA,mDACE,cACF,CAEA,4CACE,cAAe,CACf,eACF,CAMA,0FACE,YACF,CACF,CAEA,yBACE,kBAEE,WACF,CAEA,wCAJE,oBAMF,CAEA,qDACE,WACF,CAEA,yDACE,qBAAsB,CACtB,OACF,CAEA,6DACE,cACF,CACF,CAGA,2DAGE,0CACF,CAGA,0CAEE,+BAA8C,CAD9C,oBAEF,CAGA,gDACE,yBAA0B,CAC1B,kBACF,CAGA,uCACE,gBACF,CAEA,sCACE,gBACF", "sources": ["../node_modules/handsontable/dist/handsontable.full.min.css", "index.css", "App.css", "../node_modules/reactflow/dist/style.css", "styles/flow.css", "../node_modules/react-querybuilder/dist/query-builder-layout.scss", "../node_modules/react-querybuilder/dist/query-builder.scss", "components/tables/BlendsPredictionTable.css", "components/Dashboard/AnomlyDetectionDemo.css", "components/Dashboard/CarbonDemoPrediction.css", "components/Dashboard/DataExploration/ExplorationStyles.css", "../node_modules/handsontable/dist/handsontable.full.css", "components/Dashboard/DataExploration/FullScreenModal.css", "../node_modules/react-grid-layout/css/styles.css", "../node_modules/react-resizable/css/styles.css", "components/Dashboard/DataExploration/PLCExploration/styles/PLCStyles.css"], "sourcesContent": ["@charset \"UTF-8\";\n /*!\n * Copyright (c) HANDSONCODE sp. z o. o.\n *\n * HANDSONTABLE is a software distributed by HANDSONCODE sp. z o. o., a Polish corporation based in\n * Gdynia, Poland, at Aleja Zwyciestwa 96-98, registered by the District Court in Gdansk under number\n * 538651, EU tax ID number: PL5862294002, share capital: PLN 62,800.00.\n *\n * This software is protected by applicable copyright laws, including international treaties, and dual-\n * licensed - depending on whether your use for commercial purposes, meaning intended for or\n * resulting in commercial advantage or monetary compensation, or not.\n *\n * If your use is strictly personal or solely for evaluation purposes, meaning for the purposes of testing\n * the suitability, performance, and usefulness of this software outside the production environment,\n * you agree to be bound by the terms included in the \"handsontable-non-commercial-license.pdf\" file.\n *\n * Your use of this software for commercial purposes is subject to the terms included in an applicable\n * license agreement.\n *\n * In any case, you must not make any such use of this software as to develop software which may be\n * considered competitive with this software.\n *\n * UNLESS EXPRESSLY AGREED OTHERWISE, <PERSON>AN<PERSON><PERSON><PERSON><PERSON> PROVIDES THIS SOFTWARE ON AN \"AS IS\"\n * BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, AND IN NO EVENT AND UNDER NO\n * LEGAL THEORY, SHALL HANDSONCODE BE LIABLE TO YOU FOR DAMAGES, INCLUDING ANY DIRECT,\n * INDIRECT, SPECIAL, INCIDENTAL, OR CONSEQUENTIAL DAMAGES OF ANY CHARACTER ARISING FROM\n * USE OR INABILITY TO USE THIS SOFTWARE.\n *\n * Version: 15.0.0\n * Release date: 16/12/2024 (built at 16/12/2024 13:20:03)\n */.handsontable .table td,.handsontable .table th{border-top:none}.handsontable tr{background:#fff}.handsontable td{background-color:inherit}.handsontable .table caption+thead tr:first-child td,.handsontable .table caption+thead tr:first-child th,.handsontable .table colgroup+thead tr:first-child td,.handsontable .table colgroup+thead tr:first-child th,.handsontable .table thead:first-child tr:first-child td,.handsontable .table thead:first-child tr:first-child th{border-top:1px solid #ccc}.handsontable .table-bordered{border:0;border-collapse:separate}.handsontable .table-bordered td,.handsontable .table-bordered th{border-left:none}.handsontable .table-bordered td:first-child,.handsontable .table-bordered th:first-child{border-left:1px solid #ccc}.handsontable .table>tbody>tr>td,.handsontable .table>tbody>tr>th,.handsontable .table>tfoot>tr>td,.handsontable .table>tfoot>tr>th,.handsontable .table>thead>tr>td,.handsontable .table>thead>tr>th{line-height:21px;padding:0}.col-lg-1.handsontable,.col-lg-10.handsontable,.col-lg-11.handsontable,.col-lg-12.handsontable,.col-lg-2.handsontable,.col-lg-3.handsontable,.col-lg-4.handsontable,.col-lg-5.handsontable,.col-lg-6.handsontable,.col-lg-7.handsontable,.col-lg-8.handsontable,.col-lg-9.handsontable,.col-md-1.handsontable,.col-md-10.handsontable,.col-md-11.handsontable,.col-md-12.handsontable,.col-md-2.handsontable,.col-md-3.handsontable,.col-md-4.handsontable,.col-md-5.handsontable,.col-md-6.handsontable,.col-md-7.handsontable,.col-md-8.handsontable,.col-md-9.handsontable .col-sm-1.handsontable,.col-sm-10.handsontable,.col-sm-11.handsontable,.col-sm-12.handsontable,.col-sm-2.handsontable,.col-sm-3.handsontable,.col-sm-4.handsontable,.col-sm-5.handsontable,.col-sm-6.handsontable,.col-sm-7.handsontable,.col-sm-8.handsontable,.col-sm-9.handsontable .col-xs-1.handsontable,.col-xs-10.handsontable,.col-xs-11.handsontable,.col-xs-12.handsontable,.col-xs-2.handsontable,.col-xs-3.handsontable,.col-xs-4.handsontable,.col-xs-5.handsontable,.col-xs-6.handsontable,.col-xs-7.handsontable,.col-xs-8.handsontable,.col-xs-9.handsontable{padding-left:0;padding-right:0}.handsontable .table-striped>tbody>tr:nth-of-type(2n){background-color:#fff}.handsontable .hide{display:none}.handsontable .relative{position:relative}.handsontable .wtHider{position:relative;width:0}.handsontable .wtSpreader{height:auto;position:relative;width:0}.handsontable div,.handsontable input,.handsontable table,.handsontable tbody,.handsontable td,.handsontable textarea,.handsontable th,.handsontable thead{box-sizing:content-box;-webkit-box-sizing:content-box;-moz-box-sizing:content-box}.handsontable input,.handsontable textarea{min-height:auto}.handsontable table.htCore{border-collapse:separate;border-spacing:0;border-width:0;cursor:default;margin:0;max-height:none;max-width:none;outline-width:0;table-layout:fixed;width:0}.handsontable col,.handsontable col.rowHeader{width:50px}.handsontable td,.handsontable th{background-color:#fff;border-bottom:1px solid #ccc;border-left-width:0;border-right:1px solid #ccc;border-top-width:0;empty-cells:show;height:22px;line-height:21px;outline:none;outline-width:0;overflow:hidden;padding:0 4px;vertical-align:top;white-space:pre-wrap}[dir=rtl].handsontable td,[dir=rtl].handsontable th{border-left:1px solid #ccc;border-right-width:0}.handsontable th:last-child{border-bottom:1px solid #ccc;border-left:none;border-right:1px solid #ccc}[dir=rtl].handsontable th:last-child{border-left:1px solid #ccc;border-right:none}.handsontable td:first-of-type,.handsontable th:first-child{border-left:1px solid #ccc}[dir=rtl].handsontable td:first-of-type,[dir=rtl].handsontable th:first-child{border-right:1px solid #ccc}.handsontable .ht_clone_top th:nth-child(2){border-left-width:0;border-right:1px solid #ccc}[dir=rtl].handsontable .ht_clone_top th:nth-child(2){border-left:1px solid #ccc;border-right-width:0}.handsontable.htRowHeaders thead tr th:nth-child(2){border-left:1px solid #ccc}[dir=rtl].handsontable.htRowHeaders thead tr th:nth-child(2){border-right:1px solid #ccc}.handsontable tr:first-child td,.handsontable tr:first-child th{border-top:1px solid #ccc}.ht_master:not(.innerBorderInlineStart):not(.emptyColumns) tbody tr th,.ht_master:not(.innerBorderInlineStart):not(.emptyColumns) thead tr th:first-child,.ht_master:not(.innerBorderInlineStart):not(.emptyColumns)~.handsontable:not(.htGhostTable) tbody tr th,.ht_master:not(.innerBorderInlineStart):not(.emptyColumns)~.handsontable:not(.ht_clone_top):not(.htGhostTable) thead tr th:first-child{border-left:1px solid #ccc;border-right-width:0}[dir=rtl].ht_master:not(.innerBorderInlineStart):not(.emptyColumns) tbody tr th,[dir=rtl].ht_master:not(.innerBorderInlineStart):not(.emptyColumns) thead tr th:first-child,[dir=rtl].ht_master:not(.innerBorderInlineStart):not(.emptyColumns)~.handsontable:not(.htGhostTable) tbody tr th,[dir=rtl].ht_master:not(.innerBorderInlineStart):not(.emptyColumns)~.handsontable:not(.ht_clone_top):not(.htGhostTable) thead tr th:first-child{border-left-width:0;border-right:1px solid #ccc}.ht_master:not(.innerBorderTop):not(.innerBorderBottom) thead tr.lastChild th,.ht_master:not(.innerBorderTop):not(.innerBorderBottom) thead tr:last-child th,.ht_master:not(.innerBorderTop):not(.innerBorderBottom)~.handsontable thead tr.lastChild th,.ht_master:not(.innerBorderTop):not(.innerBorderBottom)~.handsontable thead tr:last-child th{border-bottom-width:0}.handsontable th{background-color:#f0f0f0;color:#222;font-weight:400;text-align:center;white-space:nowrap}.handsontable thead th{padding:0}.handsontable th.active{background-color:#ccc}.handsontable thead th .relative{padding:2px 4px}.handsontable span.colHeader{display:inline-block;line-height:1.1}.handsontable .wtBorder{font-size:0;position:absolute}.handsontable .wtBorder.hidden{display:none!important}.handsontable .wtBorder.current{z-index:10}.handsontable .wtBorder.area{z-index:8}.handsontable .wtBorder.fill{z-index:6}.handsontable .wtBorder.corner{cursor:crosshair;font-size:0}.ht_clone_master{z-index:100}.ht_clone_inline_start{z-index:120}.ht_clone_bottom{z-index:130}.ht_clone_bottom_inline_start_corner{z-index:150}.ht_clone_top{z-index:160}.ht_clone_top_inline_start_corner{z-index:180}.handsontable col.hidden{width:0!important}.handsontable tr.hidden,.handsontable tr.hidden td,.handsontable tr.hidden th{display:none}.ht_clone_bottom,.ht_clone_inline_start,.ht_clone_top,.ht_master{overflow:hidden}.ht_master .wtHolder{overflow:auto}.handsontable .ht_clone_inline_start table.htCore>thead,.handsontable .ht_master table.htCore>tbody>tr>th,.handsontable .ht_master table.htCore>thead{visibility:hidden}.ht_clone_bottom .wtHolder,.ht_clone_inline_start .wtHolder,.ht_clone_top .wtHolder{overflow:hidden}.handsontable{color:#373737;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Helvetica Neue,Arial,sans-serif;font-size:13px;font-weight:400;position:relative;touch-action:manipulation}.handsontable a{color:#104acc}.handsontable.htAutoSize{left:-99000px;position:absolute;top:-99000px;visibility:hidden}.handsontable td.htInvalid{background-color:#ffbeba!important}.handsontable td.htNoWrap{white-space:nowrap}.handsontable td.invisibleSelection,.handsontable th.invisibleSelection{outline:none}.handsontable td.invisibleSelection::selection,.handsontable th.invisibleSelection::selection{background:hsla(0,0%,100%,0)}.hot-display-license-info{color:#373737;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Helvetica Neue,Arial,sans-serif;font-size:10px;font-weight:400;padding:5px 0 3px;text-align:left}.hot-display-license-info a{color:#104acc;font-size:10px}.handsontable .htFocusCatcher{border:0;height:0;margin:0;opacity:0;padding:0;position:absolute;width:0;z-index:-1}.handsontable .manualColumnResizer{cursor:col-resize;height:25px;position:absolute;top:0;width:5px;z-index:210}.handsontable .manualRowResizer{cursor:row-resize;height:5px;left:0;position:absolute;width:50px;z-index:210}.handsontable .manualColumnResizer.active,.handsontable .manualColumnResizer:hover,.handsontable .manualRowResizer.active,.handsontable .manualRowResizer:hover{background-color:#34a9db}.handsontable .manualColumnResizerGuide{background-color:#34a9db;border-left:none;border-right:1px dashed #777;display:none;margin-left:5px;margin-right:unset;position:absolute;right:unset;top:0;width:0}[dir=rtl].handsontable .manualColumnResizerGuide{border-left:1px dashed #777;border-right:none;left:unset;margin-left:unset;margin-right:5px}.handsontable .manualRowResizerGuide{background-color:#34a9db;border-bottom:1px dashed #777;bottom:0;display:none;height:0;left:0;margin-top:5px;position:absolute}.handsontable .manualColumnResizerGuide.active,.handsontable .manualRowResizerGuide.active{display:block;z-index:209}.handsontable td.area,.handsontable td.area-1,.handsontable td.area-2,.handsontable td.area-3,.handsontable td.area-4,.handsontable td.area-5,.handsontable td.area-6,.handsontable td.area-7{position:relative}.handsontable td.area-1:before,.handsontable td.area-2:before,.handsontable td.area-3:before,.handsontable td.area-4:before,.handsontable td.area-5:before,.handsontable td.area-6:before,.handsontable td.area-7:before,.handsontable td.area:before{background:#005eff;bottom:0;content:\"\";left:0;position:absolute;right:0;top:0}.handsontable td.area:before{opacity:.1}.handsontable td.area-1:before{opacity:.2}.handsontable td.area-2:before{opacity:.27}.handsontable td.area-3:before{opacity:.35}.handsontable td.area-4:before{opacity:.41}.handsontable td.area-5:before{opacity:.47}.handsontable td.area-6:before{opacity:.54}.handsontable td.area-7:before{opacity:.58}.handsontable tbody th.current,.handsontable thead th.current{box-shadow:inset 0 0 0 2px #4b89ff}.handsontable tbody th.ht__highlight,.handsontable thead th.ht__highlight{background-color:#dcdcdc}.handsontable tbody th.ht__active_highlight,.handsontable thead th.ht__active_highlight{background-color:#8eb0e7;color:#000}.handsontableInput{background-color:#fff;border:none;border-radius:0;box-shadow:inset 0 0 0 2px #5292f7;box-sizing:border-box!important;color:#000;display:block;font-family:inherit;font-size:inherit;line-height:21px;margin:0;outline-width:0;padding:1px 5px 0;resize:none}.handsontableInput:focus{outline:none}.handsontableInputHolder{left:0;position:absolute;top:0}.htSelectEditor{position:absolute;select{-webkit-appearance:menulist-button!important;border:2px solid #4b89ff;box-sizing:border-box!important;height:100%;width:100%}}.htSelectEditor select:focus{outline:none}.htSelectEditor .htAutocompleteArrow{display:none}.handsontable .htDimmed{color:#777}.handsontable .htSubmenu{position:relative}.handsontable .htSubmenu :after{color:#777;content:\"▶\";font-size:9px;position:absolute;right:5px}[dir=rtl].handsontable .htSubmenu :after{content:\"\"}[dir=rtl].handsontable .htSubmenu :before{color:#777;content:\"◀\";font-size:9px;left:5px;position:absolute}.handsontable .htLeft{text-align:left}.handsontable .htCenter{text-align:center}.handsontable .htRight{text-align:right}.handsontable .htJustify{text-align:justify}.handsontable .htTop{vertical-align:top}.handsontable .htMiddle{vertical-align:middle}.handsontable .htBottom{vertical-align:bottom}.handsontable .htPlaceholder{color:#999}.handsontable.listbox{margin:0}.handsontable.listbox .ht_master table{background:#fff;border:1px solid #ccc;border-collapse:separate}.handsontable.listbox td,.handsontable.listbox th,.handsontable.listbox tr:first-child td,.handsontable.listbox tr:first-child th,.handsontable.listbox tr:last-child th{border-color:transparent!important}.handsontable.listbox td,.handsontable.listbox th{text-overflow:ellipsis;white-space:nowrap}.handsontable.listbox td.htDimmed{color:inherit;cursor:default;font-style:inherit}.handsontable.listbox .wtBorder{visibility:hidden}.handsontable.listbox tr td.current,.handsontable.listbox tr:hover td{background:#eee}.ht_editor_hidden{z-index:-1}.ht_editor_visible{z-index:200}.handsontable td.htSearchResult{background:#fcedd9;color:#583707}.handsontable.mobile,.handsontable.mobile .wtHolder{-webkit-touch-callout:none;-webkit-user-select:none;-khtml-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-overflow-scrolling:touch}.handsontable.mobile .handsontableInput:focus{-webkit-appearance:none;-webkit-box-shadow:inset 0 0 0 2px #5292f7;-moz-box-shadow:inset 0 0 0 2px #5292f7;box-shadow:inset 0 0 0 2px #5292f7}.handsontable .bottomSelectionHandle,.handsontable .bottomSelectionHandle-HitArea,.handsontable .topSelectionHandle,.handsontable .topSelectionHandle-HitArea{left:-10000px;right:unset;top:-10000px;z-index:9999}[dir=rtl].handsontable .bottomSelectionHandle,[dir=rtl].handsontable .bottomSelectionHandle-HitArea,[dir=rtl].handsontable .topSelectionHandle,[dir=rtl].handsontable .topSelectionHandle-HitArea{left:unset;right:-10000px}.handsontable.hide-tween{-webkit-animation:opacity-hide .3s;animation:opacity-hide .3s;animation-fill-mode:forwards;-webkit-animation-fill-mode:forwards}.handsontable.show-tween{-webkit-animation:opacity-show .3s;animation:opacity-show .3s;animation-fill-mode:forwards;-webkit-animation-fill-mode:forwards}.handsontable .htAutocompleteArrow{color:#bbb;cursor:default;float:right;font-size:10px;text-align:center;width:16px}[dir=rtl].handsontable .htAutocompleteArrow{float:left}.handsontable td.htInvalid .htAutocompleteArrow{color:#555}.handsontable td.htInvalid .htAutocompleteArrow:hover{color:#1a1a1a}.handsontable td .htAutocompleteArrow:hover{color:#777}.handsontable td.area .htAutocompleteArrow{color:#d3d3d3}.handsontable .htCheckboxRendererInput.noValue{opacity:.5}.handsontable .htCheckboxRendererLabel{cursor:pointer;display:inline-block;font-size:inherit;vertical-align:middle}.handsontable .htCheckboxRendererLabel.fullWidth{width:100%}.handsontable .collapsibleIndicator{background:#eee;border:1px solid #a6a6a6;border-radius:10px;-webkit-box-shadow:0 0 0 6px #eee;-moz-box-shadow:0 0 0 6px #eee;box-shadow:0 0 0 3px #eee;color:#222;cursor:pointer;font-size:10px;height:10px;left:unset;line-height:8px;position:absolute;right:5px;text-align:center;top:50%;transform:translateY(-50%);width:10px}[dir=rtl].handsontable .collapsibleIndicator{left:5px;right:unset}.handsontable[dir=ltr] thead th:has(.collapsibleIndicator) div.htRight span.colHeader{margin-right:20px}.handsontable[dir=rtl] thead th:has(.collapsibleIndicator) div.htLeft span.colHeader{margin-left:20px}.handsontable .columnSorting{position:relative}.handsontable[dir=ltr] div.htRight span[class*=ascending],.handsontable[dir=ltr] div.htRight span[class*=descending]{margin-left:-10px;margin-right:10px}.handsontable[dir=rtl] div.htLeft span[class*=ascending],.handsontable[dir=rtl] div.htLeft span[class*=descending]{margin-left:10px;margin-right:-10px}.handsontable[dir=ltr] div.htRight span[class*=ascending]:only-child,.handsontable[dir=ltr] div.htRight span[class*=descending]:only-child{margin-left:-15px;margin-right:15px}.handsontable[dir=rtl] div.htLeft span[class*=ascending]:only-child,.handsontable[dir=rtl] div.htLeft span[class*=descending]:only-child{margin-left:15px;margin-right:-15px}.handsontable .columnSorting.sortAction:hover{cursor:pointer;text-decoration:underline}.handsontable span.colHeader.columnSorting:before{background-position-x:right;background-repeat:no-repeat;background-size:contain;content:\"\";height:10px;left:unset;margin-top:-6px;padding-left:8px;padding-right:0;position:absolute;right:-9px;top:50%;width:5px}[dir=rtl].handsontable span.colHeader.columnSorting:before{background-position-x:left;left:-9px;padding-left:0;padding-right:8px;right:unset}.handsontable span.colHeader.columnSorting.ascending:before{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAoCAMAAADJ7yrpAAAAKlBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKE86IAAAADXRSTlMABBEmRGprlJW72e77tTkTKwAAAFNJREFUeAHtzjkSgCAUBNHPgsoy97+ulGXRqJE5L+xkxoYt2UdsLb5bqFINz+aLuuLn5rIu2RkO3fZpWENimNgiw6iBYRTPMLJjGFxQZ1hxxb/xBI1qC8k39CdKAAAAAElFTkSuQmCC)}.handsontable span.colHeader.columnSorting.descending:before{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAoCAMAAADJ7yrpAAAAKlBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKE86IAAAADXRSTlMABBEmRGprlJW72e77tTkTKwAAAFJJREFUeAHtzjkSgCAQRNFmQYUZ7n9dKUvru0TmvPAn3br0QfgdZ5xx6x+rQn23GqTYnq1FDcnuzZIO2WmedVqIRVxgGKEyjNgYRjKGkZ1hFIZ3I70LyM0VtU8AAAAASUVORK5CYII=)}.htGhostTable .htCore span.colHeader.columnSorting:not(.indicatorDisabled):before{content:\"*\";display:inline-block;padding-right:20px;position:relative}.handsontable.htGhostTable table thead th{border-bottom-width:0}.handsontable.htGhostTable table tbody tr td,.handsontable.htGhostTable table tbody tr th{border-top-width:0}.handsontable .htCommentCell{position:relative}.handsontable .htCommentCell:after{border-left:6px solid transparent;border-right:none;border-top:6px solid #000;content:\"\";left:unset;position:absolute;right:0;top:0}[dir=rtl].handsontable .htCommentCell:after{border-left:none;border-right:6px solid transparent;left:0;right:unset}.htCommentsContainer .htComments{display:none;position:absolute;z-index:1059}.htCommentsContainer .htCommentTextArea{-webkit-appearance:none;background-color:#fff;border:none;border-left:3px solid #ccc;box-shadow:0 1px 3px rgba(0,0,0,.118),0 1px 2px rgba(0,0,0,.239);-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;font-size:12px;height:90px;outline:0!important;padding:5px;width:215px}[dir=rtl].htCommentsContainer .htCommentTextArea{border-left:none;border-right:3px solid #ccc}.htCommentsContainer .htCommentTextArea:focus{border-left:3px solid #5292f7;border-right:none;box-shadow:0 1px 3px rgba(0,0,0,.118),0 1px 2px rgba(0,0,0,.239),inset 0 0 0 1px #5292f7}[dir=rtl].htCommentsContainer .htCommentTextArea:focus{border-left:none;border-right:3px solid #5292f7}\n/*!\n * Handsontable ContextMenu\n */.htContextMenu:not(.htGhostTable){display:none;position:absolute;z-index:1060}.htContextMenu .ht_clone_bottom,.htContextMenu .ht_clone_bottom_inline_start_corner,.htContextMenu .ht_clone_inline_start,.htContextMenu .ht_clone_top,.htContextMenu .ht_clone_top_inline_start_corner{display:none}.htContextMenu .ht_master table.htCore{border-color:#ccc;border-style:solid;border-width:1px 2px 2px 1px}[dir=rtl].htContextMenu .ht_master table.htCore{border-left-width:2px;border-right-width:1px}.htContextMenu.handsontable:focus{outline:none}.htContextMenu .wtBorder{visibility:hidden}.htContextMenu table tbody tr td{background:#fff;border-width:0;cursor:pointer;overflow:hidden;padding:4px 6px 0;text-overflow:ellipsis;white-space:nowrap}.htContextMenu table tbody tr td:first-child{border-width:0}[dir=rtl].htContextMenu table tbody tr td:first-child{border-left-width:0;border-right-width:0}.htContextMenu table tbody tr td.htDimmed{color:#323232;font-style:normal}.htContextMenu table tbody tr td.current{background:#f3f3f3}.htContextMenu table tbody tr td.htSeparator{border-top:1px solid #e6e6e6;cursor:default;height:0;padding:0}.htContextMenu table tbody tr td.htDisabled{color:#999;cursor:default}.htContextMenu table tbody tr td.htDisabled:hover{background:#fff;color:#999;cursor:default}.htContextMenu table tbody tr.htHidden{display:none}.htContextMenu table tbody tr td .htItemWrapper{margin-left:10px;margin-right:6px}[dir=rtl].htContextMenu table tbody tr td .htItemWrapper{margin-left:6px;margin-right:10px}.htContextMenu table tbody tr td div span.selected{left:4px;margin-top:-2px;position:absolute;right:0}[dir=rtl].htContextMenu table tbody tr td div span.selected{left:0;right:4px}.htContextMenu .ht_master .wtHolder{overflow:hidden}textarea.HandsontableCopyPaste{opacity:0;outline:0 none!important;overflow:hidden;position:fixed!important;right:100%!important;top:0!important}\n\n/*!\n * Handsontable DropdownMenu\n */.handsontable .changeType{background:#eee;border:1px solid #bbb;border-radius:2px;color:#bbb;float:right;font-size:9px;line-height:9px;margin:3px 1px 0 5px;padding:2px}[dir=rtl].handsontable .changeType{float:left}.handsontable[dir=rtl] .changeType{margin:3px 5px 0 1px}.handsontable .changeType:before{content:\"▼ \"}.handsontable .changeType:hover{border:1px solid #777;color:#777;cursor:pointer}.htDropdownMenu:not(.htGhostTable){display:none;position:absolute;z-index:1060}.htDropdownMenu .ht_clone_bottom,.htDropdownMenu .ht_clone_bottom_inline_start_corner,.htDropdownMenu .ht_clone_inline_start,.htDropdownMenu .ht_clone_top,.htDropdownMenu .ht_clone_top_inline_start_corner{display:none}.htDropdownMenu table.htCore{border-color:#ccc;border-style:solid;border-width:1px 2px 2px 1px}[dir=rtl].htDropdownMenu table.htCore{border-left-width:2px;border-right-width:1px}.htDropdownMenu.handsontable:focus{outline:none}.htDropdownMenu .wtBorder{visibility:hidden}.htDropdownMenu table tbody tr td{background:#fff;border-width:0;cursor:pointer;overflow:hidden;padding:4px 6px 0;text-overflow:ellipsis;white-space:nowrap}.htDropdownMenu table tbody tr td:first-child{border-width:0}[dir=rtl].htDropdownMenu table tbody tr td:first-child{border-left-width:0;border-right-width:0}.htDropdownMenu table tbody tr td.htDimmed{color:#323232;font-style:normal}.htDropdownMenu table tbody tr td.current{background:#e9e9e9}.htDropdownMenu table tbody tr td.htSeparator{border-top:1px solid #e6e6e6;cursor:default;height:0;padding:0}.htDropdownMenu table tbody tr td.htDisabled{color:#999}.htDropdownMenu table tbody tr td.htDisabled:hover{background:#fff;color:#999;cursor:default}.htDropdownMenu:not(.htGhostTable) table tbody tr.htHidden{display:none}.htDropdownMenu table tbody tr td .htItemWrapper,[dir=rtl].htDropdownMenu table tbody tr td .htItemWrapper{margin-left:10px;margin-right:10px}.htDropdownMenu table tbody tr td div span.selected{left:4px;margin-top:-2px;position:absolute;right:0}[dir=rtl].htDropdownMenu table tbody tr td div span.selected{left:0;right:4px}.htDropdownMenu .ht_master .wtHolder{overflow:hidden}\n\n/*!\n * Handsontable Filters\n */.htFiltersConditionsMenu:not(.htGhostTable){display:none;position:absolute;z-index:1070}.htFiltersConditionsMenu .ht_clone_bottom,.htFiltersConditionsMenu .ht_clone_bottom_inline_start_corner,.htFiltersConditionsMenu .ht_clone_inline_start,.htFiltersConditionsMenu .ht_clone_top,.htFiltersConditionsMenu .ht_clone_top_inline_start_corner{display:none}.htFiltersConditionsMenu table.htCore{border-color:#bbb;border-style:solid;border-width:1px 2px 2px 1px}.htFiltersConditionsMenu .wtBorder{visibility:hidden}.htFiltersConditionsMenu table tbody tr td{background:#fff;border-width:0;cursor:pointer;overflow:hidden;padding:4px 6px 0;text-overflow:ellipsis;white-space:nowrap}.htFiltersConditionsMenu table tbody tr td:first-child{border-width:0}[dir=rtl].htFiltersConditionsMenu table tbody tr td:first-child{border-left-width:0;border-right-width:0}.htFiltersConditionsMenu table tbody tr td.htDimmed{color:#323232;font-style:normal}.htFiltersConditionsMenu table tbody tr td.current{background:#e9e9e9}.htFiltersConditionsMenu table tbody tr td.htSeparator{border-top:1px solid #e6e6e6;height:0;padding:0}.htFiltersConditionsMenu table tbody tr td.htDisabled{color:#999}.htFiltersConditionsMenu table tbody tr td.htDisabled:hover{background:#fff;color:#999;cursor:default}.htFiltersConditionsMenu table tbody tr td .htItemWrapper{margin-left:10px;margin-right:10px}.htFiltersConditionsMenu table tbody tr td div span.selected{left:4px;margin-top:-2px;position:absolute}.htFiltersConditionsMenu .ht_master .wtHolder{overflow:hidden}.handsontable .htMenuFiltering{border-bottom:1px dotted #ccc;height:135px;overflow:hidden}.handsontable .ht_master table td.htCustomMenuRenderer{background-color:#fff;cursor:auto}.handsontable .htFiltersMenuLabel{font-size:.75em}.handsontable .htFiltersMenuActionBar{padding-bottom:3px;padding-top:10px;text-align:center}.handsontable .htFiltersMenuCondition.border{border-bottom:1px dotted #ccc!important}.handsontable .htFiltersMenuCondition .htUIInput{padding:0 0 5px}.handsontable .htFiltersMenuValue{border-bottom:1px dotted #ccc!important}.handsontable .htFiltersMenuValue .htUIMultipleSelectSearch{padding:0}.handsontable .htFiltersMenuCondition .htUIInput input,.handsontable .htFiltersMenuValue .htUIMultipleSelectSearch input{box-sizing:border-box;font-family:inherit;font-size:.75em;padding:4px;width:100%}.htUIMultipleSelect .ht_master .wtHolder{overflow-y:scroll}.handsontable .htFiltersActive .changeType{background-color:#d2e0d9;border:1px solid #509272;color:#18804e}.handsontable .htUISelectAll{margin-left:0;margin-right:10px}[dir=rtl].handsontable .htUISelectAll{margin-left:10px;margin-right:0}.handsontable .htUIClearAll,.handsontable .htUISelectAll{display:inline-block}.handsontable .htUIClearAll a,.handsontable .htUISelectAll a{font-size:.75em}.handsontable .htUISelectionControls{text-align:right}[dir=rtl].handsontable .htUISelectionControls{text-align:left}.handsontable .htCheckboxRendererInput{display:inline-block;height:1em;margin:0 5px 0 0;vertical-align:middle}[dir=rtl].handsontable .htCheckboxRendererInput{margin-left:5px;margin-right:0}.handsontable .htUIInput{padding:3px 0 7px;position:relative;text-align:center}.handsontable .htUIInput input{border:1px solid #d2d1d1;border-radius:2px}.handsontable .htUIInputIcon{position:absolute}.handsontable .htUIInput.htUIButton{cursor:pointer;display:inline-block}.handsontable .htUIInput.htUIButton input{background-color:#eee;color:#000;cursor:pointer;font-family:inherit;font-size:.75em;font-weight:700;height:19px;min-width:64px}.handsontable .htUIInput.htUIButton input:hover{border-color:#b9b9b9}.handsontable .htUIInput.htUIButtonOK{margin-left:0;margin-right:10px}[dir=rtl].handsontable .htUIInput.htUIButtonOK{margin-left:10px;margin-right:0}.handsontable .htUIInput.htUIButtonOK input{background-color:#0f9d58;border-color:#18804e;color:#fff}.handsontable .htUIInput.htUIButtonOK input:focus-visible{background-color:#92dd8d;border-color:#7cb878;color:#000}.handsontable .htUIInput.htUIButtonOK input:hover{border-color:#1a6f46}.handsontable .htUISelect{cursor:pointer;margin-bottom:7px;position:relative}.handsontable .htUISelectCaption{background-color:#e8e8e8;border:1px solid #d2d1d1;border-radius:2px;font-family:inherit;font-size:.75em;font-weight:700;overflow:hidden;padding:3px 20px 3px 10px;text-overflow:ellipsis;white-space:nowrap}.handsontable .htUISelectCaption:hover{background-color:#e8e8e8;border:1px solid #b9b9b9}.handsontable .htUISelectDropdown:after{content:\"▲\";font-size:7px;position:absolute;right:10px;top:0}.handsontable .htUISelectDropdown:before{content:\"▼\";font-size:7px;position:absolute;right:10px;top:8px}.handsontable .htUIMultipleSelect .handsontable .htCore{border:none}.handsontable .htUIMultipleSelect .handsontable .htCore td:hover{background-color:#f5f5f5}.handsontable .htUIMultipleSelectSearch input{border:1px solid #d2d1d1;border-radius:2px;padding:3px}.handsontable .htUIRadio{display:inline-block;height:100%;margin-left:0;margin-right:5px}[dir=rtl].handsontable .htUIRadio{margin-left:5px;margin-right:0}.handsontable .htUIRadio:last-child{margin-right:0}.handsontable .htUIRadio>input[type=radio]{margin-left:0;margin-right:.5ex}[dir=rtl].handsontable .htUIRadio>input[type=radio]{margin-left:.5ex;margin-right:0}.handsontable .htUIRadio label{vertical-align:middle}.handsontable .htFiltersMenuOperators{padding-bottom:5px}.handsontable th.beforeHiddenColumn{position:relative}.handsontable th.afterHiddenColumn:before,.handsontable th.beforeHiddenColumn:after{color:#bbb;font-size:5pt;position:absolute;top:50%;transform:translateY(-50%)}.handsontable th.afterHiddenColumn{position:relative}.handsontable[dir=ltr] th.afterHiddenColumn div.htLeft{margin-left:10px}.handsontable[dir=ltr] th.beforeHiddenColumn div.htRight,.handsontable[dir=rtl] th.afterHiddenColumn div.htRight{margin-right:10px}.handsontable[dir=rtl] th.beforeHiddenColumn div.htLeft{margin-left:10px}.handsontable th.beforeHiddenColumn:after{content:\"◀\";right:1px}[dir=rtl].handsontable th.beforeHiddenColumn:after{content:\"▶\";left:1px;right:auto}.handsontable th.afterHiddenColumn:before{content:\"▶\";left:1px}[dir=rtl].handsontable th.afterHiddenColumn:before{content:\"◀\";left:auto;right:1px}\n\n/*!\n * Handsontable HiddenRows\n */.handsontable th.afterHiddenRow:after,.handsontable th.beforeHiddenRow:before{color:#bbb;font-size:6pt;left:2px;line-height:6pt;position:absolute}.handsontable th.afterHiddenRow,.handsontable th.beforeHiddenRow{position:relative}.handsontable th.beforeHiddenRow:before{bottom:2px;content:\"▲\"}.handsontable th.afterHiddenRow:after{content:\"▼\";top:2px}.handsontable.ht__selection--rows tbody th.afterHiddenRow.ht__highlight:after,.handsontable.ht__selection--rows tbody th.beforeHiddenRow.ht__highlight:before{color:#eee}.handsontable td.afterHiddenRow.firstVisibleRow,.handsontable th.afterHiddenRow.firstVisibleRow{border-top:1px solid #ccc}.htRowHeaders .ht_master.innerBorderInlineStart~.ht_clone_inline_start td:first-of-type,.htRowHeaders .ht_master.innerBorderInlineStart~.ht_clone_top_inline_start_corner th:nth-child(2){border-left:0}.handsontable.ht__manualColumnMove.after-selection--columns thead th.ht__highlight{cursor:move;cursor:-moz-grab;cursor:-webkit-grab;cursor:grab}.handsontable.ht__manualColumnMove.on-moving--columns *,.handsontable.ht__manualColumnMove.on-moving--columns thead th.ht__highlight{cursor:move;cursor:-moz-grabbing;cursor:-webkit-grabbing;cursor:grabbing}.handsontable.ht__manualColumnMove.on-moving--columns .manualColumnResizer{display:none}.handsontable .ht__manualColumnMove--backlight,.handsontable .ht__manualColumnMove--guideline{display:none;height:100%;position:absolute}.handsontable .ht__manualColumnMove--guideline{background:#757575;margin-inline-end:0;margin-inline-start:-1px;top:0;width:2px;z-index:205}.handsontable .ht__manualColumnMove--backlight{background:#343434;background:rgba(52,52,52,.25);display:none;pointer-events:none;z-index:205}.handsontable.on-moving--columns .ht__manualColumnMove--backlight,.handsontable.on-moving--columns.show-ui .ht__manualColumnMove--guideline{display:block}.handsontable.ht__manualRowMove.after-selection--rows tbody th.ht__highlight{cursor:move;cursor:-moz-grab;cursor:-webkit-grab;cursor:grab}.handsontable.ht__manualRowMove.on-moving--rows *,.handsontable.ht__manualRowMove.on-moving--rows tbody th.ht__highlight{cursor:move;cursor:-moz-grabbing;cursor:-webkit-grabbing;cursor:grabbing}.handsontable.ht__manualRowMove.on-moving--rows .manualRowResizer{display:none}.handsontable .ht__manualRowMove--backlight,.handsontable .ht__manualRowMove--guideline{display:none;position:absolute;width:100%}.handsontable .ht__manualRowMove--guideline{background:#757575;height:2px;left:0;margin-top:-1px;z-index:205}.handsontable .ht__manualRowMove--backlight{background:#343434;background:rgba(52,52,52,.25);display:none;pointer-events:none;z-index:205}.handsontable.on-moving--rows .ht__manualRowMove--backlight,.handsontable.on-moving--rows.show-ui .ht__manualRowMove--guideline{display:block}.handsontable tbody td[rowspan][class*=area][class*=highlight]:not([class*=fullySelectedMergedCell]):before{opacity:0}.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-0]:before,.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-multiple]:before{opacity:.1}.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-1]:before{opacity:.2}.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-2]:before{opacity:.27}.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-3]:before{opacity:.35}.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-4]:before{opacity:.41}.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-5]:before{opacity:.47}.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-6]:before{opacity:.54}.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-7]:before{opacity:.58}.handsontable[dir=ltr] div.htRight span[class*=sort-]{margin-left:-15px;margin-right:15px}.handsontable[dir=rtl] div.htLeft span[class*=sort-]{margin-left:15px;margin-right:-15px}.handsontable[dir=ltr] div.htRight span[class*=sort-]:only-child{margin-left:-20px;margin-right:20px}.handsontable[dir=rtl] div.htLeft span[class*=sort-]:only-child{margin-left:20px;margin-right:-20px}.handsontable span.colHeader.columnSorting:after{font-size:8px;height:8px;left:unset;line-height:1.1;margin-top:-2px;padding-left:5px;padding-right:unset;position:absolute;right:-15px;top:50%}[dir=rtl].handsontable span.colHeader.columnSorting:after{left:-15px;padding-left:unset;padding-right:5px;right:unset}.handsontable span.colHeader.columnSorting[class*=\" sort-\"]:after,.handsontable span.colHeader.columnSorting[class^=sort-]:after{content:\"+\"}.handsontable span.colHeader.columnSorting.sort-1:after{content:\"1\"}.handsontable span.colHeader.columnSorting.sort-2:after{content:\"2\"}.handsontable span.colHeader.columnSorting.sort-3:after{content:\"3\"}.handsontable span.colHeader.columnSorting.sort-4:after{content:\"4\"}.handsontable span.colHeader.columnSorting.sort-5:after{content:\"5\"}.handsontable span.colHeader.columnSorting.sort-6:after{content:\"6\"}.handsontable span.colHeader.columnSorting.sort-7:after{content:\"7\"}.htGhostTable th div button.changeType+span.colHeader.columnSorting:not(.indicatorDisabled){padding-right:5px}.handsontable thead th.hiddenHeader:not(:first-of-type){display:none}.handsontable th.ht_nestingLevels{padding-left:7px;text-align:left}[dir=rtl].handsontable th.ht_nestingLevels{padding-right:7px;text-align:right}.handsontable th div.ht_nestingLevels{display:inline-block;left:11px;position:absolute;right:unset}[dir=rtl].handsontable th div.ht_nestingLevels{left:unset;right:11px}.handsontable.innerBorderInlineStart th div.ht_nestingLevels,.handsontable.innerBorderInlineStart~.handsontable th div.ht_nestingLevels{left:unset;right:10px}[dir=rtl].handsontable.innerBorderInlineStart th div.ht_nestingLevels,[dir=rtl].handsontable.innerBorderInlineStart~.handsontable th div.ht_nestingLevels{left:10px;right:unset}.handsontable th span.ht_nestingLevel{display:inline-block}.handsontable th span.ht_nestingLevel_empty{display:inline-block;float:left;height:1px;width:10px}[dir=rtl].handsontable th span.ht_nestingLevel_empty{float:right}.handsontable th span.ht_nestingLevel:after{bottom:3px;content:\"┐\";display:inline-block;font-size:9px;position:relative}.handsontable th div.ht_nestingButton{cursor:pointer;display:inline-block;left:unset;position:absolute;right:-2px}[dir=rtl].handsontable th div.ht_nestingButton{left:-2px;right:unset}.handsontable th div.ht_nestingButton.ht_nestingExpand:after{content:\"+\"}.handsontable th div.ht_nestingButton.ht_nestingCollapse:after{content:\"-\"}.handsontable.innerBorderInlineStart th div.ht_nestingButton,.handsontable.innerBorderInlineStart~.handsontable th div.ht_nestingButton{left:unset;right:0}[dir=rtl].handsontable.innerBorderInlineStart th div.ht_nestingButton,[dir=rtl].handsontable.innerBorderInlineStart~.handsontable th div.ht_nestingButton{left:0;right:unset}\n\n/*!\n * Pikaday\n * Copyright © 2014 David Bushell | BSD & MIT license | https://dbushell.com/\n */.pika-single{background:#fff;border:1px solid;border-color:#ccc #ccc #bbb;color:#333;display:block;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;position:relative;z-index:9999}.pika-single:after,.pika-single:before{content:\" \";display:table}.pika-single:after{clear:both}.pika-single.is-hidden{display:none}.pika-single.is-bound{box-shadow:0 5px 15px -5px rgba(0,0,0,.5);position:absolute}.pika-lendar{float:left;margin:8px;width:240px}.pika-title{position:relative;text-align:center}.pika-label{background-color:#fff;display:inline-block;font-size:14px;font-weight:700;line-height:20px;margin:0;overflow:hidden;padding:5px 3px;position:relative;z-index:9999}.pika-title select{cursor:pointer;left:0;margin:0;opacity:0;position:absolute;top:5px;z-index:9998}.pika-next,.pika-prev{background-color:transparent;background-position:50%;background-repeat:no-repeat;background-size:75% 75%;border:0;cursor:pointer;display:block;height:30px;opacity:.5;outline:none;overflow:hidden;padding:0;position:relative;text-indent:20px;white-space:nowrap;width:20px}.pika-next:hover,.pika-prev:hover{opacity:1}.is-rtl .pika-next,.pika-prev{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAeCAYAAAAsEj5rAAAAUklEQVR42u3VMQoAIBADQf8Pgj+OD9hG2CtONJB2ymQkKe0HbwAP0xucDiQWARITIDEBEnMgMQ8S8+AqBIl6kKgHiXqQqAeJepBo/z38J/U0uAHlaBkBl9I4GwAAAABJRU5ErkJggg==);float:left}.is-rtl .pika-prev,.pika-next{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAeCAYAAAAsEj5rAAAAU0lEQVR42u3VOwoAMAgE0dwfAnNjU26bYkBCFGwfiL9VVWoO+BJ4Gf3gtsEKKoFBNTCoCAYVwaAiGNQGMUHMkjGbgjk2mIONuXo0nC8XnCf1JXgArVIZAQh5TKYAAAAASUVORK5CYII=);float:right}.pika-next.is-disabled,.pika-prev.is-disabled{cursor:default;opacity:.2}.pika-select{display:inline-block}.pika-table{border:0;border-collapse:collapse;border-spacing:0;width:100%}.pika-table td,.pika-table th{padding:0;width:14.285714285714286%}.pika-table th{color:#999;font-weight:700;line-height:25px}.pika-button,.pika-table th{font-size:12px;text-align:center}.pika-button{background:#f5f5f5;border:0;box-sizing:border-box;-moz-box-sizing:border-box;color:#666;cursor:pointer;display:block;height:auto;line-height:15px;margin:0;outline:none;padding:5px;width:100%}.pika-week{color:#999;font-size:11px}.is-today .pika-button{color:#3af;font-weight:700}.has-event .pika-button,.is-selected .pika-button{background:#3af;border-radius:3px;box-shadow:inset 0 1px 3px #178fe5;color:#fff;font-weight:700}.has-event .pika-button{background:#005da9;box-shadow:inset 0 1px 3px #0076c9}.is-disabled .pika-button,.is-inrange .pika-button{background:#d5e9f7}.is-startrange .pika-button{background:#6cb31d;border-radius:3px;box-shadow:none;color:#fff}.is-endrange .pika-button{background:#3af;border-radius:3px;box-shadow:none;color:#fff}.is-disabled .pika-button{color:#999;cursor:default;opacity:.3;pointer-events:none}.is-outside-current-month .pika-button{color:#999;opacity:.3}.is-selection-disabled{cursor:default;pointer-events:none}.pika-button:hover,.pika-row.pick-whole-week:hover .pika-button{background:#ff8000;border-radius:3px;box-shadow:none;color:#fff}.pika-table abbr{border-bottom:none;cursor:help}", "@tailwind base;\n@tailwind components;\n@tailwind utilities;\n", "@import url('https://fonts.googleapis.com/css2?family=Work+Sans:ital,wght@0,100..900;1,100..900&display=swap');\nbody \n{\n  font-family: 'Work Sans', sans-serif;\n}\n.worksans\n{\n  font-family: 'Work Sans', sans-serif;\n}\n/* width */\n::-webkit-scrollbar {\n  width: 4px;\n  height: 4px;\n}\n\n/* Track */\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n}\n\n/* Handle */\n::-webkit-scrollbar-thumb {\n  background: #888;\n}\n\n/* Handle on hover */\n::-webkit-scrollbar-thumb:hover {\n  background: #555;\n}\n.arrow-btn {\n  transition: filter 0.3s ease;\n}\n.arrow-btn:hover img {\n  filter: brightness(0);\n}\n.arrow-btn {\n  padding: 0 5px;\n}\n.divider {\n  height: 34px;\n  width: 1px;\n  background-color: #ccc;\n  margin: 0;\n}\n.menu-dropdown {\n  background-color: #323780;\n  padding: 11px 9px;\n  border-radius: 4px;\n  display: inline-flex;\n  align-items: center;\n  color: #fff !important;\n}\n.common-btn {\n  display: flex;\n  flex-direction: column;\n  gap: 5px;\n  height: auto;\n  padding: 0 8px;\n}\n.common-btn .ant-typography,\n.label-dropdown .ant-typography {\n  margin: 0;\n  font-size: 11px;\n  font-weight: 500;\n  color: #999999;\n  font-family: 'Work Sans', sans-serif;\n}\n.common-btn:hover {\n  background-color: transparent !important;\n}\n.common-btn:hover .ant-typography,\n.label-dropdown:hover .ant-typography {\n  color: #383838;\n}\n.common-btn:hover img,\n.label-dropdown:hover img {\n  filter: brightness(0);\n}\n.feedback-link {\n  color: rgb(37, 41, 99);\n  display: flex;\n  gap: 7px;\n  font-family: 'Work Sans', sans-serif;\n  font-weight: 500;\n  font-size: 16px;\n}\n.label-dropdown {\n  padding: 0 8px;\n  display: inline-block;\n}\n.label-dropdown > div {\n  flex-direction: column;\n  display: flex;\n}\n.label-box {\n  display: flex;\n  gap: 6px;\n}\n.insight-header {\n  background-color: rgb(245, 245, 245);\n  border-bottom: 1px solid rgb(204, 204, 204);\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n  height: auto;\n  align-items: center;\n  padding: 10px 20px;\n}\n.insight-header .ant-space {\n  flex-wrap: wrap;\n}\n.side-tray {\n  padding: 10px;\n  background: #f5f5f5;\n  max-width: 122px;\n  height: 100%;\n  overflow-x: hidden;\n  overflow-y: auto;\n  border-right: 1px solid #CCCCCC;\n}\n.panel-content:has(.main-content) {\n  display: flex;\n  flex-direction: column;\n  height: calc(100vh - 98px);\n  padding: 0;\n}\n.tray-header-btn button {\n  background-color: transparent !important;\n  padding: 0;\n}\n.textarea-box {\n  display: flex;\n  /* border: 1.5px solid #d9d9d9; */\n  border-radius: 4px;\n  min-height: 70px;\n  min-width: 90px;\n  /* background: #fff; */\n  text-align: center;\n  align-items: center;\n  justify-content: center;\n  font-size: 35px;\n  cursor: pointer;\n  font-weight: 600;\n  color: #252963;\n}\n.textarea-box:hover \n{\n  background-color: #eee;\n}\n\n.Active.textarea-box {\n  background: #252963;\n  color: #fff;\n  border-radius: 4px 4px 0 0;\n}\n.arrowblue {\n  background-color: transparent !important;\n  padding: 0;\n  height: auto !important;\n}\n\n.insight-tabs {\n  width: 100%;\n  max-width: 350px;\n  min-width: 350px;\n  height: 100%;\n  border-right: 1px solid #ccc;\n  display: flex;\n  transition: all 0.3s ease-in-out 0s;\n  flex-direction: column;\n}\n.tabs-header button {\n  flex: 1;\n  background: transparent;\n  border: none;\n  border-bottom: 1px solid #cccccc;\n  padding: 13px;\n  display: inline-flex;\n  justify-content: center;\n  align-items: center;\n  gap: 6px;\n  font-size: 15px;\n  color: #252963;\n  font-weight: 400;\n}\n.tabs-header button:first-child {\n  border-right: 1px solid #ccc;\n}\n.tab-content {\n  position: relative;\n  height: calc(100vh - 169px);\n}\n.Search-bar {\n  display: flex;\n  gap: 10px;\n  flex-direction: column;\n}\n.Search-bar input[type='text'],\n.Search-bar input[type='search'] {\n  border: 1px solid #cccccc;\n  height: 38px;\n  border-radius: 7px;\n  font-family: 'Work Sans', sans-serif;\n  padding: 0 10px;\n  font-size: 15px;\n  background: white;\n}\n.Search-bar input:focus {\n  outline: 0;\n}\n.Search-bar label {\n  display: flex;\n  color: #666666;\n  gap: 5px;\n  font-size: 13px;\n  font-family: 'Work Sans', sans-serif;\n}\n.btn-secondary-new {\n  height: 32px;\n  border: 1px solid #d0d5dd;\n  border-radius: 8px;\n  background-color: transparent;\n  color: #344054;\n  font-size: 13px;\n  font-family: 'Work Sans', sans-serif;\n  padding: 0 12px;\n  gap: 4px;\n  display: inline-flex;\n  line-height: 18px;\n}\n.btn-secondary-new:hover {\n  background-color: #eee;\n}\n.btn-primary-new {\n  height: 32px;\n  border: 1px solid #252963;\n  border-radius: 8px;\n  background-color: #252963;\n  color: #fff;\n  font-size: 13px;\n  font-family: 'Work Sans', sans-serif;\n  padding: 0 12px;\n  gap: 4px;\n  align-items: center;\n  justify-content: center;\n  display: inline-flex;\n  line-height: 18px;\n}\n.filter-btns {\n  margin-bottom: 30px;\n  margin-top: 20px;\n}\n.filterBtn {\n  color: #252963;\n  font-size: 13px;\n  margin-right: 10px;\n}\n.assets-groups h4 {\n  margin: 0;\n  color: #32377f;\n  font-size: 14px;\n  font-weight: 500;\n  font-family: 'Work Sans', sans-serif;\n}\n.assets-groups .btn.btn-secondary-new {\n  height: 24px;\n}\n.assets-groups .ant-flex {\n  padding-bottom: 7px;\n  border-bottom: 1px solid #e6e6e6;\n  margin-bottom: 9px;\n}\n.assets-groups .ant-typography {\n  font-size: 12px;\n  color: #666666;\n  font-family: 'Work Sans', sans-serif;\n}\n.action-btn button {\n  border: none;\n  height: auto;\n  padding: 0;\n  background-color: transparent;\n}\n\n.action-btn {\n  gap: 6px;\n}\n.assets-tree {\n  margin-top: 26px;\n}\n.assets-tree h4 {\n  margin: 0 0 7px;\n  color: #32377f;\n  font-size: 14px;\n  font-weight: 500;\n  font-family: 'Work Sans', sans-serif;\n}\n.assets-tree ul {\n  padding: 0;\n  margin: 0;\n  list-style-type: none;\n  height: calc(100vh - 565px);\n  overflow: auto;\n}\n.assets-tree span {\n  color: #252963;\n  font-size: 12px;\n  font-weight: 600;\n  font-family: 'Work Sans', sans-serif;\n  width: 24px;\n  display: inline-block;\n  text-align: center;\n}\n.assets-tree .ant-typography {\n  color: #000000;\n  font-size: 16px;\n  font-weight: 500;\n  font-family: 'Work Sans', sans-serif;\n  margin: 0 0 10px;\n  line-height: 14px;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  line-clamp: 2;\n  -webkit-line-clamp: 2;\n  overflow: hidden;\n}\n.assets-tree li {\n  padding: 8px;\n  border-bottom: 1px solid #e6e6e6;\n}\n.common-box {\n  flex: 1;\n  height: 100%;\n  padding: 15px 20px 40px;\n  background: #f5f5f5;\n  width: calc(100% - 360px);\n  overflow: auto;\n}\n.form-input {\n  position: relative;\n  margin-bottom: 8px;\n}\n.form-input input {\n  border: 1px solid #cccccc;\n  width: 100%;\n  background: #fff;\n  height: 46px;\n  padding-left: 150px;\n  outline: 0 !important;\n}\n\n.columns-btn {\n  display: flex;\n  align-items: center;\n  gap: 14px;\n  background: #f5f5f5;\n  border: 1px solid #cccccc;\n  max-width: 144px;\n  padding: 0 12px;\n  height: 100%;\n  position: absolute;\n  left: 0;\n  width: 100%;\n  font-family: 'Work Sans', sans-serif;\n  font-weight: 500;\n  color: #4d4d4d;\n  font-size: 16px;\n}\n.work-book-box {\n  padding: 0;\n  background: #fff;\n  border: 1px solid #cccccc;\n  margin-top: 0px;\n  display: flex;\n  flex: 1;\n  flex-direction: column;\n}\n.work-book-box h4 {\n  font-size: 20px;\n  color: #000;\n  font-family: 'Work Sans', sans-serif;\n  font-weight: 400;\n}\n.dropfile-here1 {\n  border-right: 1px solid #b3b3b3;\n  border-bottom: 1px solid #b3b3b3;\n  text-align: center;\n  font-size: 20px;\n  color: #999999;\n  font-family: 'Work Sans', sans-serif;\n  font-weight: 400;\n  padding: 5px 0 20px;\n}\n.dropfile-here2 {\n  display: flex;\n  width: 76px;\n  border-right: 1px solid #b3b3b3;\n  height: calc(100% + 45px);\n  top: -45px;\n  position: relative;\n  align-items: center;\n  font-size: 20px;\n  color: #999999;\n  font-family: 'Work Sans', sans-serif;\n  font-weight: 400;\n  padding-right: 16px;\n}\n.dropfile-here3 {\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 20px;\n  color: #999999;\n  font-family: 'Work Sans', sans-serif;\n  font-weight: 400;\n}\n.dropfile {\n  position: relative;\n  height: 100%;\n}\n.dropfile-here3 {\n  border-right: 1px solid #b3b3b3;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 20px;\n  color: #999999;\n  font-family: 'Work Sans', sans-serif;\n  font-weight: 400;\n  flex: 1;\n  position: absolute;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  top: 0;\n  border-bottom: 1px solid #b3b3b3;\n}\n.arrow-btn1 {\n  width: 24px;\n  height: 24px;\n  border-radius: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 1px solid #cccccc;\n  background: #fff;\n  padding: 0;\n  position: absolute;\n  right: -12px;\n  top: 8px;\n  z-index: 99;\n}\n.arrow-btn1 img {\n  margin-left: -1px;\n  transition: all 0.3s ease-in-out 0s;\n}\n.sidebar-remove .insight-tabs {\n  max-width: 0;\n  min-width: 0;\n  border: none;\n}\n.sidebar-remove .insight-tabs .tabs-header,\n.sidebar-remove .insight-tabs .tab-content * {\n  display: none !important;\n}\n.sidebar-remove .insight-tabs .tab-content {\n  padding: 0;\n}\n.sidebar-remove .insight-tabs .tab-content .arrow-btn1 {\n  display: flex !important;\n  top: 50px;\n  right: -14px;\n}\n.sidebar-remove .insight-tabs .tab-content .arrow-btn1.active img {\n  display: block !important;\n  margin: 0;\n  transform: rotate(-180deg);\n}\n.react-grid-layout:has(.main-content) , .react-grid-layout:has(.main-content) .react-grid-item , .react-grid-layout:has(.main-content) .react-grid-item > div\n{\n  height: auto !important;\n}\n.overlay-text {\n  background-color: #F2F6F9;\n  border-top: 1px solid #EEEEEE;\n  padding: 5px 10px;\n  position: absolute;\n  z-index: 2;\n  bottom: 0;\n  left: 0;\n  font-size: 12px;\n  color: #252963;\n  right: 0;\n  font-family: 'Work Sans', sans-serif;\n\n}\n.css-1xodasp:has(.main-content) {\n  padding-bottom: 0 !important;\n}\n.relative\n{\n  position: relative;\n}\n\n\n.dataContent h4 \n{\n  color: #252963;\n  font-size: 24px;\n  font-family: 'Work Sans', sans-serif;\n  font-weight: 500;\n  margin-bottom: 10px;\n  letter-spacing: 0;\n}\n.main-content\n{\n  letter-spacing: 0;\n  height: calc(100vh - 119px);\n}\n\n.connect-data {\n  font-weight: 500;\n  display: flex;\n  padding: 8px 12px;\n  align-items: center;\n  gap: 8px;\n  color: #000000;\n  font-size: 16px;\n  font-family: 'Work Sans', sans-serif;\n}\n.connect-data:hover ,.connect-data.active\n{\n  background: #E9E9F5;\n}\n.data-outer ul {\n  margin: 0;\n  padding-left: 36px;\n  list-style-type: none;\n  padding-top: 10px;\n  overflow: hidden;\n}\n.data-outer ul ul{\n  padding-left: 4px;\n}\n.data-outer ul li \n{\n  font-size: 14px;\n  color: #666666;\n  font-family: 'Work Sans', sans-serif;\n  padding-bottom: 5px;\n  position: relative;\n  padding-left: 10px;\n}\n.data-outer ul ul li \n{\n  padding-left: 10px;\n}\n.data-outer ul li::before {\n  left: 0;\n  height: 1px;\n  width: 10px;\n  background-color: #ccc;\n  content: \"\";\n  position: absolute;\n  top: 10px;\n}\n.data-outer ul li::after {\n  left: 0;\n  height: 100%;\n  width: 1px;\n  background-color: #ccc;\n  content: \"\";\n  position: absolute;\n  top: -16px;\n}\n.data-outer ul li span {\n  width: 100%;\n  word-break: break-word;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 1;\n  overflow: hidden;\n}\n.connect-data img {\n  position: relative;\n  top: -1px;\n}\n.seprater {\n  display: inline-block;\n  width: 100%;\n  height: 1px;\n  background: #D9D9D9;\n  margin: 22px 0;\n}\n\n.data-source ul {\n  padding: 0;\n  margin: 0;\n  list-style-type: none;\n  max-height: calc(100vh - 565px);\n  overflow: auto;\n  display: contents;\n}\n.data-source span {\n  color: #252963;\n  font-size: 12px;\n  font-weight: 600;\n  font-family: 'Work Sans', sans-serif;\n  width: 24px;\n  display: inline-block;\n  text-align: center;\n}\n.data-source .ant-typography {\n  color: #666666;\n  font-size: 14px;\n  font-weight: 400;\n  font-family: 'Work Sans', sans-serif;\n  margin: 0;\n  line-height: 14px;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  line-clamp: 2;\n  -webkit-line-clamp: 2;\n  overflow: hidden;\n  text-align: left;\n}\n.data-source li {\n  padding: 8px; \n}\n.data-source span img \n{\n  position: relative;\n  margin: auto;\n  top: -1px;\n}\n.data-source li:hover\n{\n  background-color: #eeeeee75;\n}\n.data-outer\n{\n  margin-bottom: 5px;\n}\n.upload-btn {\n  margin-top: 10px;\n  display: inline-block;\n  width: 100%;\n}\n.upload-btn button {\n  border: 1px dashed #323780;\n  height: 200px;\n  color: #323780;\n  width: 100%;\n  background: rgba(50, 55, 128, 0.0509803922);\n  font-weight: bold;\n  font-size: 20px;\n  flex-direction: column;\n}\n.upload-btn .ant-upload\n{\n  width: 100%;\n}\n.upload-btn  .ant-btn-icon\n{\n  font-size: 30px;\n}\n.insight-common-ul ul\n{\n  list-style-type: none;\n  margin: 0;\n  padding: 0;\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n.insight-common-ul ul li\n{\n  cursor: pointer;\n}\n.insight-common-ul ul li .ant-typography\n{\n  font-size: 15px;\n  color: #000000;\n  font-family: 'Work Sans', sans-serif;\n  margin: 0;\n}\n.insight-common-ul ul li img\n{\n  position: relative;\n  top: -1px; \n}\n.heading-bold\n{\n  margin-bottom: 15px !important;\n  font-weight: 600 !important;\n  font-size: 16px !important;\n  font-family: 'Work Sans', sans-serif;\n  letter-spacing: 0;\n}\n.insight-common-ul ul li:hover .ant-typography\n{\n  color: #252963;\n}\n.ant-btn-primary\n{\n  background-color: #323780 !important;\n  border-color: #323780 !important;\n}\n.ant-btn-default:hover \n{\n  border-color: #323780 !important;\n  color: #323780 !important;\n}\n.App\n{\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n}\n.inner-box {\n  display: flex;\n  width: 100%;\n}\n\n.data-table {\n  width: 100%;\n  border-right: 1px solid #ccc;\n}\n.chart-data {\n  width: 35%;\n}\n.sidebar-outer\n{\n  width: calc(100% - 122px);\n}\n.component-data {\n  min-height: 300px;\n  max-height: 300px;\n  width: 100%;\n  display: inline-block;\n  background-color: #FFF;\n}\n.data-table .ant-table-content\n{\n  overflow: auto !important;\n  height: calc(100vh - 208px);\n}\n.data-source li .ant-space-item {\n  align-items: center;\n  justify-content: center;\n  display: inline-flex;\n  text-align: center;\n}\n\n.flow-canvas {\n  display: flex;\n  height: 80vh;\n  width: 79vw;\n  overflow: hidden;\n}\n\n.component-data .ant-table {\n  width: 100%;\n}\n\n.component-data .ant-table-thead > tr > th {\n  background: #f5f5f5;\n  font-weight: 500;\n  color: #252963;\n  font-family: 'Work Sans', sans-serif;\n}\n\n.component-data .ant-table-tbody > tr > td {\n  font-family: 'Work Sans', sans-serif;\n}\n\n.component-data .ant-table-tbody > tr:hover > td {\n  background: #f0f0f0;\n}\n\n.component-data {\n  padding: 0;\n  overflow: auto;\n}\n\n/* Add these new styles */\n.files-section {\n  padding: 20px;\n}\n\n.files-heading {\n  font-size: 16px;\n  font-weight: 500;\n  margin-bottom: 20px;\n  color: #000;\n  font-family: 'Work Sans', sans-serif;\n}\n\n.files-table-container {\n  background: #f5f5f5;\n}\n\n.connected-files-section {\n  padding: 8px;\n  color: #666;\n  font-size: 14px;\n  font-family: 'Work Sans', sans-serif;\n  border-bottom: 1px solid #e6e6e6;\n}\n\n\n/* Adjust the component-data class */\n.component-data {\n  min-height: auto;\n  max-height: none;\n}\n\n.files-table .ant-table-tbody > tr > td span {\n  cursor: pointer;\n}\n\n.files-table-container table\n{\n  border-radius: 0;\n  margin-bottom: 15px;\n}\n.files-table-container table thead th \n{\n  background-color: #EDEDED !important;\n  border-radius: 0 !important;\n  font-size: 16px !important;\n  color: #4D4D4D !important;\n  font-weight: 500 !important;\n  font-family: 'Work Sans', sans-serif; \n  padding: 11px 16px !important;\n}\n.files-table-container table tbody tr td \n{\n  padding: 11px 16px !important;\n  color: #333333;\n  font-size: 16px;\n  font-family: 'Work Sans', sans-serif; \n  border-color: #ccc !important; \n  background: transparent !important;\n}\n.files-table-container .first-table table tbody tr:first-child td \n{\n  border: none !important;\n  height: 48px;\n}\n.files-table-container .first-table table tbody tr:first-child td span\n{\n  display: none;\n}\n.files-table-container table tbody tr\n{\n  background-color: transparent !important;\n}\n.files-table-container .ant-table\n{\n  background-color: transparent !important;\n}\n.files-table-container table tbody tr:last-child td \n{\n  border: none !important;\n}\n.second-table thead\n{\n  display: none;\n}\n.operation-form .ant-form-item\n{\n  margin-bottom: 15px;\n}\n\n\n.column-checkbox-group  .checkbox-item {\n  margin-bottom: 10px;\n  width: 100%;\n  border-bottom: 1px solid #eee;\n  padding-bottom: 10px;\n}\n.column-checkbox-group  .checkbox-item:last-child \n{\n  border: none;\n}\n:where(.css-dev-only-do-not-override-apn68).ant-checkbox-checked .ant-checkbox-inner\n{\n  background-color: #252963;\n  border-color: #252963;\n}\n:where(.css-dev-only-do-not-override-apn68).ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover .ant-checkbox-inner, :where(.css-dev-only-do-not-override-apn68).ant-checkbox:not(.ant-checkbox-disabled):hover .ant-checkbox-inner {\n  border-color: #323780;\n}\n:where(.css-dev-only-do-not-override-apn68).ant-checkbox .ant-checkbox-inner:after\n{\n  top: 47%;\n}\n:where(.css-dev-only-do-not-override-apn68).ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover .ant-checkbox-checked:not(.ant-checkbox-disabled) .ant-checkbox-inner {\n  background-color: #252963;\n  border-color: transparent;\n}\n.tab-inner {\n  height: 100%;\n  /* padding: 20px 20px 30px; */\n  padding-left: 15px;\n  overflow: auto;\n}\n.divide-box {\n  padding: 0 20px 30px;\n}\ntable.table-custom {\n  display: table;\n  border-collapse: separate;\n  box-sizing: border-box;\n  text-indent: initial;\n  border-spacing: 2px;\n  border-color: gray;\n}\ntable.table-custom {\n  border-collapse: collapse;\n}\nt.table-custom head {\n  display: table-header-group;\n  vertical-align: middle;\n  border-color: inherit;\n}\n.table-custom tr {\n  display: table-row;\n  vertical-align: inherit;\n  border-color: inherit;\n}\n.table-custom td, .table-custom th {\n  padding: 0.75rem;\n  vertical-align: top;\n  border-top: 1px solid #dee2e6;\n  white-space: nowrap;\n}\n.table-custom thead th {\n  vertical-align: bottom; \n  background: #f6f6f6;\n}\n.stepsheight\n{\n  height: calc(100dvh - 54px);\n}\n.main-box\n{\n  height: calc(100dvh - 77px);\n  overflow: auto;\n}\n.custom-grid\n{\n  grid-template-columns: repeat(auto-fit, 250px);  \n}\n.quality-measure\n{\n  margin-left: auto;\n  width: calc(100% - 44px);\n}\n.parameter-section\n{\n  height: 100%;\n  overflow: auto;\n  width: calc(100% - 300px);\n}\n.select-target .ant-select-selector {\n  background: #F7F7F7 !important;\n  border-color: #B3B3B3 !important;\n  border-radius: 4px !important;\n}\n.select-target .ant-select-selection-placeholder\n{\n  color:  #000;\n}\n.querybuilder  .ruleGroup {\n  background: transparent;\n  border: none;\n  padding: 0;\n  gap: 20px;\n}\n.querybuilder .ruleGroup-body select , .querybuilder .ruleGroup-body input {\n  height: 32px;\n  border: 1px solid #B3B3B3;\n  border-radius: 4px;\n  background: #F7F7F7;\n}\n/* .stages\n{\n  height: calc(100dvh - 150px);\n} */\n.common-table th.ant-table-cell {\n  background: #EDEEF2 !important;\n  border: 1px solid #F7F7F7;\n  border-top: 1px solid #252963;\n  border-radius: 0 !important;\n  font-weight: 500 !important;\n  color: #000 !important;\n}\n.common-table tbody tr:nth-child(odd) {\n  background: #F7F7F7;\n}\n.common-table tbody tr td {\n  padding: 5px 16px !important;\n  border-right: 2px solid #F7F7F7;\n}\n.main-header .ant-select-selector {\n  background: #F7F7F7 !important;\n  border: 1px solid #B3B3B3 !important;\n  max-width: 200px !important;\n  font-size: 14px;\n  font-family: 'Work Sans', sans-serif;\n  width: 100% !important;\n}\n \n\n.main-header .ant-select  {\n  width: 100%;\n  max-width: 200px;\n}\n.main-header .ant-picker {\n  background: #F7F7F7;\n  border-color: #B3B3B3;\n  max-width: 230px;\n}\n.main-header .ant-picker.ant-picker-disabled {\n  cursor: not-allowed;\n  border-color: #d9d9d9;\n  color: rgba(0, 0, 0, 0.25);\n  background: rgba(0, 0, 0, 0.04);\n  opacity: 0.5;\n  box-shadow: none;\n}\n@media screen and (max-width:1452px)\n{\n  .insight-header .ant-space\n  {\n    gap: 4px;\n  }\n  .insight-header\n  {\n    padding: 10px 8px;\n  }\n} \n@media screen and (max-width:1348px)\n{\n  .insight-header .ant-space\n  {\n    gap: 3px;\n  }\n \n  .common-btn\n  {\n    padding: 0 4px;\n  }\n  .menu-dropdown\n  {\n    padding: 11px 5px;\n  }\n  .feedback-link\n  {\n    gap: 4px !important;\n    font-size: 14px;\n  }\n  .label-dropdown {\n    padding: 0 5px; \n  }\n  .arrow-btn {\n      padding: 0 2px;\n  }\n} \n\n.dashboard-menu {\n  border-right: none !important;\n}\n\n.dashboard-menu .ant-menu-item {\n  height: 48px;\n  line-height: 48px;\n  margin: 4px 0;\n  border-radius: 4px;\n}\n\n.dashboard-menu .ant-menu-item:hover {\n  background-color: #E9E9F5;\n}\n\n.dashboard-menu .ant-menu-item-selected {\n  background-color: #E9E9F5 !important;\n  color: #32377F !important;\n  font-weight: 500;\n}\n\n.dashboard-sidebar {\n  height: 100%;\n  background-color: #fff;\n}\n\n.dashboard-sidebar-container {\n  background-color: #fff;\n  border-right: 1px solid #ccc;\n}\n\n.dashboard-content-container {\n  background-color: #fff;\n  padding: 20px;\n}\n\n.dashboard-menu .ant-menu-item {\n  margin: 4px 0 !important;\n  width: calc(100% - 16px) !important;\n  border-radius: 4px !important;\n}\n\n.dashboard-menu .ant-menu-item-selected {\n  background-color: #E9E9F5 !important;\n  color: #32377F !important;\n  font-weight: 500 !important;\n}\n\n.dashboard-menu .ant-menu-item:hover {\n  color: #32377F !important;\n}\n\n.rotate-icon{\n  transform: rotate(180deg);\n}\n\n.space-y-2 > * + * {\n  margin-top: 0.5rem;\n}\n\n.list-decimal {\n  list-style-type: decimal;\n}\n\n.custom-node[data-type=\"result\"] {\n  background-color: #252963;\n  color: white;\n  cursor: pointer;\n  padding: 8px 16px;\n  border-radius: 4px;\n  transition: all 0.3s ease;\n}\n\n.custom-node[data-type=\"result\"]:hover {\n  opacity: 0.9;\n}\n\n.result-node {\n  background-color: #FFF9C4 !important; /* Light yellow color */\n  border: 1px solid #FFF176;\n  color: #000000;\n  padding: 8px 16px;\n  border-radius: 4px;\n  transition: all 0.3s ease;\n}\n\n.result-node:hover {\n  background-color: #FFF59D !important;\n}\n\n.result-node .node-content {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.result-node .node-label {\n  font-weight: 500;\n}\n\n\n\n\n.divide-box > div.ml-6 {\n  margin: 0;\n  padding: 6px 0;\n  border-bottom: 1px solid #ededed;\n}\n\n.folder-name .icons{\n  display: none;\n}\n.folder-name:hover .icons{\n  display: flex;\n}\n:where(.css-dev-only-do-not-override-apn68).ant-radio-wrapper .ant-radio-checked .ant-radio-inner {\n  border-color: #252963;\n  background-color: transparent;\n}\n:where(.css-dev-only-do-not-override-apn68).ant-radio-wrapper .ant-radio-inner::after\n{\n  background-color: #252963;\n  width: 18px;\n  height: 18px;\n  margin-block-start: -9px;\n  margin-inline-start: -9px;\n}\n.ant-radio-wrapper:hover :where(.css-dev-only-do-not-override-apn68).ant-radio-wrapper, :where(.css-dev-only-do-not-override-apn68).ant-radio-wrapper:hover .ant-radio-inner {\n  border-color: #252963;\n}\n.select-file.active:hover span{\n  color: #fff !important;\n}\n  \n.select-file.active span{\n  color: #fff !important;\n}\n  \n.three-column\n{\n  -webkit-column-count: 3;\n    -moz-column-count: 3;\n    column-count: 3;\n}\n\ntable{\n  scrollbar-width: thin;\n  scrollbar-color: #eaeaea transparent;\n  scrollbar-gutter: stable;\n}\n\n.ant-table-body{\n  overflow: auto !important;\n}\n\n.task-sidebar{\n  transition: all 0.5s ease-in-out 0s;\n  height: calc(100vh - 54px)\n}\n\n.task-page{\n  transition: all 0.5s ease-in-out 0s;\n}\n/* .correlation-heatmap [type=\"heatmap\"] , .correlation-heatmap  .apexcharts-canvas , .correlation-heatmap  .apexcharts-canvas svg\n{\n  width: 100% !important;\n} */\n\n.stepsheight .ant-card-head\n{\n  background: #e5e7eb;\n}\n\n.disable-builder .ruleGroup {\n  background: #ddd9d9c9;\n  border-color: #cfcfcfbf;\n}\n\n.overlay {\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent background */\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    z-index: 3; /* Ensure it is above other content */\n}\n\n.overlay-content {\n    background: white;\n    padding: 20px;\n    border-radius: 5px;\n    text-align: center;\n    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);\n}\n\n.gray-image {\n  filter: grayscale(100%) opacity(80%);\n  height: 18px\n}\n\n.compare-row{\n  background-color: #FDEB90 !important;\n}\n\n.compare-row:hover td {\n  background-color: #FDEB90 !important;\n}\n.date-range-selection {\n  background: #E9E9F5;\n  margin-top: 10px;\n  padding: 4px 10px;\n  margin-left: 30px;\n}\n\n.grid-item.fullscreen {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 1000;\n  background: white;\n}\n\n.grid-item.hidden {\n  display: none;\n}\n/* \n\n/* VS Code-like styling */\n/* .vscode-tabs-container {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;\n  background: #1e1e1e;\n  color: #cccccc;\n  height: 100vh;\n} */\n\n/* .tab-bar-wrapper {\n  display: flex;\n  background: #252526;\n  border-bottom: 1px solid #1e1e1e;\n} */\n\n/* .react-tabs__tab-list {\n  display: flex;\n  margin: 0;\n  padding: 0;\n  flex-grow: 1;\n} */\n\n/* .react-tabs__tab {\n  list-style: none;\n  padding: 8px 16px;\n  cursor: pointer;\n  background: #2d2d2d;\n  margin-right: 1px;\n  color: #969696;\n  display: flex;\n  align-items: center;\n}\n\n.react-tabs__tab--selected {\n  background: #1e1e1e;\n  color: #ffffff;\n  border-top: 1px solid #0e639c;\n} */\n\n/* .tab-content {\n  display: flex;\n  align-items: center;\n} */\n\n/* .close-btn {\n  background: transparent;\n  border: none;\n  color: inherit;\n  margin-left: 8px;\n  cursor: pointer;\n  padding: 0 4px;\n} */\n\n/* .close-btn:hover {\n  color: #ffffff;\n} */\n\n/* .add-tab-btn {\n  background: #252526;\n  color: #cccccc;\n  border: none;\n  padding: 0 15px;\n  cursor: pointer;\n  font-size: 16px;\n  border-left: 1px solid #1e1e1e;\n} */\n\n/* .add-tab-btn:hover {\n  background: #2a2d2e;\n} */\n\n/* .panel-content {\n  padding: 20px;\n} */\n\n\n/* Custom styles for tabs interface */\n.no-scrollbar::-webkit-scrollbar {\n  display: none;\n}\n\n.no-scrollbar {\n  -ms-overflow-style: none;\n  scrollbar-width: none;\n}\n\n/* Drag and drop styles */\n.tab.drag-over {\n  box-shadow: inset 3px 0 0 #007ACC;\n  background-color: #f5f0f0 !important;\n  color: #000;\n}\n\n.tab.dragging {\n  opacity: 0.7;\n  border: 1px dashed #555;\n}\n\n/* Context menu styles */\n.context-menu {\n  position: fixed;\n  background-color: #FFFFFF;\n  color: white;\n  border: 1px solid #454545;\n  border-radius: 2px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);\n  z-index: 1000;\n}\n\n.context-menu-item {\n  padding: 6px 20px 6px 12px;\n  cursor: pointer;\n  color: #111111;\n  white-space: nowrap;\n  user-select: none;\n}\n\n.context-menu-item:hover {\n  background-color: #faf8f8;\n  /* color: #FFFFFF; */\n} \n.label-full  .ant-col >label \n{\n  width: 100%;\n}", "/* this gets exported as style.css and can be used for the default theming */\n/* these are the necessary styles for React Flow, they get used by base.css and style.css */\n.react-flow {\n  direction: ltr;\n}\n.react-flow__container {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n}\n.react-flow__pane {\n  z-index: 1;\n  cursor: -webkit-grab;\n  cursor: grab;\n}\n.react-flow__pane.selection {\n    cursor: pointer;\n  }\n.react-flow__pane.dragging {\n    cursor: -webkit-grabbing;\n    cursor: grabbing;\n  }\n.react-flow__viewport {\n  transform-origin: 0 0;\n  z-index: 2;\n  pointer-events: none;\n}\n.react-flow__renderer {\n  z-index: 4;\n}\n.react-flow__selection {\n  z-index: 6;\n}\n.react-flow__nodesselection-rect:focus,\n.react-flow__nodesselection-rect:focus-visible {\n  outline: none;\n}\n.react-flow .react-flow__edges {\n  pointer-events: none;\n  overflow: visible;\n}\n.react-flow__edge-path,\n.react-flow__connection-path {\n  stroke: #b1b1b7;\n  stroke-width: 1;\n  fill: none;\n}\n.react-flow__edge {\n  pointer-events: visibleStroke;\n  cursor: pointer;\n}\n.react-flow__edge.animated path {\n    stroke-dasharray: 5;\n    -webkit-animation: dashdraw 0.5s linear infinite;\n            animation: dashdraw 0.5s linear infinite;\n  }\n.react-flow__edge.animated path.react-flow__edge-interaction {\n    stroke-dasharray: none;\n    -webkit-animation: none;\n            animation: none;\n  }\n.react-flow__edge.inactive {\n    pointer-events: none;\n  }\n.react-flow__edge.selected,\n  .react-flow__edge:focus,\n  .react-flow__edge:focus-visible {\n    outline: none;\n  }\n.react-flow__edge.selected .react-flow__edge-path,\n  .react-flow__edge:focus .react-flow__edge-path,\n  .react-flow__edge:focus-visible .react-flow__edge-path {\n    stroke: #555;\n  }\n.react-flow__edge-textwrapper {\n    pointer-events: all;\n  }\n.react-flow__edge-textbg {\n    fill: white;\n  }\n.react-flow__edge .react-flow__edge-text {\n    pointer-events: none;\n    -webkit-user-select: none;\n       -moz-user-select: none;\n            user-select: none;\n  }\n.react-flow__connection {\n  pointer-events: none;\n}\n.react-flow__connection .animated {\n    stroke-dasharray: 5;\n    -webkit-animation: dashdraw 0.5s linear infinite;\n            animation: dashdraw 0.5s linear infinite;\n  }\n.react-flow__connectionline {\n  z-index: 1001;\n}\n.react-flow__nodes {\n  pointer-events: none;\n  transform-origin: 0 0;\n}\n.react-flow__node {\n  position: absolute;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n  pointer-events: all;\n  transform-origin: 0 0;\n  box-sizing: border-box;\n  cursor: -webkit-grab;\n  cursor: grab;\n}\n.react-flow__node.dragging {\n    cursor: -webkit-grabbing;\n    cursor: grabbing;\n  }\n.react-flow__nodesselection {\n  z-index: 3;\n  transform-origin: left top;\n  pointer-events: none;\n}\n.react-flow__nodesselection-rect {\n    position: absolute;\n    pointer-events: all;\n    cursor: -webkit-grab;\n    cursor: grab;\n  }\n.react-flow__handle {\n  position: absolute;\n  pointer-events: none;\n  min-width: 5px;\n  min-height: 5px;\n  width: 6px;\n  height: 6px;\n  background: #1a192b;\n  border: 1px solid white;\n  border-radius: 100%;\n}\n.react-flow__handle.connectionindicator {\n    pointer-events: all;\n    cursor: crosshair;\n  }\n.react-flow__handle-bottom {\n    top: auto;\n    left: 50%;\n    bottom: -4px;\n    transform: translate(-50%, 0);\n  }\n.react-flow__handle-top {\n    left: 50%;\n    top: -4px;\n    transform: translate(-50%, 0);\n  }\n.react-flow__handle-left {\n    top: 50%;\n    left: -4px;\n    transform: translate(0, -50%);\n  }\n.react-flow__handle-right {\n    right: -4px;\n    top: 50%;\n    transform: translate(0, -50%);\n  }\n.react-flow__edgeupdater {\n  cursor: move;\n  pointer-events: all;\n}\n.react-flow__panel {\n  position: absolute;\n  z-index: 5;\n  margin: 15px;\n}\n.react-flow__panel.top {\n    top: 0;\n  }\n.react-flow__panel.bottom {\n    bottom: 0;\n  }\n.react-flow__panel.left {\n    left: 0;\n  }\n.react-flow__panel.right {\n    right: 0;\n  }\n.react-flow__panel.center {\n    left: 50%;\n    transform: translateX(-50%);\n  }\n.react-flow__attribution {\n  font-size: 10px;\n  background: rgba(255, 255, 255, 0.5);\n  padding: 2px 3px;\n  margin: 0;\n}\n.react-flow__attribution a {\n    text-decoration: none;\n    color: #999;\n  }\n@-webkit-keyframes dashdraw {\n  from {\n    stroke-dashoffset: 10;\n  }\n}\n@keyframes dashdraw {\n  from {\n    stroke-dashoffset: 10;\n  }\n}\n.react-flow__edgelabel-renderer {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n}\n.react-flow__edge.updating .react-flow__edge-path {\n      stroke: #777;\n    }\n.react-flow__edge-text {\n    font-size: 10px;\n  }\n.react-flow__node.selectable:focus,\n  .react-flow__node.selectable:focus-visible {\n    outline: none;\n  }\n.react-flow__node-default,\n.react-flow__node-input,\n.react-flow__node-output,\n.react-flow__node-group {\n  padding: 10px;\n  border-radius: 3px;\n  width: 150px;\n  font-size: 12px;\n  color: #222;\n  text-align: center;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #1a192b;\n  background-color: white;\n}\n.react-flow__node-default.selectable:hover, .react-flow__node-input.selectable:hover, .react-flow__node-output.selectable:hover, .react-flow__node-group.selectable:hover {\n      box-shadow: 0 1px 4px 1px rgba(0, 0, 0, 0.08);\n    }\n.react-flow__node-default.selectable.selected,\n    .react-flow__node-default.selectable:focus,\n    .react-flow__node-default.selectable:focus-visible,\n    .react-flow__node-input.selectable.selected,\n    .react-flow__node-input.selectable:focus,\n    .react-flow__node-input.selectable:focus-visible,\n    .react-flow__node-output.selectable.selected,\n    .react-flow__node-output.selectable:focus,\n    .react-flow__node-output.selectable:focus-visible,\n    .react-flow__node-group.selectable.selected,\n    .react-flow__node-group.selectable:focus,\n    .react-flow__node-group.selectable:focus-visible {\n      box-shadow: 0 0 0 0.5px #1a192b;\n    }\n.react-flow__node-group {\n  background-color: rgba(240, 240, 240, 0.25);\n}\n.react-flow__nodesselection-rect,\n.react-flow__selection {\n  background: rgba(0, 89, 220, 0.08);\n  border: 1px dotted rgba(0, 89, 220, 0.8);\n}\n.react-flow__nodesselection-rect:focus,\n  .react-flow__nodesselection-rect:focus-visible,\n  .react-flow__selection:focus,\n  .react-flow__selection:focus-visible {\n    outline: none;\n  }\n.react-flow__controls {\n  box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.08);\n}\n.react-flow__controls-button {\n    border: none;\n    background: #fefefe;\n    border-bottom: 1px solid #eee;\n    box-sizing: content-box;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    width: 16px;\n    height: 16px;\n    cursor: pointer;\n    -webkit-user-select: none;\n       -moz-user-select: none;\n            user-select: none;\n    padding: 5px;\n  }\n.react-flow__controls-button:hover {\n      background: #f4f4f4;\n    }\n.react-flow__controls-button svg {\n      width: 100%;\n      max-width: 12px;\n      max-height: 12px;\n    }\n.react-flow__controls-button:disabled {\n      pointer-events: none;\n    }\n.react-flow__controls-button:disabled svg {\n        fill-opacity: 0.4;\n      }\n.react-flow__minimap {\n  background-color: #fff;\n}\n.react-flow__minimap svg {\n  display: block;\n}\n.react-flow__resize-control {\n  position: absolute;\n}\n.react-flow__resize-control.left,\n.react-flow__resize-control.right {\n  cursor: ew-resize;\n}\n.react-flow__resize-control.top,\n.react-flow__resize-control.bottom {\n  cursor: ns-resize;\n}\n.react-flow__resize-control.top.left,\n.react-flow__resize-control.bottom.right {\n  cursor: nwse-resize;\n}\n.react-flow__resize-control.bottom.left,\n.react-flow__resize-control.top.right {\n  cursor: nesw-resize;\n}\n/* handle styles */\n.react-flow__resize-control.handle {\n  width: 4px;\n  height: 4px;\n  border: 1px solid #fff;\n  border-radius: 1px;\n  background-color: #3367d9;\n  transform: translate(-50%, -50%);\n}\n.react-flow__resize-control.handle.left {\n  left: 0;\n  top: 50%;\n}\n.react-flow__resize-control.handle.right {\n  left: 100%;\n  top: 50%;\n}\n.react-flow__resize-control.handle.top {\n  left: 50%;\n  top: 0;\n}\n.react-flow__resize-control.handle.bottom {\n  left: 50%;\n  top: 100%;\n}\n.react-flow__resize-control.handle.top.left {\n  left: 0;\n}\n.react-flow__resize-control.handle.bottom.left {\n  left: 0;\n}\n.react-flow__resize-control.handle.top.right {\n  left: 100%;\n}\n.react-flow__resize-control.handle.bottom.right {\n  left: 100%;\n}\n/* line styles */\n.react-flow__resize-control.line {\n  border-color: #3367d9;\n  border-width: 0;\n  border-style: solid;\n}\n.react-flow__resize-control.line.left,\n.react-flow__resize-control.line.right {\n  width: 1px;\n  transform: translate(-50%, 0);\n  top: 0;\n  height: 100%;\n}\n.react-flow__resize-control.line.left {\n  left: 0;\n  border-left-width: 1px;\n}\n.react-flow__resize-control.line.right {\n  left: 100%;\n  border-right-width: 1px;\n}\n.react-flow__resize-control.line.top,\n.react-flow__resize-control.line.bottom {\n  height: 1px;\n  transform: translate(0, -50%);\n  left: 0;\n  width: 100%;\n}\n.react-flow__resize-control.line.top {\n  top: 0;\n  border-top-width: 1px;\n}\n.react-flow__resize-control.line.bottom {\n  border-bottom-width: 1px;\n  top: 100%;\n}\n", ".react-flow {\n  background-color: #f8f8f8;\n  width: 100%;\n  height: 100%;\n}\n\n.custom-node {\n  padding: 10px 15px;\n  border-radius: 8px;\n  min-width: 180px;\n  position: relative;\n} \n.file-node {\n  background-color: #F5F5F5;\n  border: 2px solid #E3E3E3;\n  box-shadow: 0px 3px 5px 0px #0000001F;\n}\n\n.operation-node {\n  background-color: #E8E9FF; \n}\n\n.node-content {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.node-icon {\n  width: auto;\n  height: 17px;\n}\n\n.node-label {\n  font-size: 16px;\n  color: #000;\n  font-weight: 500;\n  letter-spacing: 0;\n  line-height: 19px;\n  flex: 1;\n}\n\n.connected-status {\n  position: absolute;\n  top: -23px;\n  left: 0;\n  font-size: 13px;\n  color: #000000;\n  padding: 0;\n  display: flex;\n  gap: 3px;\n  align-items: center;\n}\n\n.connected-status span {\n  width: 12px;\n  height: 12px;\n  border-radius: 3px;\n  display: inline-block;\n  background: #4AA17F;\n}\n.react-flow__handle {\n  width: 10px;\n  height: 10px;\n  background-color: #EDEDED;\n  border: 1px solid #B3B3B3;\n  border-radius: 2px;\n}\n\n.react-flow__edge-path, .react-flow__connection-path\n{\n  stroke: #B3B3B3;\n}\n.react-flow__handle-left {\n  left: -4px;\n}\n\n.react-flow__handle-right {\n  right: -4px;\n}\n\n.work-book-box {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n}\n\n.component-data {\n  flex: 1;\n  min-height: 0;\n  position: relative;\n}\n\n.flow-actions {\n  position: absolute;\n  top: 46px;\n  right: 12px;\n  z-index: 4;\n  border: 1px solid #CCCCCC;\n  border-radius: 14px;\n  overflow: hidden;\n  background-color: #F5F5F5;\n  padding: 6px;\n  box-shadow: 0px 4px 15px 0px #0000001F;\n}\n.combined-button {\n  padding: 0 !important;\n  height: 36px !important;\n  border: none !important;\n  border-radius: 4px !important;\n  overflow: hidden !important;\n  display: flex !important;\n  box-shadow: none !important;\n  background-color: transparent;\n  align-items: center !important; \n  gap:3px;\n  font-family: 'Work Sans', sans-serif;\n\n}\n\n.combined-button .execute-section {\n  display: flex;\n  align-items: center;\n  gap: 5px;\n  padding: 0 12px;\n  height: 100%;\n  background: #F5F5F5;\n  color: #344054;\n  font-size: 13px;\n  font-weight: 500;\n}\n.combined-button .save-section {\n  display: flex;\n  align-items: center;\n  padding: 0 20px;\n  height: 100%;\n  background: #32377F;\n  color: white;\n  border-radius: 9px;\n  font-size: 13px;\n  font-weight: 500;\n}\n.combined-button:hover .execute-section {\n  background: #f5f5f5;\n}\n\n.combined-button:hover .save-section {\n  background: #272a66;\n}\n\n.combined-button .anticon {\n  font-size: 16px;\n}\n.showbuilder-active\n{\n  height: calc(100% - 30px);\n}\n/* .react-flow__panel.bottom {\n  bottom: 30px;\n} */\n\n.needs-save .connected-status span {\n  background: #FF4D4F;\n}\n\n.query-status {\n  position: absolute;\n  top: -20px;\n  right: 0;\n  background: #4CAF50;\n  color: white;\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-size: 12px;\n}\n\n.back-btn {\n  width: 24px;\n  height: 24px;\n  border-radius: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 1px solid #cccccc;\n  background: #fff;\n  padding: 0;\n  /* position: absolute; */\n  margin: 5px;\n}\n\n.save_golden_batch{\n  display: flex;\n  gap: 20px;\n  margin-bottom: 15px;\n}\n\nbutton:disabled {\n  opacity: 0.5; /* Reduce opacity to indicate it's disabled */\n  cursor: not-allowed; /* Change the cursor to indicate it's disabled */\n}\n\n.custom-node .node-content {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n\n.custom-node .node-actions {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n}\n\n.custom-node .delete-icon {\n  opacity: 0.7;\n  transition: opacity 0.2s;\n}\n\n.custom-node .delete-icon:hover {\n  opacity: 1;\n}\n\n.highlighted-row {\n  position: relative;\n  background-color: #fdeb90 !important; /* Highlight color */\n}\n\n\n.highlighted-row  td {\n  /* background: none; */\n  background-color: #fdeb90 !important;\n  }\n  .rundeviation-table .ant-table-column-title{\n    text-align: center;\n    overflow: hidden;\n    /* width: fit-content; */\n\n  }\n  \n  .ant-table-cell:has(.run-deviation) {\n    margin-left: 2PX;\n    background-color: #ef3a3ad9 !important;\n  }\n\n  .ant-skeleton.ant-skeleton-element{\n    width: 100%;\n    height: 25px;\n  }\n  .ant-skeleton.ant-skeleton-element .ant-skeleton-input-lg{\n    width: 100%;\n    height: 25px;\n  }\n  .ant-form-item-required{\n    font-size: 16px !important;\n    font-weight: 500 !important;\n  }\n  .ant-select.ant-select-single{\n    height: 38px !important;\n  }\n\n\n .golden-values .ant-table-selection-column{\n  background-color: #9999995c !important;\n }\n\n .data-btn-group {\n  position: absolute;\n  right: -165px;\n  z-index: 1;\n  transform: rotate(90deg);\n  top: 257px;\n}\n \n.data-btn-group button{\n  border-radius: 0;\n  font-weight: 500;\n}\n \n.sidebar-collapse .ant-collapse{\n  border-radius: 0;\n}\n.data-btn-group button.active{\n  background: #252963;\n  border-color: #252963;\n  color: #fff;\n}\n\n.ant-btn.active:hover {\n  background: #252963 !important;\n  border-color: #252963;\n  color: #fff !important;\n}\n\n.ant-collapse .ant-collapse-content>.ant-collapse-content-box {\n  padding: 10px;\n}\n\n.outer-collapse {\n  border: none !important;\n}\n\n.outer-collapse.ant-collapse {\n  border: none !important;\n}\n\n.ant-modal-root .ant-modal-mask{\n  background-color: rgba(0, 0, 0, 0.45) !important;\n}\n\n.modebar{\n  background:rgba(0,0,0,0.5) !important;\n}\n\n.modebar-group{\n  background: transparent !important\n}", "/**\n * This file contains layout/structural styles only.\n *\n * To include decorative styles like colors and border styles,\n * import query-builder.scss instead.\n */\n\n// DO NOT ALTER OR REMOVE REGION NAMES. Some of them are used\n// to generate code snippets in the documentation.\n\n// #region Basic\n$rqb-spacing: 0.5rem !default;\n$rqb-border-width: 1px !default;\n// #endregion\n\n// #region Branches\n$rqb-branch-indent: $rqb-spacing !default;\n$rqb-branch-width: $rqb-border-width !default;\n// #endregion\n\n// Default styles\n.ruleGroup {\n  display: flex;\n  flex-direction: column;\n  gap: $rqb-spacing;\n  padding: $rqb-spacing;\n  border-width: $rqb-border-width;\n\n  .ruleGroup-body {\n    display: flex;\n    flex-direction: column;\n    gap: $rqb-spacing;\n\n    &:empty {\n      display: none;\n    }\n  }\n\n  .ruleGroup-header,\n  .rule {\n    display: flex;\n    gap: $rqb-spacing;\n    align-items: center;\n  }\n\n  .rule {\n    .rule-value:has(.rule-value-list-item) {\n      display: flex;\n      gap: $rqb-spacing;\n      align-items: baseline;\n    }\n  }\n\n  .betweenRules {\n    display: flex;\n  }\n\n  .shiftActions {\n    display: flex;\n    flex-direction: column;\n\n    & > * {\n      padding: 0;\n    }\n  }\n}\n\n// #region Drag-and-drop\n// Hover styles\n.dndOver {\n  &.rule,\n  &.ruleGroup-header {\n    padding-bottom: $rqb-spacing;\n  }\n  &.betweenRules {\n    padding-top: $rqb-spacing;\n  }\n}\n// #endregion\n\n// #region Branch styles\n.queryBuilder-branches {\n  .ruleGroup-body {\n    margin-left: calc(2 * #{$rqb-branch-indent});\n  }\n\n  .rule,\n  .ruleGroup .ruleGroup {\n    position: relative;\n\n    &::before,\n    &::after {\n      content: '';\n      width: $rqb-branch-indent;\n      left: calc(-#{$rqb-branch-indent} - #{$rqb-branch-width});\n      border-radius: 0;\n      position: absolute;\n    }\n\n    &::before {\n      top: -$rqb-spacing;\n      height: calc(50% + #{$rqb-spacing});\n      border-width: 0 0 $rqb-branch-width $rqb-branch-width;\n    }\n\n    &::after {\n      top: 50%;\n      height: 50%;\n      border-width: 0 0 0 $rqb-branch-width;\n    }\n\n    &:last-child::after {\n      display: none;\n    }\n  }\n\n  .ruleGroup .ruleGroup {\n    &::before,\n    &::after {\n      left: calc(calc(-#{$rqb-branch-indent} - #{$rqb-branch-width}) - #{$rqb-border-width});\n    }\n\n    &::before {\n      top: calc(-#{$rqb-spacing} - #{$rqb-border-width});\n      height: calc(50% + #{$rqb-spacing} + #{$rqb-border-width});\n    }\n\n    &::after {\n      height: calc(50% + #{$rqb-border-width});\n    }\n  }\n\n  .betweenRules {\n    position: relative;\n\n    &::before {\n      content: '';\n      width: $rqb-branch-indent;\n      left: calc(-#{$rqb-branch-indent} - #{$rqb-branch-width});\n      border-radius: 0;\n      position: absolute;\n      top: -$rqb-spacing;\n      height: calc(100% + #{$rqb-spacing});\n      border-width: 0 0 0 $rqb-branch-width;\n    }\n  }\n}\n// #endregion\n", "/**\n * This file incorporates layout/structural styles and adds\n * decorative styles like colors and border styles.\n *\n * To use layout/structural styles _only_, import\n * query-builder-layout.scss directly.\n */\n\n@use 'sass:map';\n\n@import './query-builder-layout.scss';\n\n// DO NOT ALTER OR REMOVE REGION NAMES. Some of them are used\n// to generate code snippets in the documentation.\n\n$rqb-dnd-drop-indicator-defaults: (\n  'color': rebeccapurple,\n  'copy-color': #669933,\n  'style': dashed,\n  'width': 2px,\n);\n\n/*\n\nNOTE: the commented code below is obviously not used, but it gets rendered on the documentation\nsite. It's a more easily readable reference, in close proximity to the real definition so as not\nto violate the \"one source of truth\" rule _too_ much.\n\n// #region Drag-and-drop\n$rqb-dnd-drop-indicator-color: rebeccapurple !default;\n$rqb-dnd-drop-indicator-copy-color: #669933 !default;\n$rqb-dnd-drop-indicator-style: dashed !default;\n$rqb-dnd-drop-indicator-width: 2px !default;\n\n// Deprecated variable names (still work)\n// $rqb-dnd-hover-border-bottom-color: rebeccapurple !default;\n// $rqb-dnd-hover-copy-border-bottom-color: #669933 !default;\n// $rqb-dnd-hover-border-bottom-style: dashed !default;\n// $rqb-dnd-hover-border-bottom-width: 2px !default;\n// #endregion\n\n*/\n\n// This function lets the user use an old variable name as long as the new\n// variable's default hasn't been overridden.\n@function prefer-new-var($old-var, $new-var, $map, $prop) {\n  // If the new variable is still the default, and the old variable\n  // does not equal the default (i.e., it's been overridden), return the\n  // old variable. Otherwise return the new variable.\n  @if $new-var == map.get($map, $prop) and $old-var != map.get($map, $prop) {\n    @return $old-var;\n  }\n\n  @return $new-var;\n}\n\n// #region Basic\n$rqb-background-color: rgba(0, 75, 183, 0.2) !default;\n$rqb-border-color: #8081a2 !default;\n$rqb-border-style: solid !default;\n$rqb-border-radius: 0.25rem !default;\n// #endregion\n\n// #region New drag-and-drop variables\n$rqb-dnd-drop-indicator-color: map.get($rqb-dnd-drop-indicator-defaults, 'color') !default;\n$rqb-dnd-drop-indicator-copy-color: map.get(\n  $rqb-dnd-drop-indicator-defaults,\n  'copy-color'\n) !default;\n$rqb-dnd-drop-indicator-style: map.get($rqb-dnd-drop-indicator-defaults, 'style') !default;\n$rqb-dnd-drop-indicator-width: map.get($rqb-dnd-drop-indicator-defaults, 'width') !default;\n// #endregion\n\n// #region Deprecated drag-and-drop variables\n$rqb-dnd-hover-border-bottom-color: map.get($rqb-dnd-drop-indicator-defaults, 'color') !default;\n$rqb-dnd-hover-copy-border-bottom-color: map.get(\n  $rqb-dnd-drop-indicator-defaults,\n  'copy-color'\n) !default;\n$rqb-dnd-hover-border-bottom-style: map.get($rqb-dnd-drop-indicator-defaults, 'style') !default;\n$rqb-dnd-hover-border-bottom-width: map.get($rqb-dnd-drop-indicator-defaults, 'width') !default;\n// #endregion\n\n// #region Branches\n$rqb-branch-color: $rqb-border-color !default;\n$rqb-branch-radius: $rqb-border-radius !default;\n$rqb-branch-style: $rqb-border-style !default;\n// #endregion\n\n// Default styles\n.ruleGroup {\n  border-color: $rqb-border-color;\n  border-style: $rqb-border-style;\n  border-radius: $rqb-border-radius;\n  background: $rqb-background-color;\n\n  .shiftActions {\n    & > * {\n      background-color: transparent;\n      border: none;\n      cursor: pointer;\n    }\n  }\n}\n\n// #region Drag-and-drop styles\n// Hover styles\n.dndOver {\n  &.rule,\n  &.ruleGroup-header {\n    border-bottom-width: prefer-new-var(\n      $rqb-dnd-hover-border-bottom-width,\n      $rqb-dnd-drop-indicator-width,\n      $rqb-dnd-drop-indicator-defaults,\n      'width'\n    );\n    border-bottom-style: prefer-new-var(\n      $rqb-dnd-hover-border-bottom-style,\n      $rqb-dnd-drop-indicator-style,\n      $rqb-dnd-drop-indicator-defaults,\n      'style'\n    );\n    border-bottom-color: prefer-new-var(\n      $rqb-dnd-hover-border-bottom-color,\n      $rqb-dnd-drop-indicator-color,\n      $rqb-dnd-drop-indicator-defaults,\n      'color'\n    );\n\n    &.dndCopy {\n      border-bottom-color: prefer-new-var(\n        $rqb-dnd-hover-copy-border-bottom-color,\n        $rqb-dnd-drop-indicator-copy-color,\n        $rqb-dnd-drop-indicator-defaults,\n        'copy-color'\n      );\n    }\n  }\n\n  &.betweenRules {\n    border-top-width: prefer-new-var(\n      $rqb-dnd-hover-border-bottom-width,\n      $rqb-dnd-drop-indicator-width,\n      $rqb-dnd-drop-indicator-defaults,\n      'width'\n    );\n    border-top-style: prefer-new-var(\n      $rqb-dnd-hover-border-bottom-style,\n      $rqb-dnd-drop-indicator-style,\n      $rqb-dnd-drop-indicator-defaults,\n      'style'\n    );\n    border-top-color: prefer-new-var(\n      $rqb-dnd-hover-border-bottom-color,\n      $rqb-dnd-drop-indicator-color,\n      $rqb-dnd-drop-indicator-defaults,\n      'color'\n    );\n\n    &.dndCopy {\n      border-top-color: prefer-new-var(\n        $rqb-dnd-hover-copy-border-bottom-color,\n        $rqb-dnd-drop-indicator-copy-color,\n        $rqb-dnd-drop-indicator-defaults,\n        'copy-color'\n      );\n    }\n  }\n}\n\n// Drag styles\n.ruleGroup,\n.rule {\n  &.dndDragging {\n    opacity: 0.5;\n  }\n\n  .queryBuilder-dragHandle {\n    cursor: move;\n  }\n}\n// #endregion\n\n// #region Branch styles\n.queryBuilder-branches {\n  .rule,\n  .ruleGroup .ruleGroup {\n    &::before,\n    &::after {\n      border-color: $rqb-branch-color;\n      border-style: $rqb-branch-style;\n    }\n\n    &:last-child::before {\n      border-bottom-left-radius: $rqb-branch-radius;\n    }\n  }\n\n  .betweenRules {\n    position: relative;\n\n    &::before {\n      border-color: $rqb-branch-color;\n      border-style: $rqb-branch-style;\n    }\n  }\n}\n// #endregion\n", ".blends-prediction-table {\n  background: white;\n  border-radius: 8px;\n  padding: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  margin: 16px 0;\n}\n\n.blends-table .ant-table {\n  font-size: 12px;\n}\n\n.blends-table .ant-table-thead > tr > th {\n  background-color: #f5f5f5;\n  font-weight: 600;\n  text-align: center;\n  padding: 8px 12px;\n  border-bottom: 2px solid #e8e8e8;\n}\n\n.blends-table .ant-table-tbody > tr > td {\n  padding: 8px 12px;\n  text-align: center;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.blends-table .ant-table-tbody > tr:hover > td {\n  background-color: #f9f9f9;\n}\n\n.blends-table .ant-table-tbody > tr:nth-child(even) {\n  background-color: #fafafa;\n}\n\n.blends-table .ant-table-tbody > tr:nth-child(even):hover > td {\n  background-color: #f5f5f5;\n}\n\n/* Animation for new rows */\n.blends-table .ant-table-tbody > tr {\n  animation: fadeIn 0.5s ease-in-out;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .blends-prediction-table {\n    padding: 12px;\n    margin: 12px 0;\n  }\n  \n  .blends-table .ant-table {\n    font-size: 11px;\n  }\n  \n  .blends-table .ant-table-thead > tr > th,\n  .blends-table .ant-table-tbody > tr > td {\n    padding: 6px 8px;\n  }\n}\n\n/* Loading state */\n.blends-table .ant-spin-container {\n  min-height: 200px;\n}\n\n/* Fixed columns styling */\n.blends-table .ant-table-fixed-left {\n  box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);\n}\n\n.blends-table .ant-table-thead > tr > th.ant-table-cell-fix-left {\n  background-color: #fafafa;\n}\n\n.blends-table .ant-table-tbody > tr > td.ant-table-cell-fix-left {\n  background-color: white;\n}\n\n.blends-table .ant-table-tbody > tr:hover > td.ant-table-cell-fix-left {\n  background-color: #f9f9f9;\n}\n\n.blends-table .ant-table-tbody > tr:nth-child(even) > td.ant-table-cell-fix-left {\n  background-color: #fafafa;\n}\n\n.blends-table .ant-table-tbody > tr:nth-child(even):hover > td.ant-table-cell-fix-left {\n  background-color: #f5f5f5;\n}\n", ".anomaly-detection-demo {\n  background: #f5f5f5;\n  min-height: 100vh;\n}\n\n.anomaly-table-container {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  margin: 16px 0;\n}\n\n.anomaly-table .ant-table {\n  font-size: 13px;\n}\n\n.anomaly-table .ant-table-thead > tr > th {\n  background-color: #f8f9fa;\n  font-weight: 600;\n  text-align: center;\n  padding: 12px 16px;\n  border-bottom: 2px solid #e8e8e8;\n  color: #333;\n}\n\n.anomaly-table .ant-table-tbody > tr > td {\n  padding: 12px 16px;\n  text-align: center;\n  border-bottom: 1px solid #f0f0f0;\n  vertical-align: middle;\n}\n\n/* Normal row styling */\n.anomaly-table .normal-row {\n  background-color: white;\n  transition: background-color 0.3s ease;\n}\n\n.anomaly-table .normal-row:hover > td {\n  background-color: #f9f9f9;\n}\n\n/* Anomalous row styling - red highlighting */\n.anomaly-table .anomaly-row {\n  background-color: #fef2f2 !important;\n  border-left: 4px solid #ef4444;\n  transition: background-color 0.3s ease;\n}\n\n.anomaly-table .anomaly-row:hover > td {\n  background-color: #fee2e2 !important;\n}\n\n.anomaly-table .anomaly-row > td {\n  background-color: #fef2f2 !important;\n  border-bottom: 1px solid #fecaca;\n}\n\n/* Animation for new rows */\n.anomaly-table .ant-table-tbody > tr {\n  animation: slideInFromTop 0.5s ease-out;\n}\n\n@keyframes slideInFromTop {\n  from {\n    opacity: 0;\n    transform: translateY(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Score badge styling */\n.anomaly-table .ant-table-tbody .bg-red-100 {\n  background-color: #fee2e2 !important;\n  color: #dc2626 !important;\n  border: 1px solid #fca5a5 !important;\n  font-weight: 600;\n  animation: pulse 2s infinite;\n}\n\n.anomaly-table .ant-table-tbody .bg-green-100 {\n  background-color: #dcfce7 !important;\n  color: #16a34a !important;\n  border: 1px solid #86efac !important;\n  font-weight: 500;\n}\n\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.8;\n  }\n}\n\n/* Fixed columns styling */\n.anomaly-table .ant-table-fixed-left {\n  box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);\n}\n\n.anomaly-table .ant-table-thead > tr > th.ant-table-cell-fix-left {\n  background-color: #f8f9fa;\n}\n\n.anomaly-table .ant-table-tbody > tr > td.ant-table-cell-fix-left {\n  background-color: white;\n}\n\n.anomaly-table .anomaly-row > td.ant-table-cell-fix-left {\n  background-color: #fef2f2 !important;\n}\n\n/* Loading state */\n.anomaly-table .ant-spin-container {\n  min-height: 300px;\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .anomaly-table-container {\n    padding: 16px;\n    margin: 12px 0;\n  }\n  \n  .anomaly-table .ant-table {\n    font-size: 12px;\n  }\n  \n  .anomaly-table .ant-table-thead > tr > th,\n  .anomaly-table .ant-table-tbody > tr > td {\n    padding: 8px 12px;\n  }\n}\n\n/* Status indicators */\n.status-indicator {\n  display: inline-flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 16px;\n}\n\n.status-indicator .indicator-dot {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n}\n\n.status-indicator .normal-dot {\n  background-color: #10b981;\n}\n\n.status-indicator .anomaly-dot {\n  background-color: #ef4444;\n  animation: blink 1s infinite;\n}\n\n@keyframes blink {\n  0%, 50% {\n    opacity: 1;\n  }\n  51%, 100% {\n    opacity: 0.3;\n  }\n}\n\n/* Header styling */\n.anomaly-detection-demo h2 {\n  color: #1f2937;\n  margin-bottom: 8px;\n}\n\n.anomaly-detection-demo p {\n  color: #6b7280;\n  line-height: 1.5;\n}\n\n/* Table scroll styling */\n.anomaly-table .ant-table-body {\n  scrollbar-width: thin;\n  scrollbar-color: #cbd5e0 #f7fafc;\n}\n\n.anomaly-table .ant-table-body::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n\n.anomaly-table .ant-table-body::-webkit-scrollbar-track {\n  background: #f7fafc;\n  border-radius: 4px;\n}\n\n.anomaly-table .ant-table-body::-webkit-scrollbar-thumb {\n  background: #cbd5e0;\n  border-radius: 4px;\n}\n\n.anomaly-table .ant-table-body::-webkit-scrollbar-thumb:hover {\n  background: #a0aec0;\n}\n\n/* Empty state styling */\n.anomaly-table .ant-empty {\n  padding: 40px 20px;\n}\n\n.anomaly-table .ant-empty-description {\n  color: #6b7280;\n}\n", ".carbon-demo-prediction {\n  background: #f5f5f5;\n  min-height: 100vh;\n}\n\n.carbon-black-table .ant-table {\n  font-size: 13px;\n}\n\n.carbon-black-table .ant-table-thead > tr > th {\n  background-color: #f0f2f5;\n  font-weight: 600;\n  text-align: center;\n  padding: 12px 16px;\n  border-bottom: 2px solid #d9d9d9;\n}\n\n.carbon-black-table .ant-table-tbody > tr > td {\n  padding: 12px 16px;\n  text-align: center;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.carbon-black-table .ant-table-tbody > tr:hover > td {\n  background-color: #f9f9f9;\n}\n\n.carbon-black-table .ant-table-tbody > tr:nth-child(even) {\n  background-color: #fafafa;\n}\n\n.carbon-black-table .ant-table-tbody > tr:nth-child(even):hover > td {\n  background-color: #f5f5f5;\n}\n\n/* Animation for new rows */\n.carbon-black-table .ant-table-tbody > tr {\n  animation: slideInFromTop 0.6s ease-out;\n}\n\n@keyframes slideInFromTop {\n  from {\n    opacity: 0;\n    transform: translateY(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Status indicators */\n.carbon-demo-prediction .ant-card {\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  border-radius: 8px;\n}\n\n.carbon-demo-prediction .ant-card-head {\n  border-bottom: 1px solid #e8e8e8;\n}\n\n/* Button styling */\n.carbon-demo-prediction .ant-btn {\n  border-radius: 6px;\n  font-weight: 500;\n}\n\n.carbon-demo-prediction .ant-btn-primary {\n  background: #1890ff;\n  border-color: #1890ff;\n}\n\n.carbon-demo-prediction .ant-btn-primary:hover {\n  background: #40a9ff;\n  border-color: #40a9ff;\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .carbon-demo-prediction {\n    padding: 12px;\n  }\n  \n  .carbon-black-table .ant-table {\n    font-size: 12px;\n  }\n  \n  .carbon-black-table .ant-table-thead > tr > th,\n  .carbon-black-table .ant-table-tbody > tr > td {\n    padding: 8px 12px;\n  }\n  \n  .carbon-demo-prediction .flex {\n    flex-direction: column;\n    gap: 12px;\n  }\n  \n  .carbon-demo-prediction .flex.items-center {\n    align-items: flex-start;\n  }\n}\n\n/* Loading state */\n.carbon-black-table .ant-spin-container {\n  min-height: 300px;\n}\n\n/* Table pagination */\n.carbon-black-table .ant-pagination {\n  margin-top: 16px;\n  text-align: center;\n}\n\n/* Error state */\n.carbon-demo-prediction .bg-red-50 {\n  background-color: #fef2f2;\n  border: 1px solid #fecaca;\n  border-radius: 6px;\n}\n\n.carbon-demo-prediction .text-red-600 {\n  color: #dc2626;\n}\n\n/* Success indicators */\n.carbon-demo-prediction .text-green-600 {\n  color: #16a34a;\n}\n\n.carbon-demo-prediction .text-blue-600 {\n  color: #2563eb;\n}\n\n/* Typography */\n.carbon-demo-prediction .font-mono {\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n}\n\n.carbon-demo-prediction .font-semibold {\n  font-weight: 600;\n}\n\n.carbon-demo-prediction .font-medium {\n  font-weight: 500;\n}\n", "/* Grid Container */\n.grid-container {\n  width: 100%;\n  height: 100%;\n  background-color: #f5f5f5;\n  border-radius: 4px;\n  overflow: auto;\n  position: relative;\n}\n\n/* Drop Indicator */\n.drop-indicator {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  padding: 20px;\n  background-color: rgba(255, 255, 255, 0.8);\n  border: 2px dashed #ccc;\n  border-radius: 8px;\n  text-align: center;\n  color: #666;\n  font-size: 16px;\n  z-index: 10;\n}\n\n/* Grid Item */\n.grid-item {\n  width: 100%;\n  height: 100%;\n  background-color: white;\n  border-radius: 4px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n/* Grid Item Header */\n.grid-item-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 12px;\n  background-color: #f0f0f0;\n  border-bottom: 1px solid #ddd;\n}\n\n.grid-item-title {\n  font-weight: 500;\n  font-size: 14px;\n  cursor: move;\n  flex-grow: 1;\n}\n\n.grid-item-controls {\n  display: flex;\n  gap: 4px;\n}\n\n.grid-item-control {\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 2px 4px;\n  font-size: 12px;\n  color: #666;\n  border-radius: 2px;\n}\n\n.grid-item-control:hover {\n  background-color: #e0e0e0;\n}\n\n/* Expand button */\n.grid-item-control .anticon-expand-outlined {\n  color: #1890ff;\n  font-size: 14px;\n}\n\n/* Expand button hover effect */\n.grid-item-control:hover .anticon-expand-outlined {\n  color: #40a9ff;\n  transform: scale(1.1);\n}\n\n/* Panel Options Menu */\n.panel-options-button {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 2px 4px;\n  font-size: 16px;\n  color: #666;\n  border-radius: 2px;\n  height: 24px;\n  width: 24px;\n}\n\n.panel-options-button:hover {\n  background-color: #e0e0e0;\n}\n\n/* Grid Item Content */\n.grid-item-content {\n  flex-grow: 1;\n  overflow: auto;\n  padding: 8px;\n}\n\n/* Fullscreen Mode */\n.grid-item.fullscreen {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  height: 100vh;\n  z-index: 1000;\n}\n\n/* Drag Handle */\n.drag-handle {\n  cursor: move;\n}\n\n/* No Drag Area */\n.no-drag {\n  cursor: default;\n}\n\n/* Component List */\n.component-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.component-item {\n  padding: 8px 12px;\n  margin-bottom: 8px;\n  background-color: white;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: background-color 0.2s;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.component-item:hover {\n  background-color: #f5f5f5;\n}\n\n.component-item.disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.added-badge {\n  font-size: 10px;\n  padding: 2px 4px;\n  background-color: #e0e0e0;\n  border-radius: 4px;\n  color: #666;\n}\n\n/* Selected Row/Column Highlighting */\n.selected-row {\n  background-color: rgba(100, 108, 255, 0.1) !important;\n}\n\n.selected-column {\n  background-color: rgba(100, 108, 255, 0.1) !important;\n}\n\n/* Interactive Content */\n.interactive-content {\n  width: 100%;\n  height: 100%;\n}\n\n/* Statistics Panel */\n.statistics-panel {\n  padding: 16px;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 16px;\n}\n\n.stat-item {\n  background-color: #f5f5f5;\n  border-radius: 4px;\n  padding: 12px;\n  text-align: center;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 4px;\n}\n\n.stat-value {\n  font-size: 18px;\n  font-weight: 500;\n}\n\n/* Date Filter Panel */\n.date-filter-panel {\n  margin-bottom: 12px;\n}\n\n/* View Sidebar */\n.view-sidebar {\n  height: 100%;\n  border-right: 1px solid #e0e0e0;\n}\n\n/* View Content */\n.view-content {\n  height: calc(100vh - 85px);\n}\n\n/* Applied Filters */\n.applied-filters {\n  padding: 8px 0;\n}\n\n.applied-filters .ant-tag {\n  margin-right: 8px;\n  margin-bottom: 8px;\n  padding: 4px 8px;\n  display: inline-flex;\n  align-items: center;\n  font-size: 12px;\n}\n\n.applied-filters .ant-tag .anticon-close {\n  margin-left: 4px;\n  font-size: 10px;\n}\n\n.applied-filters .ant-tag-blue {\n  background-color: rgba(24, 144, 255, 0.1);\n  border-color: #1890ff;\n  color: #1890ff;\n}\n\n.applied-filters .ant-tag-green {\n  background-color: rgba(82, 196, 26, 0.1);\n  border-color: #52c41a;\n  color: #52c41a;\n}\n\n.applied-filters .ant-tag-purple {\n  background-color: rgba(114, 46, 209, 0.1);\n  border-color: #722ed1;\n  color: #722ed1;\n}\n\n\n", "/*!\n * Copyright (c) HANDSONCODE sp. z o. o.\n *\n * HANDSONTABLE is a software distributed by HANDSONCODE sp. z o. o., a Polish corporation based in\n * Gdynia, Poland, at Aleja Zwyciestwa 96-98, registered by the District Court in Gdansk under number\n * 538651, EU tax ID number: PL5862294002, share capital: PLN 62,800.00.\n *\n * This software is protected by applicable copyright laws, including international treaties, and dual-\n * licensed - depending on whether your use for commercial purposes, meaning intended for or\n * resulting in commercial advantage or monetary compensation, or not.\n *\n * If your use is strictly personal or solely for evaluation purposes, meaning for the purposes of testing\n * the suitability, performance, and usefulness of this software outside the production environment,\n * you agree to be bound by the terms included in the \"handsontable-non-commercial-license.pdf\" file.\n *\n * Your use of this software for commercial purposes is subject to the terms included in an applicable\n * license agreement.\n *\n * In any case, you must not make any such use of this software as to develop software which may be\n * considered competitive with this software.\n *\n * UNLESS EXPRESSLY AGREED OTHERWISE, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> PROVIDES THIS SOFTWARE ON AN \"AS IS\"\n * BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, AND IN NO EVENT AND UNDER NO\n * LEGAL THEORY, SHALL HANDSONCODE BE LIABLE TO YOU FOR DAMAGES, INCLUDING ANY DIRECT,\n * INDIRECT, SPECIAL, INCIDENTAL, OR CONSEQUENTIAL DAMAGES OF ANY CHARACTER ARISING FROM\n * USE OR INABILITY TO USE THIS SOFTWARE.\n *\n * Version: 15.0.0\n * Release date: 16/12/2024 (built at 16/12/2024 13:19:50)\n */\n/**\n * Fix for bootstrap styles\n */\n .handsontable .table th, .handsontable .table td {\n  border-top: none;\n}\n\n.handsontable tr {\n  background: #fff;\n}\n\n.handsontable td {\n  background-color: inherit;\n}\n\n.handsontable .table caption + thead tr:first-child th,\n.handsontable .table caption + thead tr:first-child td,\n.handsontable .table colgroup + thead tr:first-child th,\n.handsontable .table colgroup + thead tr:first-child td,\n.handsontable .table thead:first-child tr:first-child th,\n.handsontable .table thead:first-child tr:first-child td {\n  border-top: 1px solid #CCCCCC;\n}\n\n/* table-bordered */\n.handsontable .table-bordered {\n  border: 0;\n  border-collapse: separate;\n}\n\n.handsontable .table-bordered th,\n.handsontable .table-bordered td {\n  border-left: none;\n}\n\n.handsontable .table-bordered th:first-child,\n.handsontable .table-bordered td:first-child {\n  border-left: 1px solid #CCCCCC;\n}\n\n.handsontable .table > tbody > tr > td,\n.handsontable .table > tbody > tr > th,\n.handsontable .table > tfoot > tr > td,\n.handsontable .table > tfoot > tr > th,\n.handsontable .table > thead > tr > td,\n.handsontable .table > thead > tr > th {\n  line-height: 21px;\n  padding: 0;\n}\n\n.col-lg-1.handsontable, .col-lg-10.handsontable, .col-lg-11.handsontable, .col-lg-12.handsontable,\n.col-lg-2.handsontable, .col-lg-3.handsontable, .col-lg-4.handsontable, .col-lg-5.handsontable, .col-lg-6.handsontable, .col-lg-7.handsontable, .col-lg-8.handsontable, .col-lg-9.handsontable,\n.col-md-1.handsontable, .col-md-10.handsontable, .col-md-11.handsontable, .col-md-12.handsontable,\n.col-md-2.handsontable, .col-md-3.handsontable, .col-md-4.handsontable, .col-md-5.handsontable, .col-md-6.handsontable, .col-md-7.handsontable, .col-md-8.handsontable, .col-md-9.handsontable .col-sm-1.handsontable,\n.col-sm-10.handsontable, .col-sm-11.handsontable, .col-sm-12.handsontable,\n.col-sm-2.handsontable, .col-sm-3.handsontable, .col-sm-4.handsontable, .col-sm-5.handsontable, .col-sm-6.handsontable, .col-sm-7.handsontable, .col-sm-8.handsontable, .col-sm-9.handsontable .col-xs-1.handsontable,\n.col-xs-10.handsontable, .col-xs-11.handsontable, .col-xs-12.handsontable,\n.col-xs-2.handsontable, .col-xs-3.handsontable, .col-xs-4.handsontable, .col-xs-5.handsontable, .col-xs-6.handsontable, .col-xs-7.handsontable, .col-xs-8.handsontable, .col-xs-9.handsontable {\n  padding-left: 0;\n  padding-right: 0;\n}\n\n.handsontable .table-striped > tbody > tr:nth-of-type(even) {\n  background-color: #FFF;\n}\n\n.handsontable .hide {\n  display: none;\n}\n\n.handsontable .relative {\n  position: relative;\n}\n\n.handsontable .wtHider {\n  position: relative;\n  width: 0;\n}\n\n.handsontable .wtSpreader {\n  position: relative;\n  /* must be 0, otherwise blank space appears in scroll demo after scrolling max to the right */\n  width: 0;\n  height: auto;\n}\n\n.handsontable table,\n.handsontable tbody,\n.handsontable thead,\n.handsontable td,\n.handsontable th,\n.handsontable input,\n.handsontable textarea,\n.handsontable div {\n  box-sizing: content-box;\n  -webkit-box-sizing: content-box;\n  -moz-box-sizing: content-box;\n}\n\n.handsontable input,\n.handsontable textarea {\n  min-height: initial;\n}\n\n.handsontable table.htCore {\n  border-collapse: separate;\n  /* it must be separate, otherwise there are offset miscalculations in WebKit: http://stackoverflow.com/questions/2655987/border-collapse-differences-in-ff-and-webkit */\n  /* this actually only changes appearance of user selection - does not make text unselectable */\n  /* -webkit-user-select: none;\n    -khtml-user-select: none;\n    -moz-user-select: none;\n    -o-user-select: none;\n    -ms-user-select: none;\n    user-select: none; // no browser supports unprefixed version\n  */\n  border-spacing: 0;\n  margin: 0;\n  border-width: 0;\n  table-layout: fixed;\n  width: 0;\n  outline-width: 0;\n  cursor: default;\n  /* reset bootstrap table style. for more info see: https://github.com/handsontable/handsontable/issues/224 */\n  max-width: none;\n  max-height: none;\n}\n\n.handsontable col {\n  width: 50px;\n}\n\n.handsontable col.rowHeader {\n  width: 50px;\n}\n\n.handsontable th,\n.handsontable td {\n  border-top-width: 0;\n  border-left-width: 0;\n  border-right: 1px solid #ccc;\n  border-bottom: 1px solid #ccc;\n  height: 22px;\n  empty-cells: show;\n  line-height: 21px;\n  padding: 0 4px 0 4px;\n  /* top, bottom padding different than 0 is handled poorly by FF with HTML5 doctype */\n  background-color: #fff;\n  vertical-align: top;\n  overflow: hidden;\n  outline: none;\n  outline-width: 0;\n  white-space: pre-wrap;\n}\n\n[dir=rtl].handsontable th, [dir=rtl].handsontable td {\n  border-right-width: 0;\n  border-left: 1px solid #ccc;\n}\n\n.handsontable th:last-child {\n  /* Foundation framework fix */\n  border-left: none;\n  border-right: 1px solid #ccc;\n  border-bottom: 1px solid #ccc;\n}\n\n[dir=rtl].handsontable th:last-child {\n  /* Foundation framework fix */\n  border-right: none;\n  border-left: 1px solid #ccc;\n}\n\n.handsontable th:first-child,\n.handsontable td:first-of-type {\n  border-left: 1px solid #ccc;\n}\n\n[dir=rtl].handsontable th:first-child, [dir=rtl].handsontable td:first-of-type {\n  border-right: 1px solid #ccc;\n}\n\n/* It removes double right border from first column header when row headers are disabled */\n.handsontable .ht_clone_top th:nth-child(2) {\n  border-left-width: 0;\n  border-right: 1px solid #ccc;\n}\n\n[dir=rtl].handsontable .ht_clone_top th:nth-child(2) {\n  border-right-width: 0;\n  border-left: 1px solid #ccc;\n}\n\n.handsontable.htRowHeaders thead tr th:nth-child(2) {\n  border-left: 1px solid #ccc;\n}\n\n[dir=rtl].handsontable.htRowHeaders thead tr th:nth-child(2) {\n  border-right: 1px solid #ccc;\n}\n\n.handsontable tr:first-child th,\n.handsontable tr:first-child td {\n  border-top: 1px solid #ccc;\n}\n\n.ht_master:not(.innerBorderInlineStart):not(.emptyColumns) tbody tr th,\n.ht_master:not(.innerBorderInlineStart):not(.emptyColumns) thead tr th:first-child,\n.ht_master:not(.innerBorderInlineStart):not(.emptyColumns) ~ .handsontable:not(.htGhostTable) tbody tr th,\n.ht_master:not(.innerBorderInlineStart):not(.emptyColumns) ~ .handsontable:not(.ht_clone_top):not(.htGhostTable) thead tr th:first-child {\n  border-right-width: 0;\n  border-left: 1px solid #ccc;\n}\n\n[dir=rtl].ht_master:not(.innerBorderInlineStart):not(.emptyColumns) tbody tr th, [dir=rtl].ht_master:not(.innerBorderInlineStart):not(.emptyColumns) thead tr th:first-child, [dir=rtl].ht_master:not(.innerBorderInlineStart):not(.emptyColumns) ~ .handsontable:not(.htGhostTable) tbody tr th, [dir=rtl].ht_master:not(.innerBorderInlineStart):not(.emptyColumns) ~ .handsontable:not(.ht_clone_top):not(.htGhostTable) thead tr th:first-child {\n  border-left-width: 0;\n  border-right: 1px solid #ccc;\n}\n\n/*\ninnerBorderTop - Property controlled by top overlay\ninnerBorderBottom - Property controlled by bottom overlay\n */\n.ht_master:not(.innerBorderTop):not(.innerBorderBottom) thead tr:last-child th,\n.ht_master:not(.innerBorderTop):not(.innerBorderBottom) ~ .handsontable thead tr:last-child th,\n.ht_master:not(.innerBorderTop):not(.innerBorderBottom) thead tr.lastChild th,\n.ht_master:not(.innerBorderTop):not(.innerBorderBottom) ~ .handsontable thead tr.lastChild th {\n  border-bottom-width: 0;\n}\n\n.handsontable th {\n  background-color: #f0f0f0;\n  color: #222;\n  text-align: center;\n  font-weight: normal;\n  white-space: nowrap;\n}\n\n.handsontable thead th {\n  padding: 0;\n}\n\n.handsontable th.active {\n  background-color: #ccc;\n}\n\n.handsontable thead th .relative {\n  padding: 2px 4px;\n}\n\n.handsontable span.colHeader {\n  display: inline-block;\n  line-height: 1.1;\n}\n\n/* Selection */\n.handsontable .wtBorder {\n  position: absolute;\n  font-size: 0;\n}\n\n.handsontable .wtBorder.hidden {\n  display: none !important;\n}\n\n/* A layer order of the selection types */\n.handsontable .wtBorder.current {\n  z-index: 10;\n}\n\n.handsontable .wtBorder.area {\n  z-index: 8;\n}\n\n.handsontable .wtBorder.fill {\n  z-index: 6;\n}\n\n/* fill handle */\n.handsontable .wtBorder.corner {\n  font-size: 0;\n  cursor: crosshair;\n}\n\n.ht_clone_master {\n  z-index: 100;\n}\n\n.ht_clone_inline_start {\n  z-index: 120;\n}\n\n.ht_clone_bottom {\n  z-index: 130;\n}\n\n.ht_clone_bottom_inline_start_corner {\n  z-index: 150;\n}\n\n.ht_clone_top {\n  z-index: 160;\n}\n\n.ht_clone_top_inline_start_corner {\n  z-index: 180;\n}\n\n.handsontable col.hidden {\n  width: 0 !important;\n}\n\n.handsontable tr.hidden,\n.handsontable tr.hidden td,\n.handsontable tr.hidden th {\n  display: none;\n}\n\n.ht_master,\n.ht_clone_inline_start,\n.ht_clone_top,\n.ht_clone_bottom {\n  overflow: hidden;\n}\n\n.ht_master .wtHolder {\n  overflow: auto;\n}\n\n.handsontable .ht_master table.htCore > thead,\n.handsontable .ht_master table.htCore > tbody > tr > th,\n.handsontable .ht_clone_inline_start table.htCore > thead {\n  visibility: hidden;\n}\n\n.ht_clone_top .wtHolder,\n.ht_clone_inline_start .wtHolder,\n.ht_clone_bottom .wtHolder {\n  overflow: hidden;\n}\n\n@charset \"UTF-8\";\n\n.handsontable {\n  position: relative;\n  touch-action: manipulation;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Roboto\", \"Oxygen\", \"Ubuntu\", \"Helvetica Neue\", Arial, sans-serif;\n  font-size: 13px;\n  font-weight: normal;\n  color: #373737;\n}\n\n.handsontable a {\n  color: #104acc;\n}\n\n.handsontable.htAutoSize {\n  visibility: hidden;\n  left: -99000px;\n  position: absolute;\n  top: -99000px;\n}\n\n.handsontable td.htInvalid {\n  /* gives priority over td.area selection background */\n  background-color: #ffbeba !important;\n}\n\n.handsontable td.htNoWrap {\n  white-space: nowrap;\n}\n\n.handsontable td.invisibleSelection,\n.handsontable th.invisibleSelection {\n  outline: none;\n}\n\n.handsontable td.invisibleSelection::selection,\n.handsontable th.invisibleSelection::selection {\n  background: rgba(255, 255, 255, 0);\n}\n\n.hot-display-license-info {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Roboto\", \"Oxygen\", \"Ubuntu\", \"Helvetica Neue\", Arial, sans-serif;\n  font-size: 10px;\n  font-weight: normal;\n  color: #373737;\n  padding: 5px 0 3px 0;\n  text-align: left;\n}\n\n.hot-display-license-info a {\n  color: #104acc;\n  font-size: 10px;\n}\n\n.handsontable .htFocusCatcher {\n  position: absolute;\n  z-index: -1;\n  opacity: 0;\n  border: 0;\n  margin: 0;\n  padding: 0;\n  width: 0;\n  height: 0;\n}\n\n/* plugins */\n/* row + column resizer */\n.handsontable .manualColumnResizer {\n  position: absolute;\n  top: 0;\n  cursor: col-resize;\n  z-index: 210;\n  width: 5px;\n  height: 25px;\n}\n\n.handsontable .manualRowResizer {\n  position: absolute;\n  left: 0;\n  cursor: row-resize;\n  z-index: 210;\n  height: 5px;\n  width: 50px;\n}\n\n.handsontable .manualColumnResizer:hover,\n.handsontable .manualColumnResizer.active,\n.handsontable .manualRowResizer:hover,\n.handsontable .manualRowResizer.active {\n  background-color: #34a9db;\n}\n\n.handsontable .manualColumnResizerGuide {\n  position: absolute;\n  right: unset;\n  top: 0;\n  background-color: #34a9db;\n  display: none;\n  width: 0;\n  border-right: 1px dashed #777;\n  border-left: none;\n  margin-left: 5px;\n  margin-right: unset;\n}\n\n[dir=rtl].handsontable .manualColumnResizerGuide {\n  left: unset;\n  border-left: 1px dashed #777;\n  border-right: none;\n  margin-right: 5px;\n  margin-left: unset;\n}\n\n.handsontable .manualRowResizerGuide {\n  position: absolute;\n  left: 0;\n  bottom: 0;\n  background-color: #34a9db;\n  display: none;\n  height: 0;\n  border-bottom: 1px dashed #777;\n  margin-top: 5px;\n}\n\n.handsontable .manualColumnResizerGuide.active,\n.handsontable .manualRowResizerGuide.active {\n  display: block;\n  z-index: 209;\n}\n\n.handsontable td.area,\n.handsontable td.area-1,\n.handsontable td.area-2,\n.handsontable td.area-3,\n.handsontable td.area-4,\n.handsontable td.area-5,\n.handsontable td.area-6,\n.handsontable td.area-7 {\n  position: relative;\n}\n\n.handsontable td.area::before,\n.handsontable td.area-1::before,\n.handsontable td.area-2::before,\n.handsontable td.area-3::before,\n.handsontable td.area-4::before,\n.handsontable td.area-5::before,\n.handsontable td.area-6::before,\n.handsontable td.area-7::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: #005eff;\n}\n\n.handsontable td.area::before {\n  opacity: 0.1;\n}\n\n.handsontable td.area-1::before {\n  opacity: 0.2;\n}\n\n.handsontable td.area-2::before {\n  opacity: 0.27;\n}\n\n.handsontable td.area-3::before {\n  opacity: 0.35;\n}\n\n.handsontable td.area-4::before {\n  opacity: 0.41;\n}\n\n.handsontable td.area-5::before {\n  opacity: 0.47;\n}\n\n.handsontable td.area-6::before {\n  opacity: 0.54;\n}\n\n.handsontable td.area-7::before {\n  opacity: 0.58;\n}\n\n.handsontable tbody th.current,\n.handsontable thead th.current {\n  box-shadow: inset 0 0 0 2px #4b89ff;\n}\n\n.handsontable tbody th.ht__highlight,\n.handsontable thead th.ht__highlight {\n  background-color: #dcdcdc;\n}\n\n.handsontable tbody th.ht__active_highlight,\n.handsontable thead th.ht__active_highlight {\n  background-color: #8eb0e7;\n  color: #000;\n}\n\n.handsontableInput {\n  border: none;\n  outline-width: 0;\n  margin: 0;\n  padding: 1px 5px 0 5px;\n  font-family: inherit;\n  line-height: 21px;\n  font-size: inherit;\n  box-shadow: 0 0 0 2px #5292F7 inset;\n  resize: none;\n  /* below are needed to overwrite stuff added by jQuery UI Bootstrap theme */\n  display: block;\n  color: #000;\n  border-radius: 0;\n  background-color: #FFF;\n  box-sizing: border-box !important;\n  /* overwrite styles potentionally made by a framework */\n}\n\n.handsontableInput:focus {\n  outline: none;\n}\n\n.handsontableInputHolder {\n  position: absolute;\n  top: 0;\n  left: 0;\n}\n\n.htSelectEditor {\n  position: absolute;\n\n  select {\n    -webkit-appearance: menulist-button !important;\n    width: 100%;\n    height: 100%;\n    border: 2px solid #4b89ff;\n    box-sizing: border-box !important;\n  }\n}\n\n.htSelectEditor select:focus {\n  outline: none;\n}\n\n.htSelectEditor .htAutocompleteArrow {\n  display: none;\n}\n\n/*\nTextRenderer readOnly cell\n*/\n.handsontable .htDimmed {\n  color: #777;\n}\n\n.handsontable .htSubmenu {\n  position: relative;\n}\n\n.handsontable .htSubmenu ::after {\n  content: \"▶\";\n  color: #777;\n  position: absolute;\n  right: 5px;\n  font-size: 9px;\n}\n\n[dir=rtl].handsontable .htSubmenu ::after {\n  content: \"\";\n}\n\n[dir=rtl].handsontable .htSubmenu ::before {\n  content: \"◀\";\n  color: #777;\n  position: absolute;\n  left: 5px;\n  font-size: 9px;\n}\n\n/*\nTextRenderer horizontal alignment\n*/\n.handsontable .htLeft {\n  text-align: left;\n}\n\n.handsontable .htCenter {\n  text-align: center;\n}\n\n.handsontable .htRight {\n  text-align: right;\n}\n\n.handsontable .htJustify {\n  text-align: justify;\n}\n\n/*\nTextRenderer vertical alignment\n*/\n.handsontable .htTop {\n  vertical-align: top;\n}\n\n.handsontable .htMiddle {\n  vertical-align: middle;\n}\n\n.handsontable .htBottom {\n  vertical-align: bottom;\n}\n\n/*\nTextRenderer placeholder value\n*/\n.handsontable .htPlaceholder {\n  color: #999;\n}\n\n/**\n * Handsontable listbox theme\n */\n.handsontable.listbox {\n  margin: 0;\n}\n\n.handsontable.listbox .ht_master table {\n  border: 1px solid #ccc;\n  border-collapse: separate;\n  background: white;\n}\n\n.handsontable.listbox th,\n.handsontable.listbox tr:first-child th,\n.handsontable.listbox tr:last-child th,\n.handsontable.listbox tr:first-child td,\n.handsontable.listbox td {\n  border-color: transparent !important;\n}\n\n.handsontable.listbox th,\n.handsontable.listbox td {\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n\n.handsontable.listbox td.htDimmed {\n  cursor: default;\n  color: inherit;\n  font-style: inherit;\n}\n\n.handsontable.listbox .wtBorder {\n  visibility: hidden;\n}\n\n.handsontable.listbox tr td.current,\n.handsontable.listbox tr:hover td {\n  background: #eee;\n}\n\n.ht_editor_hidden {\n  z-index: -1;\n}\n\n.ht_editor_visible {\n  z-index: 200;\n}\n\n.handsontable td.htSearchResult {\n  background: #fcedd9;\n  color: #583707;\n}\n/*\n\n Handsontable Mobile Text Editor stylesheet\n\n */\n.handsontable.mobile,\n.handsontable.mobile .wtHolder {\n  -webkit-touch-callout: none;\n  -webkit-user-select: none;\n  -khtml-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n  -webkit-overflow-scrolling: touch;\n}\n\n.handsontable.mobile .handsontableInput:focus {\n  -webkit-box-shadow: 0 0 0 2px #5292f7 inset;\n  -moz-box-shadow: 0 0 0 2px #5292f7 inset;\n  box-shadow: 0 0 0 2px #5292f7 inset;\n  -webkit-appearance: none;\n}\n\n/* Initial left/top coordinates - overwritten when actual position is set */\n.handsontable .topSelectionHandle,\n.handsontable .topSelectionHandle-HitArea,\n.handsontable .bottomSelectionHandle,\n.handsontable .bottomSelectionHandle-HitArea {\n  left: -10000px;\n  right: unset;\n  top: -10000px;\n  z-index: 9999;\n}\n\n[dir=rtl].handsontable .topSelectionHandle, [dir=rtl].handsontable .topSelectionHandle-HitArea, [dir=rtl].handsontable .bottomSelectionHandle, [dir=rtl].handsontable .bottomSelectionHandle-HitArea {\n  right: -10000px;\n  left: unset;\n}\n\n.handsontable.hide-tween {\n  -webkit-animation: opacity-hide 0.3s;\n  animation: opacity-hide 0.3s;\n  animation-fill-mode: forwards;\n  -webkit-animation-fill-mode: forwards;\n}\n\n.handsontable.show-tween {\n  -webkit-animation: opacity-show 0.3s;\n  animation: opacity-show 0.3s;\n  animation-fill-mode: forwards;\n  -webkit-animation-fill-mode: forwards;\n}\n/*\nAutocompleteRenderer down arrow\n*/\n.handsontable .htAutocompleteArrow {\n  float: right;\n  font-size: 10px;\n  color: #bbbbbb;\n  cursor: default;\n  width: 16px;\n  text-align: center;\n}\n\n[dir=rtl].handsontable .htAutocompleteArrow {\n  float: left;\n}\n\n.handsontable td.htInvalid .htAutocompleteArrow {\n  color: #555555;\n}\n\n.handsontable td.htInvalid .htAutocompleteArrow:hover {\n  color: #1a1a1a;\n}\n\n.handsontable td .htAutocompleteArrow:hover {\n  color: #777;\n}\n\n.handsontable td.area .htAutocompleteArrow {\n  color: #d3d3d3;\n}\n/*\nCheckboxRenderer\n*/\n.handsontable .htCheckboxRendererInput.noValue {\n  opacity: 0.5;\n}\n\n.handsontable .htCheckboxRendererLabel {\n  font-size: inherit;\n  vertical-align: middle;\n  cursor: pointer;\n  display: inline-block;\n}\n\n.handsontable .htCheckboxRendererLabel.fullWidth {\n  width: 100%;\n}\n\n.handsontable .collapsibleIndicator {\n  position: absolute;\n  top: 50%;\n  transform: translate(0%, -50%);\n  left: unset;\n  right: 5px;\n  border: 1px solid #A6A6A6;\n  line-height: 8px;\n  color: #222;\n  border-radius: 10px;\n  font-size: 10px;\n  width: 10px;\n  height: 10px;\n  cursor: pointer;\n  -webkit-box-shadow: 0 0 0 6px rgb(238, 238, 238);\n  -moz-box-shadow: 0 0 0 6px rgb(238, 238, 238);\n  box-shadow: 0 0 0 3px rgb(238, 238, 238);\n  background: #eee;\n  text-align: center;\n}\n\n[dir=rtl].handsontable .collapsibleIndicator {\n  right: unset;\n  left: 5px;\n}\n\n.handsontable[dir=ltr] thead th:has(.collapsibleIndicator) div.htRight span.colHeader {\n  margin-right: 20px;\n}\n\n.handsontable[dir=rtl] thead th:has(.collapsibleIndicator) div.htLeft span.colHeader {\n  margin-left: 20px;\n}\n\n.handsontable .columnSorting {\n  position: relative;\n}\n\n.handsontable[dir=ltr] div.htRight span[class*=ascending],\n.handsontable[dir=ltr] div.htRight span[class*=descending] {\n  margin-right: 10px;\n  margin-left: -10px;\n}\n\n.handsontable[dir=rtl] div.htLeft span[class*=ascending],\n.handsontable[dir=rtl] div.htLeft span[class*=descending] {\n  margin-left: 10px;\n  margin-right: -10px;\n}\n\n.handsontable[dir=ltr] div.htRight span[class*=ascending]:only-child,\n.handsontable[dir=ltr] div.htRight span[class*=descending]:only-child {\n  margin-right: 15px;\n  margin-left: -15px;\n}\n\n.handsontable[dir=rtl] div.htLeft span[class*=ascending]:only-child,\n.handsontable[dir=rtl] div.htLeft span[class*=descending]:only-child {\n  margin-left: 15px;\n  margin-right: -15px;\n}\n\n.handsontable .columnSorting.sortAction:hover {\n  text-decoration: underline;\n  cursor: pointer;\n} /* Arrow position */\n.handsontable span.colHeader.columnSorting::before {\n  /* Centering start */\n  top: 50%;\n  /* One extra pixel for purpose of proper positioning of sorting arrow, when `font-size` set to default */\n  margin-top: -6px;\n  /* Centering end */\n  /* For purpose of continuous mouse over experience, when moving between the `span` and the `::before` elements */\n  padding-left: 8px;\n  padding-right: 0;\n  position: absolute;\n  right: -9px;\n  left: unset;\n  content: \"\";\n  height: 10px;\n  width: 5px;\n  background-size: contain;\n  background-repeat: no-repeat;\n  background-position-x: right;\n}\n\n[dir=rtl].handsontable span.colHeader.columnSorting::before {\n  /* Centering end */\n  /* For purpose of continuous mouse over experience, when moving between the `span` and the `::before` elements */\n  padding-right: 8px;\n  padding-left: 0;\n  left: -9px;\n  right: unset;\n  background-position-x: left;\n}\n\n.handsontable span.colHeader.columnSorting.ascending::before {\n  /* arrow up; 20 x 40 px, scaled to 5 x 10 px; base64 size: 0.3kB */\n  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAoCAMAAADJ7yrpAAAAKlBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKE86IAAAADXRSTlMABBEmRGprlJW72e77tTkTKwAAAFNJREFUeAHtzjkSgCAUBNHPgsoy97+ulGXRqJE5L+xkxoYt2UdsLb5bqFINz+aLuuLn5rIu2RkO3fZpWENimNgiw6iBYRTPMLJjGFxQZ1hxxb/xBI1qC8k39CdKAAAAAElFTkSuQmCC);\n}\n\n.handsontable span.colHeader.columnSorting.descending::before {\n  /* arrow down; 20 x 40 px, scaled to 5 x 10 px; base64 size: 0.3kB */\n  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAoCAMAAADJ7yrpAAAAKlBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKE86IAAAADXRSTlMABBEmRGprlJW72e77tTkTKwAAAFJJREFUeAHtzjkSgCAQRNFmQYUZ7n9dKUvru0TmvPAn3br0QfgdZ5xx6x+rQn23GqTYnq1FDcnuzZIO2WmedVqIRVxgGKEyjNgYRjKGkZ1hFIZ3I70LyM0VtU8AAAAASUVORK5CYII=);\n}\n\n.htGhostTable .htCore span.colHeader.columnSorting:not(.indicatorDisabled)::before {\n  content: \"*\";\n  display: inline-block;\n  position: relative;\n  /* The multi-line header and header with longer text need more padding to not hide arrow,\n  we make header wider in `GhostTable` to make some space for arrow which is positioned absolutely in the main table */\n  padding-right: 20px;\n}\n\n/* Force the ghost table to ignore the additional upper border 1px for the first row in the table */\n.handsontable.htGhostTable table thead th {\n  border-bottom-width: 0;\n}\n\n.handsontable.htGhostTable table tbody tr th,\n.handsontable.htGhostTable table tbody tr td {\n  border-top-width: 0;\n}\n\n.handsontable .htCommentCell {\n  position: relative;\n}\n\n.handsontable .htCommentCell::after {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  right: 0;\n  left: unset;\n  border-left: 6px solid transparent;\n  border-right: none;\n  border-top: 6px solid black;\n}\n\n[dir=rtl].handsontable .htCommentCell::after {\n  left: 0;\n  right: unset;\n  border-right: 6px solid transparent;\n  border-left: none;\n}\n\n.htCommentsContainer .htComments {\n  display: none;\n  z-index: 1059;\n  position: absolute;\n}\n\n.htCommentsContainer .htCommentTextArea {\n  box-shadow: rgba(0, 0, 0, 0.117647) 0 1px 3px, rgba(0, 0, 0, 0.239216) 0 1px 2px;\n  -webkit-box-sizing: border-box;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n  border: none;\n  border-left: 3px solid #ccc;\n  border-right: none;\n  background-color: #fff;\n  width: 215px;\n  height: 90px;\n  font-size: 12px;\n  padding: 5px;\n  outline: 0 !important;\n  -webkit-appearance: none;\n}\n\n[dir=rtl].htCommentsContainer .htCommentTextArea {\n  border-right: 3px solid #ccc;\n  border-left: none;\n}\n\n.htCommentsContainer .htCommentTextArea:focus {\n  box-shadow: rgba(0, 0, 0, 0.117647) 0 1px 3px, rgba(0, 0, 0, 0.239216) 0 1px 2px, inset 0 0 0 1px #5292f7;\n  border-left: 3px solid #5292f7;\n  border-right: none;\n}\n\n[dir=rtl].htCommentsContainer .htCommentTextArea:focus {\n  border-right: 3px solid #5292f7;\n  border-left: none;\n}\n/*!\n * Handsontable ContextMenu\n */\n.htContextMenu:not(.htGhostTable) {\n  display: none;\n  position: absolute;\n  /* needs to be higher than 1050 - z-index for Twitter Bootstrap modal (#1569) */\n  z-index: 1060;\n}\n\n.htContextMenu .ht_clone_top,\n.htContextMenu .ht_clone_bottom,\n.htContextMenu .ht_clone_inline_start,\n.htContextMenu .ht_clone_top_inline_start_corner,\n.htContextMenu .ht_clone_bottom_inline_start_corner {\n  display: none;\n}\n\n.htContextMenu .ht_master table.htCore {\n  border-color: #ccc;\n  border-style: solid;\n  border-top-width: 1px;\n  border-bottom-width: 2px;\n  border-left-width: 1px;\n  border-right-width: 2px;\n}\n\n[dir=rtl].htContextMenu .ht_master table.htCore {\n  border-right-width: 1px;\n  border-left-width: 2px;\n}\n\n.htContextMenu.handsontable:focus {\n  outline: none;\n}\n\n.htContextMenu .wtBorder {\n  visibility: hidden;\n}\n\n.htContextMenu table tbody tr td {\n  background: white;\n  border-width: 0;\n  padding: 4px 6px 0 6px;\n  cursor: pointer;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n\n.htContextMenu table tbody tr td:first-child {\n  border-top-width: 0;\n  border-bottom-width: 0;\n  border-left-width: 0;\n  border-right-width: 0;\n}\n\n[dir=rtl].htContextMenu table tbody tr td:first-child {\n  border-right-width: 0;\n  border-left-width: 0;\n}\n\n.htContextMenu table tbody tr td.htDimmed {\n  font-style: normal;\n  color: #323232;\n}\n\n.htContextMenu table tbody tr td.current {\n  background: #f3f3f3;\n}\n\n.htContextMenu table tbody tr td.htSeparator {\n  border-top: 1px solid #e6e6e6;\n  height: 0;\n  padding: 0;\n  cursor: default;\n}\n\n.htContextMenu table tbody tr td.htDisabled {\n  color: #999;\n  cursor: default;\n}\n\n.htContextMenu table tbody tr td.htDisabled:hover {\n  background: #fff;\n  color: #999;\n  cursor: default;\n}\n\n.htContextMenu table tbody tr.htHidden {\n  display: none;\n}\n\n.htContextMenu table tbody tr td .htItemWrapper {\n  margin-left: 10px;\n  margin-right: 6px;\n}\n\n[dir=rtl].htContextMenu table tbody tr td .htItemWrapper {\n  margin-right: 10px;\n  margin-left: 6px;\n}\n\n.htContextMenu table tbody tr td div span.selected {\n  margin-top: -2px;\n  position: absolute;\n  left: 4px;\n  right: 0;\n}\n\n[dir=rtl].htContextMenu table tbody tr td div span.selected {\n  right: 4px;\n  left: 0;\n}\n\n.htContextMenu .ht_master .wtHolder {\n  overflow: hidden;\n}\n\ntextarea.HandsontableCopyPaste {\n  position: fixed !important;\n  top: 0 !important;\n  right: 100% !important;\n  overflow: hidden;\n  opacity: 0;\n  outline: 0 none !important;\n}\n\n@charset \"UTF-8\";\n\n/*!\n * Handsontable DropdownMenu\n */\n.handsontable .changeType {\n  background: #eee;\n  border-radius: 2px;\n  border: 1px solid #bbb;\n  color: #bbb;\n  font-size: 9px;\n  line-height: 9px;\n  padding: 2px;\n  margin: 3px 1px 0 5px;\n  float: right;\n}\n\n[dir=rtl].handsontable .changeType {\n  float: left;\n}\n\n.handsontable[dir=rtl] .changeType {\n  margin: 3px 5px 0 1px;\n}\n\n.handsontable .changeType::before {\n  content: \"▼ \";\n}\n\n.handsontable .changeType:hover {\n  border: 1px solid #777;\n  color: #777;\n  cursor: pointer;\n}\n\n.htDropdownMenu:not(.htGhostTable) {\n  display: none;\n  position: absolute;\n  /* needs to be higher than 1050 - z-index for Twitter Bootstrap modal (#1569) */\n  z-index: 1060;\n}\n\n.htDropdownMenu .ht_clone_top,\n.htDropdownMenu .ht_clone_bottom,\n.htDropdownMenu .ht_clone_inline_start,\n.htDropdownMenu .ht_clone_top_inline_start_corner,\n.htDropdownMenu .ht_clone_bottom_inline_start_corner {\n  display: none;\n}\n\n.htDropdownMenu table.htCore {\n  border-color: #ccc;\n  border-style: solid;\n  border-top-width: 1px;\n  border-bottom-width: 2px;\n  border-left-width: 1px;\n  border-right-width: 2px;\n}\n\n[dir=rtl].htDropdownMenu table.htCore {\n  border-right-width: 1px;\n  border-left-width: 2px;\n}\n\n.htDropdownMenu.handsontable:focus {\n  outline: none;\n}\n\n.htDropdownMenu .wtBorder {\n  visibility: hidden;\n}\n\n.htDropdownMenu table tbody tr td {\n  background: white;\n  border-width: 0;\n  padding: 4px 6px 0 6px;\n  cursor: pointer;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n\n.htDropdownMenu table tbody tr td:first-child {\n  border-top-width: 0;\n  border-right-width: 0;\n  border-bottom-width: 0;\n  border-left-width: 0;\n}\n\n[dir=rtl].htDropdownMenu table tbody tr td:first-child {\n  border-left-width: 0;\n  border-right-width: 0;\n}\n\n.htDropdownMenu table tbody tr td.htDimmed {\n  font-style: normal;\n  color: #323232;\n}\n\n.htDropdownMenu table tbody tr td.current {\n  background: #e9e9e9;\n}\n\n.htDropdownMenu table tbody tr td.htSeparator {\n  border-top: 1px solid #e6e6e6;\n  height: 0;\n  padding: 0;\n  cursor: default;\n}\n\n.htDropdownMenu table tbody tr td.htDisabled {\n  color: #999;\n}\n\n.htDropdownMenu table tbody tr td.htDisabled:hover {\n  background: #fff;\n  color: #999;\n  cursor: default;\n}\n\n.htDropdownMenu:not(.htGhostTable) table tbody tr.htHidden {\n  display: none;\n}\n\n.htDropdownMenu table tbody tr td .htItemWrapper {\n  margin-left: 10px;\n  margin-right: 10px;\n}\n\n[dir=rtl].htDropdownMenu table tbody tr td .htItemWrapper {\n  margin-right: 10px;\n  margin-left: 10px;\n}\n\n.htDropdownMenu table tbody tr td div span.selected {\n  margin-top: -2px;\n  position: absolute;\n  left: 4px;\n  right: 0;\n}\n\n[dir=rtl].htDropdownMenu table tbody tr td div span.selected {\n  right: 4px;\n  left: 0;\n}\n\n.htDropdownMenu .ht_master .wtHolder {\n  overflow: hidden;\n}\n\n@charset \"UTF-8\";\n\n/*!\n * Handsontable Filters\n */\n/* Conditions menu */\n.htFiltersConditionsMenu:not(.htGhostTable) {\n  display: none;\n  position: absolute;\n  z-index: 1070;\n}\n\n.htFiltersConditionsMenu .ht_clone_top,\n.htFiltersConditionsMenu .ht_clone_bottom,\n.htFiltersConditionsMenu .ht_clone_inline_start,\n.htFiltersConditionsMenu .ht_clone_top_inline_start_corner,\n.htFiltersConditionsMenu .ht_clone_bottom_inline_start_corner {\n  display: none;\n}\n\n.htFiltersConditionsMenu table.htCore {\n  border: 1px solid #bbb;\n  border-bottom-width: 2px;\n  border-right-width: 2px;\n}\n\n.htFiltersConditionsMenu .wtBorder {\n  visibility: hidden;\n}\n\n.htFiltersConditionsMenu table tbody tr td {\n  background: white;\n  border-width: 0;\n  padding: 4px 6px 0 6px;\n  cursor: pointer;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n\n.htFiltersConditionsMenu table tbody tr td:first-child {\n  border-top-width: 0;\n  border-right-width: 0;\n  border-bottom-width: 0;\n  border-left-width: 0;\n}\n\n[dir=rtl].htFiltersConditionsMenu table tbody tr td:first-child {\n  border-left-width: 0;\n  border-right-width: 0;\n}\n\n.htFiltersConditionsMenu table tbody tr td.htDimmed {\n  font-style: normal;\n  color: #323232;\n}\n\n.htFiltersConditionsMenu table tbody tr td.current {\n  background: #e9e9e9;\n}\n\n.htFiltersConditionsMenu table tbody tr td.htSeparator {\n  border-top: 1px solid #e6e6e6;\n  height: 0;\n  padding: 0;\n}\n\n.htFiltersConditionsMenu table tbody tr td.htDisabled {\n  color: #999;\n}\n\n.htFiltersConditionsMenu table tbody tr td.htDisabled:hover {\n  background: #fff;\n  color: #999;\n  cursor: default;\n}\n\n.htFiltersConditionsMenu table tbody tr td .htItemWrapper {\n  margin-left: 10px;\n  margin-right: 10px;\n}\n\n.htFiltersConditionsMenu table tbody tr td div span.selected {\n  margin-top: -2px;\n  position: absolute;\n  left: 4px;\n}\n\n.htFiltersConditionsMenu .ht_master .wtHolder {\n  overflow: hidden;\n}\n\n.handsontable .htMenuFiltering {\n  border-bottom: 1px dotted #ccc;\n  height: 135px;\n  overflow: hidden;\n}\n\n.handsontable .ht_master table td.htCustomMenuRenderer {\n  background-color: #fff;\n  cursor: auto;\n}\n\n/* Menu label */\n.handsontable .htFiltersMenuLabel {\n  font-size: 0.75em;\n}\n\n/* Component action bar */\n.handsontable .htFiltersMenuActionBar {\n  text-align: center;\n  padding-top: 10px;\n  padding-bottom: 3px;\n}\n\n/* Component filter by conditional */\n.handsontable .htFiltersMenuCondition.border {\n  border-bottom: 1px dotted #ccc !important;\n}\n\n.handsontable .htFiltersMenuCondition .htUIInput {\n  padding: 0 0 5px 0;\n}\n\n/* Component filter by value */\n.handsontable .htFiltersMenuValue {\n  border-bottom: 1px dotted #ccc !important;\n}\n\n.handsontable .htFiltersMenuValue .htUIMultipleSelectSearch {\n  padding: 0;\n}\n\n.handsontable .htFiltersMenuCondition .htUIInput input,\n.handsontable .htFiltersMenuValue .htUIMultipleSelectSearch input {\n  font-family: inherit;\n  font-size: 0.75em;\n  padding: 4px;\n  box-sizing: border-box;\n  width: 100%;\n}\n\n.htUIMultipleSelect .ht_master .wtHolder {\n  overflow-y: scroll;\n}\n\n.handsontable .htFiltersActive .changeType {\n  border: 1px solid #509272;\n  color: #18804e;\n  background-color: #d2e0d9;\n}\n\n.handsontable .htUISelectAll {\n  margin-left: 0;\n  margin-right: 10px;\n}\n\n[dir=rtl].handsontable .htUISelectAll {\n  margin-right: 0;\n  margin-left: 10px;\n}\n\n.handsontable .htUIClearAll, .handsontable .htUISelectAll {\n  display: inline-block;\n}\n\n.handsontable .htUIClearAll a, .handsontable .htUISelectAll a {\n  font-size: 0.75em;\n}\n\n.handsontable .htUISelectionControls {\n  text-align: right;\n}\n\n[dir=rtl].handsontable .htUISelectionControls {\n  text-align: left;\n}\n\n.handsontable .htCheckboxRendererInput {\n  display: inline-block;\n  margin-top: 0;\n  margin-right: 5px;\n  margin-bottom: 0;\n  margin-left: 0;\n  vertical-align: middle;\n  height: 1em;\n}\n\n[dir=rtl].handsontable .htCheckboxRendererInput {\n  margin-left: 5px;\n  margin-right: 0;\n}\n\n/* UI elements */\n/* Input */\n.handsontable .htUIInput {\n  padding: 3px 0 7px 0;\n  position: relative;\n  text-align: center;\n}\n\n.handsontable .htUIInput input {\n  border-radius: 2px;\n  border: 1px solid #d2d1d1;\n}\n\n.handsontable .htUIInputIcon {\n  position: absolute;\n}\n\n/* Button */\n.handsontable .htUIInput.htUIButton {\n  cursor: pointer;\n  display: inline-block;\n}\n\n.handsontable .htUIInput.htUIButton input {\n  background-color: #eee;\n  color: #000;\n  cursor: pointer;\n  font-family: inherit;\n  font-size: 0.75em;\n  font-weight: bold;\n  height: 19px;\n  min-width: 64px;\n}\n\n.handsontable .htUIInput.htUIButton input:hover {\n  border-color: #b9b9b9;\n}\n\n.handsontable .htUIInput.htUIButtonOK {\n  margin-left: 0;\n  margin-right: 10px;\n}\n\n[dir=rtl].handsontable .htUIInput.htUIButtonOK {\n  margin-right: 0;\n  margin-left: 10px;\n}\n\n.handsontable .htUIInput.htUIButtonOK input {\n  background-color: #0f9d58;\n  border-color: #18804e;\n  color: #fff;\n}\n\n.handsontable .htUIInput.htUIButtonOK input:focus-visible {\n  background-color: #92dd8d;\n  border-color: #7cb878;\n  color: #000;\n}\n\n.handsontable .htUIInput.htUIButtonOK input:hover {\n  border-color: #1a6f46;\n}\n\n/* Select */\n.handsontable .htUISelect {\n  cursor: pointer;\n  margin-bottom: 7px;\n  position: relative;\n}\n\n.handsontable .htUISelectCaption {\n  background-color: #e8e8e8;\n  border-radius: 2px;\n  border: 1px solid #d2d1d1;\n  font-family: inherit;\n  font-size: 0.75em;\n  font-weight: bold;\n  padding: 3px 20px 3px 10px;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  overflow: hidden;\n}\n\n.handsontable .htUISelectCaption:hover {\n  background-color: #e8e8e8;\n  border: 1px solid #b9b9b9;\n}\n\n.handsontable .htUISelectDropdown::after {\n  content: \"▲\";\n  font-size: 7px;\n  position: absolute;\n  right: 10px;\n  top: 0;\n}\n\n.handsontable .htUISelectDropdown::before {\n  content: \"▼\";\n  font-size: 7px;\n  position: absolute;\n  right: 10px;\n  top: 8px;\n}\n\n/* SelectMultiple */\n.handsontable .htUIMultipleSelect .handsontable .htCore {\n  border: none;\n}\n\n.handsontable .htUIMultipleSelect .handsontable .htCore td:hover {\n  background-color: #F5F5F5;\n}\n\n.handsontable .htUIMultipleSelectSearch input {\n  border-radius: 2px;\n  border: 1px solid #d2d1d1;\n  padding: 3px;\n}\n\n.handsontable .htUIRadio {\n  display: inline-block;\n  margin-left: 0;\n  margin-right: 5px;\n  height: 100%;\n}\n\n[dir=rtl].handsontable .htUIRadio {\n  margin-right: 0;\n  margin-left: 5px;\n}\n\n.handsontable .htUIRadio:last-child {\n  margin-right: 0;\n}\n\n.handsontable .htUIRadio > input[type=radio] {\n  margin-left: 0;\n  margin-right: 0.5ex;\n}\n\n[dir=rtl].handsontable .htUIRadio > input[type=radio] {\n  margin-right: 0;\n  margin-left: 0.5ex;\n}\n\n.handsontable .htUIRadio label {\n  vertical-align: middle;\n}\n\n.handsontable .htFiltersMenuOperators {\n  padding-bottom: 5px;\n}\n\n@charset \"UTF-8\";\n\n/*\n * Handsontable HiddenColumns\n */\n.handsontable th.beforeHiddenColumn {\n  position: relative;\n}\n\n.handsontable th.beforeHiddenColumn::after,\n.handsontable th.afterHiddenColumn::before {\n  color: #bbb;\n  position: absolute;\n  top: 50%;\n  font-size: 5pt;\n  transform: translateY(-50%);\n}\n\n.handsontable th.afterHiddenColumn {\n  position: relative;\n}\n\n.handsontable[dir=ltr] th.afterHiddenColumn div.htLeft {\n  margin-left: 10px;\n}\n\n.handsontable[dir=ltr] th.beforeHiddenColumn div.htRight {\n  margin-right: 10px;\n}\n\n.handsontable[dir=rtl] th.afterHiddenColumn div.htRight {\n  margin-right: 10px;\n}\n\n.handsontable[dir=rtl] th.beforeHiddenColumn div.htLeft {\n  margin-left: 10px;\n}\n\n.handsontable th.beforeHiddenColumn::after {\n  right: 1px;\n  content: \"◀\"; /* left arrow */\n}\n\n[dir=rtl].handsontable th.beforeHiddenColumn::after {\n  right: initial;\n  left: 1px;\n  content: \"▶\"; /* right arrow */\n}\n\n.handsontable th.afterHiddenColumn::before {\n  left: 1px;\n  content: \"▶\"; /* right arrow */\n}\n\n[dir=rtl].handsontable th.afterHiddenColumn::before {\n  right: 1px;\n  left: initial;\n  content: \"◀\"; /* left arrow */\n}\n\n@charset \"UTF-8\";\n\n/*!\n * Handsontable HiddenRows\n */\n.handsontable th.beforeHiddenRow::before,\n.handsontable th.afterHiddenRow::after {\n  color: #bbb;\n  font-size: 6pt;\n  line-height: 6pt;\n  position: absolute;\n  left: 2px;\n}\n\n.handsontable th.beforeHiddenRow,\n.handsontable th.afterHiddenRow {\n  position: relative;\n}\n\n.handsontable th.beforeHiddenRow::before {\n  content: \"▲\";\n  bottom: 2px;\n}\n\n.handsontable th.afterHiddenRow::after {\n  content: \"▼\";\n  top: 2px;\n}\n\n.handsontable.ht__selection--rows tbody th.beforeHiddenRow.ht__highlight::before,\n.handsontable.ht__selection--rows tbody th.afterHiddenRow.ht__highlight::after {\n  color: #eee;\n}\n\n.handsontable td.afterHiddenRow.firstVisibleRow,\n.handsontable th.afterHiddenRow.firstVisibleRow {\n  border-top: 1px solid #CCC;\n}\n\n.htRowHeaders .ht_master.innerBorderInlineStart ~ .ht_clone_top_inline_start_corner th:nth-child(2),\n.htRowHeaders .ht_master.innerBorderInlineStart ~ .ht_clone_inline_start td:first-of-type {\n  border-left: 0 none;\n}\n\n.handsontable.ht__manualColumnMove.after-selection--columns thead th.ht__highlight {\n  cursor: move;\n  cursor: -moz-grab;\n  cursor: -webkit-grab;\n  cursor: grab;\n}\n\n.handsontable.ht__manualColumnMove.on-moving--columns *,\n.handsontable.ht__manualColumnMove.on-moving--columns thead th.ht__highlight {\n  cursor: move;\n  cursor: -moz-grabbing;\n  cursor: -webkit-grabbing;\n  cursor: grabbing;\n}\n\n.handsontable.ht__manualColumnMove.on-moving--columns .manualColumnResizer {\n  display: none;\n}\n\n.handsontable .ht__manualColumnMove--guideline,\n.handsontable .ht__manualColumnMove--backlight {\n  position: absolute;\n  height: 100%;\n  display: none;\n}\n\n.handsontable .ht__manualColumnMove--guideline {\n  background: #757575;\n  width: 2px;\n  top: 0;\n  margin-inline-start: -1px;\n  margin-inline-end: 0;\n  z-index: 205;\n}\n\n.handsontable .ht__manualColumnMove--backlight {\n  background: #343434;\n  background: rgba(52, 52, 52, 0.25);\n  display: none;\n  z-index: 205;\n  pointer-events: none;\n}\n\n.handsontable.on-moving--columns.show-ui .ht__manualColumnMove--guideline,\n.handsontable.on-moving--columns .ht__manualColumnMove--backlight {\n  display: block;\n}\n\n.handsontable.ht__manualRowMove.after-selection--rows tbody th.ht__highlight {\n  cursor: move;\n  cursor: -moz-grab;\n  cursor: -webkit-grab;\n  cursor: grab;\n}\n\n.handsontable.ht__manualRowMove.on-moving--rows *,\n.handsontable.ht__manualRowMove.on-moving--rows tbody th.ht__highlight {\n  cursor: move;\n  cursor: -moz-grabbing;\n  cursor: -webkit-grabbing;\n  cursor: grabbing;\n}\n\n.handsontable.ht__manualRowMove.on-moving--rows .manualRowResizer {\n  display: none;\n}\n\n.handsontable .ht__manualRowMove--guideline,\n.handsontable .ht__manualRowMove--backlight {\n  position: absolute;\n  width: 100%;\n  display: none;\n}\n\n.handsontable .ht__manualRowMove--guideline {\n  background: #757575;\n  height: 2px;\n  left: 0;\n  margin-top: -1px;\n  z-index: 205;\n}\n\n.handsontable .ht__manualRowMove--backlight {\n  background: #343434;\n  background: rgba(52, 52, 52, 0.25);\n  display: none;\n  z-index: 205;\n  pointer-events: none;\n}\n\n.handsontable.on-moving--rows.show-ui .ht__manualRowMove--guideline,\n.handsontable.on-moving--rows .ht__manualRowMove--backlight {\n  display: block;\n}\n\n.handsontable tbody td[rowspan][class*=area][class*=highlight]:not([class*=fullySelectedMergedCell])::before {\n  opacity: 0;\n}\n\n.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-multiple]::before {\n  opacity: 0.1;\n}\n\n.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-0]::before {\n  opacity: 0.1;\n}\n\n.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-1]::before {\n  opacity: 0.2;\n}\n\n.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-2]::before {\n  opacity: 0.27;\n}\n\n.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-3]::before {\n  opacity: 0.35;\n}\n\n.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-4]::before {\n  opacity: 0.41;\n}\n\n.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-5]::before {\n  opacity: 0.47;\n}\n\n.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-6]::before {\n  opacity: 0.54;\n}\n\n.handsontable tbody td[rowspan][class*=area][class*=highlight][class*=fullySelectedMergedCell-7]::before {\n  opacity: 0.58;\n}\n\n.handsontable[dir=ltr] div.htRight span[class*=sort-] {\n  margin-right: 15px;\n  margin-left: -15px;\n}\n\n.handsontable[dir=rtl] div.htLeft span[class*=sort-] {\n  margin-left: 15px;\n  margin-right: -15px;\n}\n\n.handsontable[dir=ltr] div.htRight span[class*=sort-]:only-child {\n  margin-right: 20px;\n  margin-left: -20px;\n}\n\n.handsontable[dir=rtl] div.htLeft span[class*=sort-]:only-child {\n  margin-left: 20px;\n  margin-right: -20px;\n}\n\n/* Column's number position */\n.handsontable span.colHeader.columnSorting::after {\n  /* Centering start */\n  top: 50%;\n  /* Two extra pixels (-2 instead of -4) for purpose of proper positioning of numeric indicators, when `font-size` set to default */\n  margin-top: -2px;\n  /* Centering end */\n  position: absolute;\n  right: -15px;\n  left: unset;\n  /* For purpose of continuous mouse over experience, when moving between the `::before` and the `::after` elements */\n  padding-left: 5px;\n  padding-right: unset;\n  font-size: 8px;\n  height: 8px;\n  line-height: 1.1;\n}\n\n[dir=rtl].handsontable span.colHeader.columnSorting::after {\n  left: -15px;\n  right: unset;\n  /* For purpose of continuous mouse over experience, when moving between the `::before` and the `::after` elements */\n  padding-right: 5px;\n  padding-left: unset;\n}\n\n/* We support up to 7 numeric indicators, describing order of column in sorted columns queue */\n.handsontable span.colHeader.columnSorting[class^=sort-]::after,\n.handsontable span.colHeader.columnSorting[class*=\" sort-\"]::after {\n  content: \"+\";\n}\n\n.handsontable span.colHeader.columnSorting.sort-1::after {\n  content: \"1\";\n}\n\n.handsontable span.colHeader.columnSorting.sort-2::after {\n  content: \"2\";\n}\n\n.handsontable span.colHeader.columnSorting.sort-3::after {\n  content: \"3\";\n}\n\n.handsontable span.colHeader.columnSorting.sort-4::after {\n  content: \"4\";\n}\n\n.handsontable span.colHeader.columnSorting.sort-5::after {\n  content: \"5\";\n}\n\n.handsontable span.colHeader.columnSorting.sort-6::after {\n  content: \"6\";\n}\n\n.handsontable span.colHeader.columnSorting.sort-7::after {\n  content: \"7\";\n}\n\n/* Drop-down menu widens header by 5 pixels, sort sequence numbers won't overlap the icon; mainly for the IE9+ */\n.htGhostTable th div button.changeType + span.colHeader.columnSorting:not(.indicatorDisabled) {\n  padding-right: 5px;\n}\n\n.handsontable thead th.hiddenHeader:not(:first-of-type) {\n  display: none;\n}\n\n@charset \"UTF-8\";\n\n.handsontable th.ht_nestingLevels {\n  text-align: left;\n  padding-left: 7px;\n}\n\n[dir=rtl].handsontable th.ht_nestingLevels {\n  text-align: right;\n  padding-right: 7px;\n}\n\n.handsontable th div.ht_nestingLevels {\n  display: inline-block;\n  position: absolute;\n  left: 11px;\n  right: unset;\n}\n\n[dir=rtl].handsontable th div.ht_nestingLevels {\n  right: 11px;\n  left: unset;\n}\n\n.handsontable.innerBorderInlineStart th div.ht_nestingLevels,\n.handsontable.innerBorderInlineStart ~ .handsontable th div.ht_nestingLevels {\n  right: 10px;\n  left: unset;\n}\n\n[dir=rtl].handsontable.innerBorderInlineStart th div.ht_nestingLevels, [dir=rtl].handsontable.innerBorderInlineStart ~ .handsontable th div.ht_nestingLevels {\n  left: 10px;\n  right: unset;\n}\n\n.handsontable th span.ht_nestingLevel {\n  display: inline-block;\n}\n\n.handsontable th span.ht_nestingLevel_empty {\n  display: inline-block;\n  width: 10px;\n  height: 1px;\n  float: left;\n}\n\n[dir=rtl].handsontable th span.ht_nestingLevel_empty {\n  float: right;\n}\n\n.handsontable th span.ht_nestingLevel::after {\n  content: \"┐\";\n  font-size: 9px;\n  display: inline-block;\n  position: relative;\n  bottom: 3px;\n}\n\n.handsontable th div.ht_nestingButton {\n  display: inline-block;\n  position: absolute;\n  right: -2px;\n  left: unset;\n  cursor: pointer;\n}\n\n[dir=rtl].handsontable th div.ht_nestingButton {\n  left: -2px;\n  right: unset;\n}\n\n.handsontable th div.ht_nestingButton.ht_nestingExpand::after {\n  content: \"+\";\n}\n\n.handsontable th div.ht_nestingButton.ht_nestingCollapse::after {\n  content: \"-\";\n}\n\n.handsontable.innerBorderInlineStart th div.ht_nestingButton,\n.handsontable.innerBorderInlineStart ~ .handsontable th div.ht_nestingButton {\n  right: 0;\n  left: unset;\n}\n\n[dir=rtl].handsontable.innerBorderInlineStart th div.ht_nestingButton, [dir=rtl].handsontable.innerBorderInlineStart ~ .handsontable th div.ht_nestingButton {\n  left: 0;\n  right: unset;\n}\n\n@charset \"UTF-8\";\n\n/*!\n * Pikaday\n * Copyright © 2014 David Bushell | BSD & MIT license | https://dbushell.com/\n */\n\n.pika-single {\n    z-index: 9999;\n    display: block;\n    position: relative;\n    color: #333;\n    background: #fff;\n    border: 1px solid #ccc;\n    border-bottom-color: #bbb;\n    font-family: \"Helvetica Neue\", Helvetica, Arial, sans-serif;\n}\n\n/*\nclear child float (pika-lendar), using the famous micro clearfix hack\nhttp://nicolasgallagher.com/micro-clearfix-hack/\n*/\n.pika-single:before,\n.pika-single:after {\n    content: \" \";\n    display: table;\n}\n.pika-single:after { clear: both }\n\n.pika-single.is-hidden {\n    display: none;\n}\n\n.pika-single.is-bound {\n    position: absolute;\n    box-shadow: 0 5px 15px -5px rgba(0,0,0,.5);\n}\n\n.pika-lendar {\n    float: left;\n    width: 240px;\n    margin: 8px;\n}\n\n.pika-title {\n    position: relative;\n    text-align: center;\n}\n\n.pika-label {\n    display: inline-block;\n    position: relative;\n    z-index: 9999;\n    overflow: hidden;\n    margin: 0;\n    padding: 5px 3px;\n    font-size: 14px;\n    line-height: 20px;\n    font-weight: bold;\n    background-color: #fff;\n}\n.pika-title select {\n    cursor: pointer;\n    position: absolute;\n    z-index: 9998;\n    margin: 0;\n    left: 0;\n    top: 5px;\n    opacity: 0;\n}\n\n.pika-prev,\n.pika-next {\n    display: block;\n    cursor: pointer;\n    position: relative;\n    outline: none;\n    border: 0;\n    padding: 0;\n    width: 20px;\n    height: 30px;\n    /* hide text using text-indent trick, using width value (it's enough) */\n    text-indent: 20px;\n    white-space: nowrap;\n    overflow: hidden;\n    background-color: transparent;\n    background-position: center center;\n    background-repeat: no-repeat;\n    background-size: 75% 75%;\n    opacity: .5;\n}\n\n.pika-prev:hover,\n.pika-next:hover {\n    opacity: 1;\n}\n\n.pika-prev,\n.is-rtl .pika-next {\n    float: left;\n    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAeCAYAAAAsEj5rAAAAUklEQVR42u3VMQoAIBADQf8Pgj+OD9hG2CtONJB2ymQkKe0HbwAP0xucDiQWARITIDEBEnMgMQ8S8+AqBIl6kKgHiXqQqAeJepBo/z38J/U0uAHlaBkBl9I4GwAAAABJRU5ErkJggg==);\n}\n\n.pika-next,\n.is-rtl .pika-prev {\n    float: right;\n    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAeCAYAAAAsEj5rAAAAU0lEQVR42u3VOwoAMAgE0dwfAnNjU26bYkBCFGwfiL9VVWoO+BJ4Gf3gtsEKKoFBNTCoCAYVwaAiGNQGMUHMkjGbgjk2mIONuXo0nC8XnCf1JXgArVIZAQh5TKYAAAAASUVORK5CYII=);\n}\n\n.pika-prev.is-disabled,\n.pika-next.is-disabled {\n    cursor: default;\n    opacity: .2;\n}\n\n.pika-select {\n    display: inline-block;\n}\n\n.pika-table {\n    width: 100%;\n    border-collapse: collapse;\n    border-spacing: 0;\n    border: 0;\n}\n\n.pika-table th,\n.pika-table td {\n    width: 14.285714285714286%;\n    padding: 0;\n}\n\n.pika-table th {\n    color: #999;\n    font-size: 12px;\n    line-height: 25px;\n    font-weight: bold;\n    text-align: center;\n}\n\n.pika-button {\n    cursor: pointer;\n    display: block;\n    box-sizing: border-box;\n    -moz-box-sizing: border-box;\n    outline: none;\n    border: 0;\n    margin: 0;\n    width: 100%;\n    padding: 5px;\n    color: #666;\n    font-size: 12px;\n    line-height: 15px;\n    text-align: center;\n    background: #f5f5f5;\n    height: initial;\n}\n\n.pika-week {\n    font-size: 11px;\n    color: #999;\n}\n\n.is-today .pika-button {\n    color: #33aaff;\n    font-weight: bold;\n}\n\n.is-selected .pika-button,\n.has-event .pika-button {\n    color: #fff;\n    font-weight: bold;\n    background: #33aaff;\n    box-shadow: inset 0 1px 3px #178fe5;\n    border-radius: 3px;\n}\n\n.has-event .pika-button {\n    background: #005da9;\n    box-shadow: inset 0 1px 3px #0076c9;\n}\n\n.is-disabled .pika-button,\n.is-inrange .pika-button {\n    background: #D5E9F7;\n}\n\n.is-startrange .pika-button {\n    color: #fff;\n    background: #6CB31D;\n    box-shadow: none;\n    border-radius: 3px;\n}\n\n.is-endrange .pika-button {\n    color: #fff;\n    background: #33aaff;\n    box-shadow: none;\n    border-radius: 3px;\n}\n\n.is-disabled .pika-button {\n    pointer-events: none;\n    cursor: default;\n    color: #999;\n    opacity: .3;\n}\n\n.is-outside-current-month .pika-button {\n    color: #999;\n    opacity: .3;\n}\n\n.is-selection-disabled {\n    pointer-events: none;\n    cursor: default;\n}\n\n.pika-button:hover,\n.pika-row.pick-whole-week:hover .pika-button {\n    color: #fff;\n    background: #ff8000;\n    box-shadow: none;\n    border-radius: 3px;\n}\n\n/* styling for abbr */\n.pika-table abbr {\n    border-bottom: none;\n    cursor: help;\n}\n\n", ".full-screen-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.85);\n  z-index: 999; /* Ensure it's above everything else */\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  /* Add to document body instead of being contained in parent */\n  position: fixed;\n}\n\n/* :where(.css-dev-only-do-not-override-apn68).ant-drawer {\n    position: fixed;\n    inset: 0;\n    z-index: 9999;\n    pointer-events: none;\n    color: rgba(0, 0, 0, 0.88);\n} */\n.full-screen-content {\n  background-color: #fff;\n  width: 98%;\n  height: 98%;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n.full-screen-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 16px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.full-screen-header h2 {\n  margin: 0;\n  font-size: 18px;\n}\n\n.close-button {\n  background: none;\n  border: none;\n  font-size: 20px;\n  cursor: pointer;\n  color: #999;\n  transition: color 0.2s;\n}\n\n.close-button:hover {\n  color: #333;\n}\n\n.header-buttons {\n  display: flex;\n  gap: 8px; /* space between buttons */\n}\n\n.full-screen-body {\n  flex: 1;\n  overflow: auto;\n  padding: 16px;\n  height: calc(100% - 50px); /* Subtract header height */\n}\n\n/* Style for panel content in full screen */\n.full-screen-panel-content {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n ", ".react-grid-layout {\n  position: relative;\n  transition: height 200ms ease;\n}\n.react-grid-item {\n  transition: all 200ms ease;\n  transition-property: left, top, width, height;\n}\n.react-grid-item img {\n  pointer-events: none;\n  user-select: none;\n}\n.react-grid-item.cssTransforms {\n  transition-property: transform, width, height;\n}\n.react-grid-item.resizing {\n  transition: none;\n  z-index: 1;\n  will-change: width, height;\n}\n\n.react-grid-item.react-draggable-dragging {\n  transition: none;\n  z-index: 3;\n  will-change: transform;\n}\n\n.react-grid-item.dropping {\n  visibility: hidden;\n}\n\n.react-grid-item.react-grid-placeholder {\n  background: red;\n  opacity: 0.2;\n  transition-duration: 100ms;\n  z-index: 2;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  -o-user-select: none;\n  user-select: none;\n}\n\n.react-grid-item.react-grid-placeholder.placeholder-resizing {\n  transition: none;\n}\n\n.react-grid-item > .react-resizable-handle {\n  position: absolute;\n  width: 20px;\n  height: 20px;\n}\n\n.react-grid-item > .react-resizable-handle::after {\n  content: \"\";\n  position: absolute;\n  right: 3px;\n  bottom: 3px;\n  width: 5px;\n  height: 5px;\n  border-right: 2px solid rgba(0, 0, 0, 0.4);\n  border-bottom: 2px solid rgba(0, 0, 0, 0.4);\n}\n\n.react-resizable-hide > .react-resizable-handle {\n  display: none;\n}\n\n.react-grid-item > .react-resizable-handle.react-resizable-handle-sw {\n  bottom: 0;\n  left: 0;\n  cursor: sw-resize;\n  transform: rotate(90deg);\n}\n.react-grid-item > .react-resizable-handle.react-resizable-handle-se {\n  bottom: 0;\n  right: 0;\n  cursor: se-resize;\n}\n.react-grid-item > .react-resizable-handle.react-resizable-handle-nw {\n  top: 0;\n  left: 0;\n  cursor: nw-resize;\n  transform: rotate(180deg);\n}\n.react-grid-item > .react-resizable-handle.react-resizable-handle-ne {\n  top: 0;\n  right: 0;\n  cursor: ne-resize;\n  transform: rotate(270deg);\n}\n.react-grid-item > .react-resizable-handle.react-resizable-handle-w,\n.react-grid-item > .react-resizable-handle.react-resizable-handle-e {\n  top: 50%;\n  margin-top: -10px;\n  cursor: ew-resize;\n}\n.react-grid-item > .react-resizable-handle.react-resizable-handle-w {\n  left: 0;\n  transform: rotate(135deg);\n}\n.react-grid-item > .react-resizable-handle.react-resizable-handle-e {\n  right: 0;\n  transform: rotate(315deg);\n}\n.react-grid-item > .react-resizable-handle.react-resizable-handle-n,\n.react-grid-item > .react-resizable-handle.react-resizable-handle-s {\n  left: 50%;\n  margin-left: -10px;\n  cursor: ns-resize;\n}\n.react-grid-item > .react-resizable-handle.react-resizable-handle-n {\n  top: 0;\n  transform: rotate(225deg);\n}\n.react-grid-item > .react-resizable-handle.react-resizable-handle-s {\n  bottom: 0;\n  transform: rotate(45deg);\n}\n", ".react-resizable {\n  position: relative;\n}\n.react-resizable-handle {\n  position: absolute;\n  width: 20px;\n  height: 20px;\n  background-repeat: no-repeat;\n  background-origin: content-box;\n  box-sizing: border-box;\n  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA2IDYiIHN0eWxlPSJiYWNrZ3JvdW5kLWNvbG9yOiNmZmZmZmYwMCIgeD0iMHB4IiB5PSIwcHgiIHdpZHRoPSI2cHgiIGhlaWdodD0iNnB4Ij48ZyBvcGFjaXR5PSIwLjMwMiI+PHBhdGggZD0iTSA2IDYgTCAwIDYgTCAwIDQuMiBMIDQgNC4yIEwgNC4yIDQuMiBMIDQuMiAwIEwgNiAwIEwgNiA2IEwgNiA2IFoiIGZpbGw9IiMwMDAwMDAiLz48L2c+PC9zdmc+');\n  background-position: bottom right;\n  padding: 0 3px 3px 0;\n}\n.react-resizable-handle-sw {\n  bottom: 0;\n  left: 0;\n  cursor: sw-resize;\n  transform: rotate(90deg);\n}\n.react-resizable-handle-se {\n  bottom: 0;\n  right: 0;\n  cursor: se-resize;\n}\n.react-resizable-handle-nw {\n  top: 0;\n  left: 0;\n  cursor: nw-resize;\n  transform: rotate(180deg);\n}\n.react-resizable-handle-ne {\n  top: 0;\n  right: 0;\n  cursor: ne-resize;\n  transform: rotate(270deg);\n}\n.react-resizable-handle-w,\n.react-resizable-handle-e {\n  top: 50%;\n  margin-top: -10px;\n  cursor: ew-resize;\n}\n.react-resizable-handle-w {\n  left: 0;\n  transform: rotate(135deg);\n}\n.react-resizable-handle-e {\n  right: 0;\n  transform: rotate(315deg);\n}\n.react-resizable-handle-n,\n.react-resizable-handle-s {\n  left: 50%;\n  margin-left: -10px;\n  cursor: ns-resize;\n}\n.react-resizable-handle-n {\n  top: 0;\n  transform: rotate(225deg);\n}\n.react-resizable-handle-s {\n  bottom: 0;\n  transform: rotate(45deg);\n}", "/* PLC Exploration Specific Styles */\n\n/* Batch Comparison Tooltip - High Z-Index for 30% screen coverage */\n.batch-comparison-tooltip {\n  z-index: 9999 !important;\n  position: fixed !important;\n  pointer-events: none;\n}\n\n/* ECharts tooltip override */\n.echarts-tooltip {\n  z-index: 9999 !important;\n}\n\n/* PLC Grid Container */\n.plc-grid-container {\n  position: relative;\n}\n\n/* PLC Grid Layout */\n.plc-layout {\n  position: relative;\n  min-height: 400px;\n}\n\n.plc-grid-item-wrapper {\n  transition: all 0.3s ease;\n}\n\n/* PLC Grid Item */\n.plc-grid-item {\n  background: white;\n  border: 1px solid #e8e8e8;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  transition: all 0.3s ease;\n  overflow: hidden;\n}\n\n.plc-grid-item:hover {\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);\n  border-color: #1890ff;\n}\n\n/* PLC Grid Item Header */\n.plc-grid-item-header {\n  background: linear-gradient(90deg, #f0f2f5 0%, #fafafa 100%);\n  border-bottom: 1px solid #e8e8e8;\n  padding: 12px 16px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  min-height: 48px;\n}\n\n.plc-drag-handle {\n  cursor: move;\n  flex: 1;\n  user-select: none;\n}\n\n.plc-drag-handle:hover {\n  color: #1890ff;\n}\n\n.plc-grid-item-controls {\n  display: flex;\n  gap: 4px;\n  align-items: center;\n}\n\n.plc-grid-item-controls .ant-btn {\n  border: none;\n  box-shadow: none;\n  padding: 4px 8px;\n  height: auto;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.plc-grid-item-controls .ant-btn:hover {\n  background-color: rgba(24, 144, 255, 0.1);\n  color: #1890ff;\n}\n\n/* PLC Grid Item Content */\n.plc-grid-item-content {\n  padding: 16px;\n  height: calc(100% - 48px);\n  overflow: auto;\n}\n\n/* PLC Sidebar Styles */\n.plc-sidebar-collapse .ant-collapse-header {\n  padding: 12px 16px !important;\n  background: #f0f2f5 !important;\n}\n\n.plc-sidebar-collapse .ant-collapse-content-box {\n  padding: 16px !important;\n}\n\n/* Removed unused .plc-panel-item styles - using component-item instead */\n\n/* Drop Indicator - matches batch exploration exactly */\n.drop-indicator {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  padding: 20px;\n  background-color: rgba(255, 255, 255, 0.8);\n  border: 2px dashed #ccc;\n  border-radius: 8px;\n  text-align: center;\n  color: #666;\n  font-size: 16px;\n  z-index: 10;\n}\n\n/* PLC Configuration Drawer */\n.plc-config-drawer {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.config-section-container {\n  border-bottom: 1px solid #e8e8e8;\n  background: white;\n}\n\n.config-section-header {\n  background: #f5f5f5;\n  padding: 16px 24px;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.config-section-header .section-title {\n  margin: 0 !important;\n  font-size: 14px;\n  font-weight: 600;\n  color: #262626;\n}\n\n.config-section-content {\n  padding: 20px 24px;\n}\n\n.date-time-row {\n  display: flex;\n  align-items: flex-end;\n  gap: 8px;\n  margin-bottom: 16px;\n}\n\n.arrow-separator {\n  color: #8c8c8c;\n  font-size: 14px;\n  margin-bottom: 24px;\n  font-weight: 500;\n}\n\n.submit-section {\n  margin-top: 20px;\n  padding-top: 16px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.plc-config-form .ant-form-item {\n  margin-bottom: 16px;\n}\n\n.plc-config-form .ant-form-item-label > label {\n  font-weight: 500;\n  color: #595959;\n  font-size: 12px;\n}\n\n.plc-config-form .ant-select,\n.plc-config-form .ant-picker {\n  border-radius: 4px;\n}\n\n.plc-config-form .ant-btn-primary {\n  background: #1890ff;\n  border-color: #1890ff;\n  border-radius: 4px;\n  font-weight: 500;\n}\n\n/* PLC Time Series Panel Empty State */\n.plc-timeseries-empty {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  text-align: center;\n  padding: 40px 20px;\n}\n\n.plc-timeseries-empty .anticon {\n  margin-bottom: 16px;\n  opacity: 0.6;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .plc-grid-item-header {\n    padding: 8px 12px;\n    min-height: 40px;\n  }\n  \n  .plc-grid-item-content {\n    padding: 12px;\n    height: calc(100% - 40px);\n  }\n  \n  .plc-grid-item-controls .ant-btn {\n    padding: 2px 6px;\n  }\n}\n\n/* Fullscreen Mode Adjustments */\n.plc-fullscreen-content {\n  height: 100vh;\n  width: 100vw;\n  background: white;\n}\n\n.plc-fullscreen-content .plc-grid-item-content {\n  height: calc(100vh - 60px);\n  padding: 20px;\n}\n\n/* Loading States */\n.plc-loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 10;\n}\n\n/* Optimized for performance - minimal animations */\n\n/* Custom scrollbar for PLC components */\n.plc-grid-item-content::-webkit-scrollbar {\n  width: 6px;\n}\n\n.plc-grid-item-content::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n.plc-grid-item-content::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n.plc-grid-item-content::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n/* PLCDataExplorer Styles */\n.plc-data-explorer {\n  position: relative;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.plc-data-explorer.collapsed {\n  height: auto;\n}\n\n/* --- HEADER: NO ANIMATION --- */\n.plc-data-explorer > div:first-child {\n  transition: none !important;\n}\n.plc-data-explorer > div:first-child:hover {\n  background: #f8f9fa !important;\n  box-shadow: none !important;\n}\n.plc-data-explorer > div:first-child:active {\n  transform: none !important;\n  box-shadow: none !important;\n}\n.plc-data-explorer > div:first-child:focus-within {\n  outline: none !important;\n  outline-offset: 0 !important;\n}\n\n/* --- EXPLORER CONTENT: SMOOTH OPEN/CLOSE ANIMATION --- */\n.plc-data-explorer .explorer-content {\n  transition: max-height 0.3s cubic-bezier(0.4,0,0.2,1), opacity 0.3s cubic-bezier(0.4,0,0.2,1), transform 0.3s cubic-bezier(0.4,0,0.2,1);\n  overflow: hidden;\n}\n.plc-data-explorer.collapsed .explorer-content {\n  max-height: 0 !important;\n  opacity: 0;\n  transform: translateY(-10px);\n  pointer-events: none;\n}\n.plc-data-explorer.expanded .explorer-content {\n  max-height: 1200px !important; /* adjust as needed for your content */\n  opacity: 1;\n  transform: translateY(0);\n  pointer-events: auto;\n}\n\n/* PLC Content Layout */\n.plc-content {\n  overflow: hidden;\n}\n\n.plc-grid-section {\n  position: relative;\n  overflow: auto;\n}\n\n.plc-explorer-section {\n  border-top: 1px solid #e8e8e8;\n  background: #fafafa;\n}\n\n/* Explorer Controls Styling */\n.plc-data-explorer .explorer-content .ant-picker {\n  border-radius: 4px;\n}\n\n.plc-data-explorer .explorer-content .ant-select {\n  border-radius: 4px;\n}\n\n.plc-data-explorer .explorer-content .ant-btn {\n  border-radius: 4px;\n  font-weight: 500;\n}\n\n.plc-data-explorer .explorer-content .ant-btn-primary {\n  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);\n}\n\n.plc-data-explorer .explorer-content .ant-btn-primary:hover {\n  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);\n}\n\n/* Explorer Chart Container */\n.plc-data-explorer .explorer-content > div:last-child {\n  position: relative;\n}\n\n.plc-data-explorer .explorer-content .ant-empty {\n  margin: auto;\n}\n\n/* Responsive Design for PLCDataExplorer */\n@media (max-width: 768px) {\n  .plc-content {\n    flex-direction: column;\n  }\n  \n  .plc-grid-section {\n    height: 60% !important;\n    min-height: 300px;\n  }\n  \n  .plc-explorer-section {\n    height: 40% !important;\n    min-height: 200px;\n  }\n  \n  .plc-data-explorer > div:first-child {\n    padding: 8px 12px;\n  }\n  \n  .plc-data-explorer > div:first-child > div:first-child {\n    font-size: 14px;\n  }\n  \n  .plc-data-explorer > div:first-child .ant-btn {\n    font-size: 11px;\n    padding: 2px 8px;\n  }\n  \n  .plc-data-explorer .explorer-content {\n    padding: 12px;\n  }\n  \n  .plc-data-explorer .explorer-content > div:first-child {\n    padding: 12px;\n  }\n}\n\n@media (max-width: 480px) {\n  .plc-grid-section {\n    height: 50% !important;\n    padding: 8px;\n  }\n  \n  .plc-explorer-section {\n    height: 50% !important;\n  }\n  \n  .plc-data-explorer .explorer-content > div:first-child {\n    padding: 8px;\n  }\n  \n  .plc-data-explorer .explorer-content > div:first-child > div {\n    flex-direction: column;\n    gap: 8px;\n  }\n  \n  .plc-data-explorer .explorer-content > div:first-child > div > div {\n    min-width: 100%;\n  }\n}\n\n/* Animation improvements */\n.plc-data-explorer,\n.plc-grid-section,\n.plc-explorer-section {\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n/* Explorer header active state */\n.plc-data-explorer > div:first-child:active {\n  transform: scale(0.98);\n  box-shadow: 0 -2px 8px rgba(24, 144, 255, 0.2);\n}\n\n/* Improved focus states for accessibility */\n.plc-data-explorer > div:first-child:focus-within {\n  outline: 2px solid #40a9ff;\n  outline-offset: 2px;\n}\n\n/* Loading state improvements */\n.plc-data-explorer .ant-spin-container {\n  min-height: 100px;\n}\n\n.plc-data-explorer .ant-spin-spinning {\n  background: rgba(255, 255, 255, 0.8);\n}\n"], "names": [], "sourceRoot": ""}
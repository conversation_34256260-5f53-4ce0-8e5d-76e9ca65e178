#!/usr/bin/env node

/**
 * ES256 JWT Migration Script
 * Helps migrate from HS256 to ES256 JWT implementation
 */

import fs from 'fs';
import path from 'path';
import { generateES256KeyPair, saveKeyPairToFiles, validateKeyPair } from '../src/server/utils/keyGenerator.js';

console.log('🔄 ES256 JWT Migration Script\n');

const ENV_FILE_PATH = './.env';
const KEYS_DIR = './keys';

/**
 * Check if .env file exists
 */
function checkEnvFile() {
  if (!fs.existsSync(ENV_FILE_PATH)) {
    console.log('❌ .env file not found');
    console.log('Please create a .env file in the Backend directory');
    process.exit(1);
  }
  console.log('✅ .env file found');
}

/**
 * Check if ES256 keys already exist
 */
function checkExistingKeys() {
  const privateKeyPath = path.join(KEYS_DIR, 'jwt-private.pem');
  const publicKeyPath = path.join(KEYS_DIR, 'jwt-public.pem');
  
  if (fs.existsSync(privateKeyPath) && fs.existsSync(publicKeyPath)) {
    console.log('⚠️  ES256 keys already exist');
    console.log('   Private key: ' + privateKeyPath);
    console.log('   Public key: ' + publicKeyPath);
    
    // Ask user if they want to regenerate
    const readline = require('readline').createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    return new Promise((resolve) => {
      readline.question('Do you want to regenerate the keys? (y/N): ', (answer) => {
        readline.close();
        resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
      });
    });
  }
  
  return Promise.resolve(true);
}

/**
 * Generate and save ES256 keys
 */
function generateKeys() {
  try {
    console.log('🔑 Generating ES256 key pair...');
    const keyPair = generateES256KeyPair();
    
    console.log('✅ Key pair generated successfully');
    console.log(`   Algorithm: ${keyPair.algorithm}`);
    console.log(`   Curve: ${keyPair.curve}`);
    
    // Validate the key pair
    if (!validateKeyPair(keyPair)) {
      throw new Error('Generated key pair failed validation');
    }
    console.log('✅ Key pair validation passed');
    
    // Save keys to files
    const paths = saveKeyPairToFiles(keyPair, KEYS_DIR);
    
    // Set proper permissions
    fs.chmodSync(paths.privateKeyPath, 0o600); // Private key - owner read only
    fs.chmodSync(paths.publicKeyPath, 0o644);  // Public key - readable by all
    
    console.log('✅ Key permissions set correctly');
    
    return paths;
  } catch (error) {
    console.error('❌ Failed to generate keys:', error.message);
    process.exit(1);
  }
}

/**
 * Update .env file with key paths
 */
function updateEnvFile() {
  try {
    let envContent = fs.readFileSync(ENV_FILE_PATH, 'utf8');
    
    // Check if ES256 keys are already configured
    if (envContent.includes('JWT_PRIVATE_KEY_PATH') && envContent.includes('JWT_PUBLIC_KEY_PATH')) {
      console.log('✅ .env file already contains ES256 key configuration');
      return;
    }
    
    // Add ES256 key configuration
    const keyConfig = `
# ES256 JWT Keys (ECDSA P-256)
JWT_PRIVATE_KEY_PATH=./keys/jwt-private.pem
JWT_PUBLIC_KEY_PATH=./keys/jwt-public.pem`;
    
    // Find JWT_SECRET line and add after it
    if (envContent.includes('JWT_SECRET=')) {
      envContent = envContent.replace(
        /JWT_SECRET=.*/,
        `$&${keyConfig}`
      );
    } else {
      // Add at the end
      envContent += keyConfig;
    }
    
    fs.writeFileSync(ENV_FILE_PATH, envContent);
    console.log('✅ .env file updated with ES256 key paths');
  } catch (error) {
    console.error('❌ Failed to update .env file:', error.message);
    process.exit(1);
  }
}

/**
 * Verify the migration
 */
async function verifyMigration() {
  try {
    console.log('🔍 Verifying migration...');
    
    // Try to load the key pair
    const { loadKeyPairFromEnv } = await import('../src/server/utils/keyGenerator.js');
    const keyPair = loadKeyPairFromEnv();
    
    if (!keyPair.privateKey || !keyPair.publicKey) {
      throw new Error('Key pair not loaded properly');
    }
    
    if (keyPair.algorithm !== 'ES256') {
      throw new Error(`Expected ES256, got ${keyPair.algorithm}`);
    }
    
    if (!validateKeyPair(keyPair)) {
      throw new Error('Key pair validation failed');
    }
    
    console.log('✅ Migration verification passed');
    return true;
  } catch (error) {
    console.error('❌ Migration verification failed:', error.message);
    return false;
  }
}

/**
 * Display post-migration instructions
 */
function displayInstructions() {
  console.log('\n🚀 Migration completed successfully!\n');
  
  console.log('📋 Next Steps:');
  console.log('1. Restart your application server');
  console.log('2. All existing JWT tokens will be invalidated');
  console.log('3. Users will need to re-login');
  console.log('4. Mobile apps may need to handle re-authentication');
  console.log('');
  
  console.log('🔒 Security Notes:');
  console.log('• Private key is stored with 600 permissions (owner read-only)');
  console.log('• Public key is stored with 644 permissions (readable by all)');
  console.log('• Never commit private keys to version control');
  console.log('• Consider implementing key rotation policies');
  console.log('');
  
  console.log('🧪 Testing:');
  console.log('Run the verification test:');
  console.log('  node -e "import(\'./src/server/services/enhancedJWT.js\').then(() => console.log(\'✅ ES256 JWT loaded successfully\'))"');
  console.log('');
  
  console.log('📚 Documentation:');
  console.log('• See Backend/docs/ES256_JWT_UPGRADE.md for detailed information');
  console.log('• Monitor authentication logs for any issues');
}

/**
 * Main migration function
 */
async function migrate() {
  try {
    console.log('Starting ES256 JWT migration...\n');
    
    // Step 1: Check prerequisites
    checkEnvFile();
    
    // Step 2: Check existing keys
    const shouldGenerate = await checkExistingKeys();
    
    if (shouldGenerate) {
      // Step 3: Generate keys
      generateKeys();
    } else {
      console.log('⏭️  Skipping key generation');
    }
    
    // Step 4: Update .env file
    updateEnvFile();
    
    // Step 5: Verify migration
    const verified = await verifyMigration();
    
    if (verified) {
      // Step 6: Display instructions
      displayInstructions();
    } else {
      console.log('❌ Migration failed verification. Please check the logs and try again.');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  }
}

// Run migration if script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  migrate();
}

export { migrate };

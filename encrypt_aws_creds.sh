#!/bin/bash

# Prompt user for input
read -p "Enter tenantId: " tenantId
read -p "Enter AWS Region: " awsRegion
read -p "Enter AWS Bucket Name: " awsBucketName
read -p "Enter AWS Folder: " AWS_FOLDER
read -p "Enter Access Key: " AccessKey
read -p "Enter Secret Key: " SecretKey
read -p "Enter AWS Consolidated PLC: " AWS_CONSOLIDATED_PLC

read -p "Enter Super Admin TOTP token: " token

# API endpoint
API_URL="http://localhost:8080/api/v1/auth/encrypt-aws-creds"

# Build JSON payload
payload=$(cat <<EOF
{
  "tenantId": $tenantId,
  "awsConfig": {
    "AWS_REGION": "$awsRegion",
    "AWS_BUCKET_NAME": "$awsBucketName",
    "AWS_FOLDER": "$AWS_FOLDER",
    "AccessKey": "$AccessKey",
    "SecretKey": "$SecretKey",
    "AWS_CONSOLIDATED_PLC": "$AWS_CONSOLIDATED_PLC"
  },
  "token": $token
}
EOF
)

# Display payload
echo -e "\nPayload being sent:"
echo "$payload"

# Ask for confirmation
read -p "Do you want to send this request? (y/n): " confirm

if [[ "$confirm" =~ ^[Yy]$ ]]; then
  echo -e "\nSending request..."
  
  # Send POST request via curl
  response=$(curl -s -w "\nHTTP_STATUS:%{http_code}\n" -X POST "$API_URL" \
    -H "Content-Type: application/json" \
    -d "$payload")

  # Extract body and status
  body=$(echo "$response" | sed -e 's/HTTP_STATUS\:.*//g')
  status=$(echo "$response" | tr -d '\n' | sed -e 's/.*HTTP_STATUS://')

  # Print response
  echo -e "\nResponse:"
  echo "$body"
  echo "Status Code: $status"
else
  echo -e "\nRequest canceled."
fi